{"IsEncrypted": false, "Values": {"AzureWebJobsStorage": "UseDevelopmentStorage=true", "FUNCTIONS_WORKER_RUNTIME": "dotnet-isolated", "ServiceBusConnection": "Endpoint=sb://dev-eastus-fxqevents.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=y0gZy0BqnXHYBsUmkaS5csmxKyWppP+1h+ASbMMy43Q=", "VehicleAccessQueue": "vehicle-access-creation-local", "UserAccessQueue": "user-access-creation-local", "VehicleSyncQueue": "sync-process-local", "FleetXQApiBaseUrl": "https://localhost:53052", "FleetXQUsername": "Admin", "FleetXQPassword": "Admin"}, "ConnectionStrings": {"ServiceBus": "Endpoint=sb://dev-eastus-fxqevents.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=y0gZy0BqnXHYBsUmkaS5csmxKyWppP+1h+ASbMMy43Q="}, "ServiceBus": {"VehicleAccessQueue": "vehicle-access-creation-local", "UserAccessQueue": "user-access-creation-local", "VehicleSyncQueue": "sync-process-local"}}