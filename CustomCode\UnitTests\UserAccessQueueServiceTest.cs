using FleetXQ.BusinessLayer.Components.Server.Custom;
using FleetXQ.Data.DataObjects;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using NSubstitute;
using NUnit.Framework;
using System;
using System.Text.Json;
using System.Threading.Tasks;

namespace FleetXQ.BusinessLayer.Components.Server.UnitTests
{
    [TestFixture]
    public class UserAccessQueueServiceTest
    {
        private UserAccessQueueService _userAccessQueueService;
        private IConfiguration _mockConfiguration;
        private ILogger<UserAccessQueueService> _mockLogger;

        [SetUp]
        public void Setup()
        {
            // Create substitutes using NSubstitute
            _mockConfiguration = Substitute.For<IConfiguration>();
            _mockLogger = Substitute.For<ILogger<UserAccessQueueService>>();

            // Setup configuration mock to match what UserAccessQueueService expects
            _mockConfiguration.GetConnectionString("ServiceBus")
                .Returns("Endpoint=sb://test.servicebus.windows.net/;SharedAccessKeyName=test;SharedAccessKey=testkey");

            // Mock the configuration section for ServiceBus queue name
            var mockSection = Substitute.For<IConfigurationSection>();
            mockSection.Value.Returns("user-access-update");
            _mockConfiguration.GetSection("ServiceBus:UserAccessQueue").Returns(mockSection);

            // Create the service under test
            _userAccessQueueService = new UserAccessQueueService(_mockConfiguration, _mockLogger);
        }

        [Test]
        public void UserAccessQueueService_Constructor_ShouldInitializeCorrectly()
        {
            // Arrange & Act
            var service = new UserAccessQueueService(_mockConfiguration, _mockLogger);

            // Assert
            Assert.That(service, Is.Not.Null);
        }

        [Test]
        public async Task SendUserAccessUpdateMessageAsync_WithValidMessage_ShouldNotThrowValidationErrors()
        {
            // Arrange
            var testMessage = CreateTestUserAccessUpdateMessage();

            // Act & Assert
            try
            {
                await _userAccessQueueService.SendUserAccessUpdateMessageAsync(testMessage);

                // If we reach here without throwing validation errors, the message structure is valid
                Assert.Pass("Message validation passed");
            }
            catch (ArgumentNullException ex)
            {
                Assert.Fail($"Null argument validation failed: {ex.ParamName}");
            }
            catch (Exception ex)
            {
                // Expected for unit tests since we don't have real Service Bus connection
                // Just verify it's not a validation error
                Assert.That(ex.Message, Does.Not.Contain("ArgumentNull"));
                Console.WriteLine($"Expected exception due to Service Bus connection: {ex.Message}");
            }
        }

        [Test]
        public async Task SendUserAccessUpdateMessageAsync_WithNullMessage_ShouldThrowArgumentNullException()
        {
            // Arrange
            FleetXQ.Data.DataObjects.UserAccessUpdateMessage nullMessage = null;

            // Act & Assert
            try
            {
                await _userAccessQueueService.SendUserAccessUpdateMessageAsync(nullMessage);
                Assert.Fail("Expected ArgumentNullException was not thrown");
            }
            catch (ArgumentNullException ex)
            {
                Assert.That(ex.ParamName, Is.EqualTo("message"));
            }
            catch (Exception ex)
            {
                Assert.Fail($"Expected ArgumentNullException but got {ex.GetType().Name}: {ex.Message}");
            }
        }

        [Test]
        public void UserAccessUpdateMessage_Properties_ShouldSetCorrectly()
        {
            // Arrange
            var personId = Guid.NewGuid();
            var customerId = Guid.NewGuid();
            var userId = Guid.NewGuid();
            var correlationId = Guid.NewGuid().ToString();
            var createdAt = DateTime.UtcNow;

            // Act
            var message = new FleetXQ.Data.DataObjects.UserAccessUpdateMessage
            {
                PersonId = personId,
                CustomerId = customerId,
                PersonToSiteAccessesJson = "[{\"test\":\"data\"}]",
                PersonToDepartmentAccessesJson = "[{\"test\":\"data\"}]",
                PersonToModelAccessesJson = "[{\"test\":\"data\"}]",
                PersonToVehicleAccessesJson = "[{\"test\":\"data\"}]",
                CreatedAt = createdAt,
                InitiatedByUserId = userId,
                CorrelationId = correlationId,
                Priority = "High"
            };

            // Assert
            Assert.That(message.PersonId, Is.EqualTo(personId));
            Assert.That(message.CustomerId, Is.EqualTo(customerId));
            Assert.That(message.PersonToSiteAccessesJson, Is.EqualTo("[{\"test\":\"data\"}]"));
            Assert.That(message.PersonToDepartmentAccessesJson, Is.EqualTo("[{\"test\":\"data\"}]"));
            Assert.That(message.PersonToModelAccessesJson, Is.EqualTo("[{\"test\":\"data\"}]"));
            Assert.That(message.PersonToVehicleAccessesJson, Is.EqualTo("[{\"test\":\"data\"}]"));
            Assert.That(message.CreatedAt, Is.EqualTo(createdAt));
            Assert.That(message.InitiatedByUserId, Is.EqualTo(userId));
            Assert.That(message.CorrelationId, Is.EqualTo(correlationId));
            Assert.That(message.Priority, Is.EqualTo("High"));
        }

        [Test]
        public void UserAccessUpdateMessage_DefaultValues_ShouldBeEmpty()
        {
            // Arrange & Act
            var message = new FleetXQ.Data.DataObjects.UserAccessUpdateMessage();

            // Assert
            Assert.That(message.PersonId, Is.EqualTo(Guid.Empty));
            Assert.That(message.CustomerId, Is.EqualTo(Guid.Empty));
            Assert.That(message.InitiatedByUserId, Is.Null);
            Assert.That(message.PersonToSiteAccessesJson, Is.Null);
            Assert.That(message.PersonToDepartmentAccessesJson, Is.Null);
            Assert.That(message.PersonToModelAccessesJson, Is.Null);
            Assert.That(message.PersonToVehicleAccessesJson, Is.Null);
            Assert.That(message.CorrelationId, Is.Null);
            Assert.That(message.Priority, Is.EqualTo("Normal"));
        }

        [Test]
        public void CreateTestUserAccessUpdateMessage_ShouldCreateValidMessage()
        {
            // Act
            var message = CreateTestUserAccessUpdateMessage();

            // Assert
            Assert.That(message, Is.Not.Null);
            Assert.That(message.PersonId, Is.Not.EqualTo(Guid.Empty));
            Assert.That(message.CustomerId, Is.Not.EqualTo(Guid.Empty));
            Assert.That(message.InitiatedByUserId, Is.Not.Null);
            Assert.That(message.CorrelationId, Is.Not.Null.And.Not.Empty);
            Assert.That(message.Priority, Is.EqualTo("Normal"));
            Assert.That(message.CreatedAt, Is.LessThanOrEqualTo(DateTime.UtcNow));
        }

        [Test]
        public void UserAccessUpdateMessage_JsonSerialization_ShouldWorkCorrectly()
        {
            // Arrange
            var message = CreateTestUserAccessUpdateMessage();

            // Act
            var json = JsonSerializer.Serialize(message);
            var deserializedMessage = JsonSerializer.Deserialize<FleetXQ.Data.DataObjects.UserAccessUpdateMessage>(json);

            // Assert
            Assert.That(deserializedMessage, Is.Not.Null);
            Assert.That(deserializedMessage.PersonId, Is.EqualTo(message.PersonId));
            Assert.That(deserializedMessage.CustomerId, Is.EqualTo(message.CustomerId));
            Assert.That(deserializedMessage.InitiatedByUserId, Is.EqualTo(message.InitiatedByUserId));
            Assert.That(deserializedMessage.CorrelationId, Is.EqualTo(message.CorrelationId));
            Assert.That(deserializedMessage.Priority, Is.EqualTo(message.Priority));
        }

        #region Helper Methods

        private FleetXQ.Data.DataObjects.UserAccessUpdateMessage CreateTestUserAccessUpdateMessage()
        {
            return new FleetXQ.Data.DataObjects.UserAccessUpdateMessage
            {
                PersonId = Guid.NewGuid(),
                CustomerId = Guid.NewGuid(),
                PersonToSiteAccessesJson = "[]",
                PersonToDepartmentAccessesJson = "[]",
                PersonToModelAccessesJson = "[]",
                PersonToVehicleAccessesJson = "[]",
                CreatedAt = DateTime.UtcNow,
                InitiatedByUserId = Guid.NewGuid(),
                CorrelationId = Guid.NewGuid().ToString(),
                Priority = "Normal"
            };
        }

        #endregion
    }
}