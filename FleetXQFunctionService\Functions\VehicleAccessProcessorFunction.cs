using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Logging;
using System.Net;
using Azure.Messaging.ServiceBus;
using System.Threading.Tasks;
using System;
using System.Text.Json;
using System.Collections.Generic;
using System.Diagnostics;
using FleetXQFunctionService.Messages;
using FleetXQFunctionService.Services;

namespace FleetXQFunctionService.Functions
{
    public class VehicleAccessProcessorFunction
    {
        private readonly ILogger _logger;
        private readonly IVehicleAccessCreationService _vehicleAccessCreationService;
        private readonly ISyncProcessService _syncProcessService;
        private readonly IDelayService _delayService;

        public VehicleAccessProcessorFunction(ILoggerFactory loggerFactory, IVehicleAccessCreationService vehicleAccessCreationService, ISyncProcessService syncProcessService, IDelayService delayService)
        {
            _logger = loggerFactory.CreateLogger<VehicleAccessProcessorFunction>();
            _vehicleAccessCreationService = vehicleAccessCreationService;
            _syncProcessService = syncProcessService;
            _delayService = delayService;
        }

        [Function("HealthCheck")]
        public async Task<HttpResponseData> HealthCheck([HttpTrigger(AuthorizationLevel.Anonymous, "get")] HttpRequestData req)
        {
            _logger.LogInformation("Health check function processed a request.");

            var response = req.CreateResponse(HttpStatusCode.OK);
            response.Headers.Add("Content-Type", "application/json; charset=utf-8");

            // Test FleetXQ data layer connection as part of health check
            var fleetXqHealthy = await _vehicleAccessCreationService.TestConnectionAsync();

            var healthStatus = new
            {
                Status = fleetXqHealthy ? "Healthy" : "Unhealthy",
                Timestamp = DateTime.UtcNow,
                FleetXQDataLayer = fleetXqHealthy ? "Connected" : "Failed"
            };

            await response.WriteStringAsync(JsonSerializer.Serialize(healthStatus));

            if (!fleetXqHealthy)
            {
                response.StatusCode = HttpStatusCode.ServiceUnavailable;
            }

            return response;
        }

        [Function("UserAccessProcessor")]
        public async Task ProcessUserAccessUpdateMessage(
            [ServiceBusTrigger(
                queueName: "%UserAccessQueue%",
                Connection = "ServiceBusConnection")] ServiceBusReceivedMessage message,
            ServiceBusMessageActions messageActions)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            UserAccessUpdateMessage? userAccessMessage = null;

            try
            {
                _logger.LogInformation("Processing user access update message: {MessageId}", message.MessageId);

                // Deserialize the message
                var messageBody = message.Body.ToString();

                try
                {
                    userAccessMessage = JsonSerializer.Deserialize<UserAccessUpdateMessage>(messageBody);
                }
                catch (JsonException jsonEx)
                {
                    _logger.LogError(jsonEx, "Failed to deserialize user access update message: {MessageId}", message.MessageId);
                    await messageActions.DeadLetterMessageAsync(message, new Dictionary<string, object>
                    {
                        ["DeadLetterReason"] = "InvalidMessageFormat",
                        ["DeadLetterErrorDescription"] = $"JSON deserialization failed: {jsonEx.Message}"
                    });
                    return;
                }

                if (userAccessMessage == null)
                {
                    _logger.LogError("Deserialized user access message is null: {MessageId}", message.MessageId);
                    await messageActions.DeadLetterMessageAsync(message, new Dictionary<string, object>
                    {
                        ["DeadLetterReason"] = "InvalidMessageFormat",
                        ["DeadLetterErrorDescription"] = "Deserialized message is null"
                    });
                    return;
                }

                _logger.LogInformation("Processing user access update for person {PersonId} from message {MessageId}, CorrelationId: {CorrelationId}",
                    userAccessMessage.PersonId, message.MessageId, userAccessMessage.CorrelationId);

                // TODO: Enable when Service Bus queue has sessions enabled
                // Check for duplicate processing using Person ID to prevent race conditions
                // This prevents multiple instances from processing the same person simultaneously
                /*
                if (await IsPersonCurrentlyProcessingAsync(userAccessMessage.PersonId))
                {
                    _logger.LogWarning("Person {PersonId} is already being processed by another instance. Deferring message {MessageId} for 30 seconds.",
                        userAccessMessage.PersonId, message.MessageId);

                    // Defer the message for 30 seconds to allow the other instance to complete
                    await messageActions.DeferMessageAsync(message, new Dictionary<string, object>
                    {
                        ["DeferReason"] = "PersonAlreadyProcessing",
                        ["DeferredAt"] = DateTime.UtcNow.ToString("O"),
                        ["RetryAfterSeconds"] = 30
                    });
                    return;
                }

                // Mark person as being processed
                await MarkPersonAsProcessingAsync(userAccessMessage.PersonId, message.MessageId);
                */

                bool success = false;
                try
                {
                    // Process using FleetXQ VehicleAccessCreationService ManageUserAccessAsync method
                    success = await _vehicleAccessCreationService.ProcessUserAccessUpdateAsync(userAccessMessage);
                }
                finally
                {
                    // TODO: Enable when sessions are available
                    // Always release the processing lock
                    // await ReleasePersonProcessingLockAsync(userAccessMessage.PersonId);
                }

                if (!success)
                {
                    _logger.LogError("FleetXQ processing failed for person {PersonId}, delivery count: {DeliveryCount}",
                        userAccessMessage.PersonId, message.DeliveryCount);

                    // Use Service Bus DeliveryCount for retry logic
                    var maxDeliveryAttempts = 6; // 1 initial + 5 retries

                    if (message.DeliveryCount < maxDeliveryAttempts)
                    {
                        var retryDelayTimeSpan = CalculateRetryDelay(message.DeliveryCount - 1);

                        _logger.LogWarning("Retrying for person {PersonId} - delivery attempt {DeliveryCount}/{MaxAttempts}, waiting {DelaySeconds}s before retry",
                            userAccessMessage.PersonId, message.DeliveryCount, maxDeliveryAttempts, retryDelayTimeSpan.TotalSeconds);

                        // Wait for the calculated delay before abandoning the message
                        await _delayService.DelayAsync(retryDelayTimeSpan);

                        _logger.LogInformation("Abandoning message for person {PersonId} after {DelaySeconds}s delay",
                            userAccessMessage.PersonId, retryDelayTimeSpan.TotalSeconds);

                        await messageActions.AbandonMessageAsync(message, new Dictionary<string, object>
                        {
                            ["DeliveryAttempt"] = message.DeliveryCount,
                            ["RetryAfterDelaySeconds"] = retryDelayTimeSpan.TotalSeconds,
                            ["ErrorDescription"] = "Failed to process user access update in FleetXQ system"
                        });
                    }
                    else
                    {
                        _logger.LogError("Max delivery attempts reached for person {PersonId}, moving to dead letter queue", userAccessMessage.PersonId);
                        await messageActions.DeadLetterMessageAsync(message, new Dictionary<string, object>
                        {
                            ["DeadLetterReason"] = "ProcessingFailed",
                            ["DeadLetterErrorDescription"] = "Failed to process user access update after maximum retry attempts",
                            ["FinalDeliveryCount"] = message.DeliveryCount,
                            ["MaxDeliveryAttempts"] = maxDeliveryAttempts
                        });
                    }
                }
                else
                {
                    _logger.LogInformation("Successfully processed user access update for person {PersonId} in {ElapsedMs}ms",
                        userAccessMessage.PersonId, stopwatch.ElapsedMilliseconds);
                    await messageActions.CompleteMessageAsync(message);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing user access update message {MessageId} for person {PersonId}: {ErrorMessage}",
                    message.MessageId, userAccessMessage?.PersonId, ex.Message);

                try
                {
                    // Check if this is a race condition error that can be retried
                    if (IsRaceConditionRelatedError(ex))
                    {
                        _logger.LogWarning("Race condition detected in user access processing for person {PersonId}. Error: {Message}",
                            userAccessMessage?.PersonId, ex.Message);

                        // Use Service Bus DeliveryCount for retry logic in race condition scenarios
                        var maxDeliveryAttempts = 6; // 1 initial + 5 retries

                        if (message.DeliveryCount < maxDeliveryAttempts)
                        {
                            var retryDelayTimeSpan = CalculateRetryDelay(message.DeliveryCount - 1);

                            _logger.LogWarning("Race condition detected, retrying for person {PersonId} - delivery attempt {DeliveryCount}/{MaxAttempts}, waiting {DelaySeconds}s before retry",
                                userAccessMessage?.PersonId, message.DeliveryCount, maxDeliveryAttempts, retryDelayTimeSpan.TotalSeconds);

                            // Add jitter specifically for race conditions to spread out retries
                            var raceConditionJitter = TimeSpan.FromSeconds(new Random().Next(5, 15));
                            var totalDelay = retryDelayTimeSpan.Add(raceConditionJitter);

                            await _delayService.DelayAsync(totalDelay);

                            _logger.LogInformation("Abandoning message for person {PersonId} after {DelaySeconds}s delay due to race condition",
                                userAccessMessage?.PersonId, totalDelay.TotalSeconds);

                            await messageActions.AbandonMessageAsync(message, new Dictionary<string, object>
                            {
                                ["DeliveryAttempt"] = message.DeliveryCount,
                                ["RetryAfterDelaySeconds"] = totalDelay.TotalSeconds,
                                ["ErrorDescription"] = $"Race condition detected: {ex.Message}",
                                ["RaceConditionError"] = true
                            });
                            return;
                        }
                    }

                    // Dead letter the message for unexpected exceptions or max retries reached
                    await messageActions.DeadLetterMessageAsync(message, new Dictionary<string, object>
                    {
                        ["DeadLetterReason"] = "ProcessingFailed",
                        ["DeadLetterErrorDescription"] = ex.Message,
                        ["IsRaceConditionError"] = IsRaceConditionRelatedError(ex)
                    });
                }
                catch (Exception innerEx)
                {
                    _logger.LogError(innerEx, "Failed to handle user access message error for person {PersonId}", userAccessMessage?.PersonId);
                }
            }
            finally
            {
                stopwatch.Stop();
            }
        }

        [Function("VehicleAccessProcessor")]
        public async Task ProcessVehicleAccessCreationMessage(
            [ServiceBusTrigger(queueName: "%VehicleAccessQueue%", Connection = "ServiceBusConnection")] ServiceBusReceivedMessage message,
            ServiceBusMessageActions messageActions)
        {
            var stopwatch = Stopwatch.StartNew();
            VehicleAccessCreationMessage vehicleAccessMessage = null;

            try
            {
                _logger.LogInformation("Processing vehicle access creation message {MessageId}, DeliveryCount: {DeliveryCount}",
                    message.MessageId, message.DeliveryCount);

                // Deserialize message
                var messageBody = message.Body.ToString();
                vehicleAccessMessage = JsonSerializer.Deserialize<VehicleAccessCreationMessage>(messageBody);

                _logger.LogInformation("Processing vehicle access creation for vehicle {VehicleId} from message {MessageId}",
                    vehicleAccessMessage.VehicleId, message.MessageId);

                // TODO: Re-enable when Service Bus queue has sessions enabled
                // Check for duplicate processing using Session ID (which is set to VehicleId)
                // This prevents multiple instances from processing the same vehicle simultaneously
                /*
                if (await IsVehicleCurrentlyProcessingAsync(vehicleAccessMessage.VehicleId))
                {
                    _logger.LogWarning("Vehicle {VehicleId} is already being processed by another instance. Deferring message {MessageId} for 30 seconds.",
                        vehicleAccessMessage.VehicleId, message.MessageId);

                    // Defer the message for 30 seconds to allow the other instance to complete
                    await messageActions.DeferMessageAsync(message, new Dictionary<string, object>
                    {
                        ["DeferReason"] = "VehicleAlreadyProcessing",
                        ["DeferredAt"] = DateTime.UtcNow.ToString("O"),
                        ["RetryAfterSeconds"] = 30
                    });
                    return;
                }

                // Mark vehicle as being processed
                await MarkVehicleAsProcessingAsync(vehicleAccessMessage.VehicleId, message.MessageId);
                */

                try
                {
                    // Process using FleetXQ VehicleAccessCreationService
                    var success = await _vehicleAccessCreationService.ProcessVehicleAccessCreationAsync(vehicleAccessMessage);

                    if (!success)
                    {
                        _logger.LogError("FleetXQ processing failed for vehicle {VehicleId}, delivery count: {DeliveryCount}",
                            vehicleAccessMessage.VehicleId, message.DeliveryCount);

                        // Use Service Bus DeliveryCount for retry logic instead of message content
                        var maxDeliveryAttempts = 6; // 1 initial + 5 retries

                        if (message.DeliveryCount < maxDeliveryAttempts)
                        {
                            var retryDelayTimeSpan = CalculateRetryDelay(message.DeliveryCount - 1);

                            _logger.LogWarning("Retrying for vehicle {VehicleId} - delivery attempt {DeliveryCount}/{MaxAttempts}, will retry after {DelaySeconds}s",
                                vehicleAccessMessage.VehicleId, message.DeliveryCount, maxDeliveryAttempts, retryDelayTimeSpan.TotalSeconds);

                            // TODO: Re-enable when sessions are available
                            // Release the processing lock before deferring
                            // await ReleaseVehicleProcessingLockAsync(vehicleAccessMessage.VehicleId);

                            // Actually wait for the calculated delay before abandoning the message
                            // This ensures proper exponential backoff timing
                            await _delayService.DelayAsync(retryDelayTimeSpan);

                            _logger.LogInformation("Abandoning message for vehicle {VehicleId} after {DelaySeconds}s delay",
                                vehicleAccessMessage.VehicleId, retryDelayTimeSpan.TotalSeconds);

                            await messageActions.AbandonMessageAsync(message, new Dictionary<string, object>
                            {
                                ["DeliveryAttempt"] = message.DeliveryCount,
                                ["RetryAfterDelaySeconds"] = retryDelayTimeSpan.TotalSeconds,
                                ["ErrorDescription"] = "Failed to process vehicle access in FleetXQ system - vehicle may not be ready yet"
                            });
                        }
                        else
                        {
                            await messageActions.DeadLetterMessageAsync(message, new Dictionary<string, object>
                            {
                                ["DeadLetterReason"] = "FleetXQProcessingFailed",
                                ["DeadLetterErrorDescription"] = "Failed to process vehicle access in FleetXQ system after maximum delivery attempts",
                                ["FinalDeliveryCount"] = message.DeliveryCount,
                                ["MaxDeliveryAttempts"] = maxDeliveryAttempts
                            });
                        }
                    }
                    else
                    {
                        _logger.LogInformation("Successfully processed vehicle access creation for vehicle {VehicleId}", vehicleAccessMessage.VehicleId);
                        await messageActions.CompleteMessageAsync(message);
                    }
                }
                finally
                {
                    // TODO: Re-enable when sessions are available
                    // Always release the processing lock
                    // await ReleaseVehicleProcessingLockAsync(vehicleAccessMessage.VehicleId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing vehicle access creation message {MessageId} for vehicle {VehicleId}: {ErrorMessage}",
                    message.MessageId, vehicleAccessMessage?.VehicleId, ex.Message);

                try
                {
                    // TODO: Re-enable when sessions are available
                    // Release processing lock if we have vehicle ID
                    /*
                    if (vehicleAccessMessage?.VehicleId != null)
                    {
                        await ReleaseVehicleProcessingLockAsync(vehicleAccessMessage.VehicleId);
                    }
                    */

                    // Use Service Bus DeliveryCount for retry logic in exception scenarios too
                    var maxDeliveryAttempts = 6; // 1 initial + 5 retries

                    if (message.DeliveryCount < maxDeliveryAttempts)
                    {
                        var retryDelayTimeSpan = CalculateRetryDelay(message.DeliveryCount - 1);

                        _logger.LogWarning("Exception occurred, retrying for vehicle {VehicleId} - delivery attempt {DeliveryCount}/{MaxAttempts}, waiting {DelaySeconds}s before retry",
                            vehicleAccessMessage?.VehicleId, message.DeliveryCount, maxDeliveryAttempts, retryDelayTimeSpan.TotalSeconds);

                        // Actually wait for the calculated delay before abandoning the message
                        await _delayService.DelayAsync(retryDelayTimeSpan);

                        _logger.LogInformation("Abandoning message for vehicle {VehicleId} after {DelaySeconds}s delay due to exception",
                            vehicleAccessMessage?.VehicleId, retryDelayTimeSpan.TotalSeconds);

                        await messageActions.AbandonMessageAsync(message, new Dictionary<string, object>
                        {
                            ["DeliveryAttempt"] = message.DeliveryCount,
                            ["RetryAfterDelaySeconds"] = retryDelayTimeSpan.TotalSeconds,
                            ["ErrorDescription"] = ex.Message
                        });
                    }
                    else
                    {
                        // Dead letter the message after max delivery attempts
                        await messageActions.DeadLetterMessageAsync(message, new Dictionary<string, object>
                        {
                            ["DeadLetterReason"] = "VehicleAccessProcessingFailed",
                            ["DeadLetterErrorDescription"] = ex.Message,
                            ["FinalDeliveryCount"] = message.DeliveryCount,
                            ["MaxDeliveryAttempts"] = maxDeliveryAttempts
                        });
                    }
                }
                catch (Exception innerEx)
                {
                    _logger.LogError(innerEx, "Failed to handle vehicle access message error for vehicle {VehicleId}", vehicleAccessMessage?.VehicleId);
                }
            }
            finally
            {
                stopwatch.Stop();
                _logger.LogInformation("Vehicle access processing completed for message {MessageId} in {ElapsedMs}ms",
                    message.MessageId, stopwatch.ElapsedMilliseconds);
            }
        }

        [Function("VehicleSyncProcessor")]
        public async Task ProcessVehicleSyncMessage(
            [ServiceBusTrigger(
                queueName: "%VehicleSyncQueue%",
                Connection = "ServiceBusConnection")] ServiceBusReceivedMessage message,
            ServiceBusMessageActions messageActions)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            VehicleSyncMessage? vehicleSyncMessage = null;

            try
            {
                _logger.LogInformation("Processing vehicle sync message: {MessageId}", message.MessageId);

                // Deserialize the message
                var messageBody = message.Body.ToString();

                try
                {
                    vehicleSyncMessage = JsonSerializer.Deserialize<VehicleSyncMessage>(messageBody);
                }
                catch (JsonException jsonEx)
                {
                    _logger.LogError(jsonEx, "Failed to deserialize vehicle sync message: {MessageId}", message.MessageId);
                    await messageActions.DeadLetterMessageAsync(message, new Dictionary<string, object>
                    {
                        ["DeadLetterReason"] = "InvalidMessageFormat",
                        ["DeadLetterErrorDescription"] = $"JSON deserialization failed: {jsonEx.Message}"
                    });
                    return;
                }

                if (vehicleSyncMessage == null)
                {
                    _logger.LogError("Deserialized vehicle sync message is null: {MessageId}", message.MessageId);
                    await messageActions.DeadLetterMessageAsync(message, new Dictionary<string, object>
                    {
                        ["DeadLetterReason"] = "InvalidMessageFormat",
                        ["DeadLetterErrorDescription"] = "Deserialized message is null"
                    });
                    return;
                }

                _logger.LogInformation("Processing vehicle sync for vehicle {VehicleId} ({Sequence}/{Total}) from message {MessageId}, CorrelationId: {CorrelationId}, Reason: {SyncReason}",
                    vehicleSyncMessage.VehicleId, vehicleSyncMessage.VehicleSequence, vehicleSyncMessage.TotalVehicles,
                    message.MessageId, vehicleSyncMessage.CorrelationId, vehicleSyncMessage.SyncReason);

                // Process using SyncProcessService
                var success = await _syncProcessService.ProcessVehicleSyncAsync(messageBody);

                if (!success)
                {
                    _logger.LogError("Vehicle sync processing failed for vehicle {VehicleId} ({Sequence}/{Total}), delivery count: {DeliveryCount}",
                        vehicleSyncMessage.VehicleId, vehicleSyncMessage.VehicleSequence, vehicleSyncMessage.TotalVehicles, message.DeliveryCount);

                    // Use Service Bus DeliveryCount for retry logic
                    var maxDeliveryAttempts = 5; // Sync operations can have fewer retries since they're less critical

                    if (message.DeliveryCount < maxDeliveryAttempts)
                    {
                        var retryDelayTimeSpan = CalculateRetryDelay(message.DeliveryCount - 1);

                        _logger.LogWarning("Retrying vehicle sync for vehicle {VehicleId} ({Sequence}/{Total}) - delivery attempt {DeliveryCount}/{MaxAttempts}, waiting {DelaySeconds}s before retry",
                            vehicleSyncMessage.VehicleId, vehicleSyncMessage.VehicleSequence, vehicleSyncMessage.TotalVehicles,
                            message.DeliveryCount, maxDeliveryAttempts, retryDelayTimeSpan.TotalSeconds);

                        // Wait for the calculated delay before abandoning the message
                        await _delayService.DelayAsync(retryDelayTimeSpan);

                        _logger.LogInformation("Abandoning vehicle sync message for vehicle {VehicleId} after {DelaySeconds}s delay",
                            vehicleSyncMessage.VehicleId, retryDelayTimeSpan.TotalSeconds);

                        await messageActions.AbandonMessageAsync(message, new Dictionary<string, object>
                        {
                            ["DeliveryAttempt"] = message.DeliveryCount,
                            ["RetryAfterDelaySeconds"] = retryDelayTimeSpan.TotalSeconds,
                            ["ErrorDescription"] = "Failed to process vehicle sync in FleetXQ system"
                        });
                    }
                    else
                    {
                        _logger.LogError("Max delivery attempts reached for vehicle sync {VehicleId}, moving to dead letter queue", vehicleSyncMessage.VehicleId);
                        await messageActions.DeadLetterMessageAsync(message, new Dictionary<string, object>
                        {
                            ["DeadLetterReason"] = "VehicleSyncProcessingFailed",
                            ["DeadLetterErrorDescription"] = "Failed to process vehicle sync after maximum retry attempts",
                            ["FinalDeliveryCount"] = message.DeliveryCount,
                            ["MaxDeliveryAttempts"] = maxDeliveryAttempts,
                            ["VehicleId"] = vehicleSyncMessage.VehicleId.ToString(),
                            ["SyncReason"] = vehicleSyncMessage.SyncReason
                        });
                    }
                }
                else
                {
                    _logger.LogInformation("Successfully processed vehicle sync for vehicle {VehicleId} ({Sequence}/{Total}) - {SyncReason} in {ElapsedMs}ms",
                        vehicleSyncMessage.VehicleId, vehicleSyncMessage.VehicleSequence, vehicleSyncMessage.TotalVehicles,
                        vehicleSyncMessage.SyncReason, stopwatch.ElapsedMilliseconds);
                    await messageActions.CompleteMessageAsync(message);
                }
            }
            catch (Azure.Messaging.ServiceBus.ServiceBusException sbEx) when (sbEx.Reason == Azure.Messaging.ServiceBus.ServiceBusFailureReason.MessageLockLost)
            {
                _logger.LogWarning("Message lock lost for vehicle sync {MessageId} and vehicle {VehicleId} - message will be retried automatically",
                    message.MessageId, vehicleSyncMessage?.VehicleId);
                // Don't attempt any message settlement operations when lock is lost
                return;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing vehicle sync message {MessageId} for vehicle {VehicleId}: {ErrorMessage}",
                    message.MessageId, vehicleSyncMessage?.VehicleId, ex.Message);

                try
                {
                    // Use Service Bus DeliveryCount for retry logic in exception scenarios too
                    var maxDeliveryAttempts = 5;

                    if (message.DeliveryCount < maxDeliveryAttempts)
                    {
                        var retryDelayTimeSpan = CalculateRetryDelay(message.DeliveryCount - 1);

                        _logger.LogWarning("Exception occurred, retrying vehicle sync for vehicle {VehicleId} - delivery attempt {DeliveryCount}/{MaxAttempts}, waiting {DelaySeconds}s before retry",
                            vehicleSyncMessage?.VehicleId, message.DeliveryCount, maxDeliveryAttempts, retryDelayTimeSpan.TotalSeconds);

                        await _delayService.DelayAsync(retryDelayTimeSpan);

                        _logger.LogInformation("Abandoning vehicle sync message for vehicle {VehicleId} after {DelaySeconds}s delay due to exception",
                            vehicleSyncMessage?.VehicleId, retryDelayTimeSpan.TotalSeconds);

                        await messageActions.AbandonMessageAsync(message, new Dictionary<string, object>
                        {
                            ["DeliveryAttempt"] = message.DeliveryCount,
                            ["RetryAfterDelaySeconds"] = retryDelayTimeSpan.TotalSeconds,
                            ["ErrorDescription"] = ex.Message
                        });
                    }
                    else
                    {
                        // Dead letter the message after max delivery attempts
                        await messageActions.DeadLetterMessageAsync(message, new Dictionary<string, object>
                        {
                            ["DeadLetterReason"] = "VehicleSyncProcessingFailed",
                            ["DeadLetterErrorDescription"] = ex.Message,
                            ["FinalDeliveryCount"] = message.DeliveryCount,
                            ["MaxDeliveryAttempts"] = maxDeliveryAttempts
                        });
                    }
                }
                catch (Azure.Messaging.ServiceBus.ServiceBusException sbEx) when (sbEx.Reason == Azure.Messaging.ServiceBus.ServiceBusFailureReason.MessageLockLost)
                {
                    _logger.LogWarning("Message lock lost for vehicle sync {VehicleId} - message will be retried automatically", vehicleSyncMessage?.VehicleId);
                    // Don't attempt to settle the message when lock is lost - Service Bus will automatically retry
                }
                catch (Exception innerEx)
                {
                    _logger.LogError(innerEx, "Failed to handle vehicle sync message error for vehicle {VehicleId}", vehicleSyncMessage?.VehicleId);
                }
            }
            finally
            {
                stopwatch.Stop();
            }
        }

        private TimeSpan CalculateRetryDelay(int retryCount)
        {
            // Base delay starts at 5 seconds
            var baseDelaySeconds = 5;

            // Exponential backoff: 5s, 10s, 20s, 40s, 80s, 160s, 320s (capped at 5 minutes)
            var exponentialDelaySeconds = Math.Pow(2, retryCount) * baseDelaySeconds;

            // Cap at 5 minutes (300 seconds) to prevent extremely long delays
            var cappedDelaySeconds = Math.Min(exponentialDelaySeconds, 300);

            // Add some jitter to prevent thundering herd effect
            var jitter = new Random().Next(0, 10); // 0-10 second jitter
            var finalDelaySeconds = cappedDelaySeconds + jitter;

            return TimeSpan.FromSeconds(finalDelaySeconds);
        }

        // Add these new methods for session-based locking

        /// <summary>
        /// Check if a vehicle is currently being processed by another instance
        /// </summary>
        private async Task<bool> IsVehicleCurrentlyProcessingAsync(Guid vehicleId)
        {
            // Implementation depends on your caching/storage solution
            // This could use Redis, Azure Cache, or even Azure Table Storage

            // For now, return false to avoid breaking changes
            // TODO: Implement proper distributed locking mechanism
            return false;
        }

        /// <summary>
        /// Mark a vehicle as being processed with a TTL to prevent deadlocks
        /// </summary>
        private async Task MarkVehicleAsProcessingAsync(Guid vehicleId, string messageId)
        {
            try
            {
                // Implementation depends on your distributed cache solution
                // This should set a distributed lock with TTL (e.g., 10 minutes)

                _logger.LogDebug("Marked vehicle {VehicleId} as processing by message {MessageId}", vehicleId, messageId);

                // TODO: Implement proper distributed locking mechanism
                // Example using Redis: await cache.StringSetAsync($"vehicle_processing:{vehicleId}", messageId, TimeSpan.FromMinutes(10));
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to mark vehicle {VehicleId} as processing. Continuing without lock.", vehicleId);
            }
        }

        /// <summary>
        /// Release the processing lock for a vehicle
        /// </summary>
        private async Task ReleaseVehicleProcessingLockAsync(Guid vehicleId)
        {
            try
            {
                // Implementation depends on your distributed cache solution
                // This should remove the distributed lock

                _logger.LogDebug("Released processing lock for vehicle {VehicleId}", vehicleId);

                // TODO: Implement proper distributed locking mechanism  
                // Example using Redis: await cache.KeyDeleteAsync($"vehicle_processing:{vehicleId}");
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to release processing lock for vehicle {VehicleId}", vehicleId);
            }
        }

        // Add these new methods for person-level locking

        /// <summary>
        /// Check if a person is currently being processed by another instance
        /// </summary>
        private async Task<bool> IsPersonCurrentlyProcessingAsync(Guid personId)
        {
            // Implementation depends on your caching/storage solution
            // This could use Redis, Azure Cache, or even Azure Table Storage

            // For now, return false to avoid breaking changes
            // TODO: Implement proper distributed locking mechanism
            return false;
        }

        /// <summary>
        /// Mark a person as being processed with a TTL to prevent deadlocks
        /// </summary>
        private async Task MarkPersonAsProcessingAsync(Guid personId, string messageId)
        {
            try
            {
                // Implementation depends on your distributed cache solution
                // This should set a distributed lock with TTL (e.g., 10 minutes)

                _logger.LogDebug("Marked person {PersonId} as processing by message {MessageId}", personId, messageId);

                // TODO: Implement proper distributed locking mechanism
                // Example using Redis: await cache.StringSetAsync($"person_processing:{personId}", messageId, TimeSpan.FromMinutes(10));
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to mark person {PersonId} as processing. Continuing without lock.", personId);
            }
        }

        /// <summary>
        /// Release the processing lock for a person
        /// </summary>
        private async Task ReleasePersonProcessingLockAsync(Guid personId)
        {
            try
            {
                // Implementation depends on your distributed cache solution
                // This should remove the distributed lock

                _logger.LogDebug("Released processing lock for person {PersonId}", personId);

                // TODO: Implement proper distributed locking mechanism  
                // Example using Redis: await cache.KeyDeleteAsync($"person_processing:{personId}");
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to release processing lock for person {PersonId}", personId);
            }
        }

        /// <summary>
        /// Determines if an exception is likely due to a race condition in user access processing
        /// </summary>
        private bool IsRaceConditionRelatedError(Exception ex)
        {
            var message = ex.Message?.ToLowerInvariant() ?? string.Empty;
            var innerMessage = ex.InnerException?.Message?.ToLowerInvariant() ?? string.Empty;

            return message.Contains("row count") ||
                   message.Contains("already deleted") ||
                   message.Contains("not found") ||
                   message.Contains("batch command") ||
                   message.Contains("sql not available") ||
                   message.Contains("departmentvehiclenormalcardaccessdeletehandler") ||
                   message.Contains("modelvehiclenormalcardaccessdeletehandler") ||
                   innerMessage.Contains("row count") ||
                   innerMessage.Contains("batch command") ||
                   innerMessage.Contains("sql not available");
        }
    }




}