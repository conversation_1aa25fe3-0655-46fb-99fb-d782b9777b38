#nullable disable
using System;
using System.Text.Json;
using NFluent;
using NUnit.Framework;
using System.Linq;
using FleetXQFunctionService.Messages;

namespace FleetXQFunctionService.Tests
{
    [TestFixture]
    public class UserAccessUpdateMessageTests
    {
        [Test]
        public void UserAccessUpdateMessage_DefaultConstructor_ShouldInitializeCorrectly()
        {
            // Act
            var message = new UserAccessUpdateMessage();

            // Assert
            Check.That(message).IsNotNull();
            Check.That(message.PersonId).IsEqualTo(Guid.Empty);
            Check.That(message.CustomerId).IsEqualTo(Guid.Empty);
            Check.That(message.InitiatedByUserId).IsNull();
            Check.That(message.PersonToSiteAccessesJson).IsNull();
            Check.That(message.PersonToDepartmentAccessesJson).IsNull();
            Check.That(message.PersonToModelAccessesJson).IsNull();
            Check.That(message.PersonToVehicleAccessesJson).IsNull();
            Check.That(message.CorrelationId).IsNull();
            Check.That(message.Priority).IsEqualTo("Normal");
        }

        [Test]
        public void UserAccessUpdateMessage_PropertyAssignment_ShouldSetValuesCorrectly()
        {
            // Arrange
            var personId = Guid.NewGuid();
            var customerId = Guid.NewGuid();
            var userId = Guid.NewGuid();
            var correlationId = Guid.NewGuid().ToString();
            var createdAt = DateTime.UtcNow;

            // Act
            var message = new UserAccessUpdateMessage
            {
                PersonId = personId,
                CustomerId = customerId,
                PersonToSiteAccessesJson = "[{\"test\":\"site\"}]",
                PersonToDepartmentAccessesJson = "[{\"test\":\"dept\"}]",
                PersonToModelAccessesJson = "[{\"test\":\"model\"}]",
                PersonToVehicleAccessesJson = "[{\"test\":\"vehicle\"}]",
                CreatedAt = createdAt,
                InitiatedByUserId = userId,
                CorrelationId = correlationId,
                Priority = "High"
            };

            // Assert
            Check.That(message.PersonId).IsEqualTo(personId);
            Check.That(message.CustomerId).IsEqualTo(customerId);
            Check.That(message.PersonToSiteAccessesJson).IsEqualTo("[{\"test\":\"site\"}]");
            Check.That(message.PersonToDepartmentAccessesJson).IsEqualTo("[{\"test\":\"dept\"}]");
            Check.That(message.PersonToModelAccessesJson).IsEqualTo("[{\"test\":\"model\"}]");
            Check.That(message.PersonToVehicleAccessesJson).IsEqualTo("[{\"test\":\"vehicle\"}]");
            Check.That(message.CreatedAt).IsEqualTo(createdAt);
            Check.That(message.InitiatedByUserId).IsEqualTo(userId);
            Check.That(message.CorrelationId).IsEqualTo(correlationId);
            Check.That(message.Priority).IsEqualTo("High");
        }

        [Test]
        public void UserAccessUpdateMessage_JsonSerialization_ShouldProduceValidJson()
        {
            // Arrange
            var message = CreateTestMessage();

            // Act
            var json = JsonSerializer.Serialize(message);

            // Assert
            Check.That(json).IsNotNull().And.IsNotEmpty();
            Check.That(json).Contains("PersonId");
            Check.That(json).Contains("CustomerId");
            Check.That(json).Contains("CorrelationId");
            Check.That(json).Contains("Priority");
        }

        [Test]
        public void UserAccessUpdateMessage_JsonDeserialization_ShouldRestoreOriginalValues()
        {
            // Arrange
            var originalMessage = CreateTestMessage();
            var json = JsonSerializer.Serialize(originalMessage);

            // Act
            var deserializedMessage = JsonSerializer.Deserialize<UserAccessUpdateMessage>(json);

            // Assert
            Check.That(deserializedMessage).IsNotNull();
            Check.That(deserializedMessage.PersonId).IsEqualTo(originalMessage.PersonId);
            Check.That(deserializedMessage.CustomerId).IsEqualTo(originalMessage.CustomerId);
            Check.That(deserializedMessage.InitiatedByUserId).IsEqualTo(originalMessage.InitiatedByUserId);
            Check.That(deserializedMessage.CorrelationId).IsEqualTo(originalMessage.CorrelationId);
            Check.That(deserializedMessage.Priority).IsEqualTo(originalMessage.Priority);
            Check.That(deserializedMessage.PersonToSiteAccessesJson).IsEqualTo(originalMessage.PersonToSiteAccessesJson);
        }

        [Test]
        public void UserAccessUpdateMessage_WithEmptyJsonArrays_ShouldSerializeCorrectly()
        {
            // Arrange
            var message = new UserAccessUpdateMessage
            {
                PersonId = Guid.NewGuid(),
                CustomerId = Guid.NewGuid(),
                PersonToSiteAccessesJson = "[]",
                PersonToDepartmentAccessesJson = "[]",
                PersonToModelAccessesJson = "[]",
                PersonToVehicleAccessesJson = "[]",
                CreatedAt = DateTime.UtcNow,
                InitiatedByUserId = Guid.NewGuid(),
                CorrelationId = Guid.NewGuid().ToString(),
                Priority = "Normal"
            };

            // Act
            var json = JsonSerializer.Serialize(message);
            var deserializedMessage = JsonSerializer.Deserialize<UserAccessUpdateMessage>(json);

            // Assert
            Check.That(deserializedMessage).IsNotNull();
            Check.That(deserializedMessage.PersonToSiteAccessesJson).IsEqualTo("[]");
            Check.That(deserializedMessage.PersonToDepartmentAccessesJson).IsEqualTo("[]");
            Check.That(deserializedMessage.PersonToModelAccessesJson).IsEqualTo("[]");
            Check.That(deserializedMessage.PersonToVehicleAccessesJson).IsEqualTo("[]");
        }

        [Test]
        public void UserAccessUpdateMessage_WithNullJsonArrays_ShouldHandleCorrectly()
        {
            // Arrange
            var message = new UserAccessUpdateMessage
            {
                PersonId = Guid.NewGuid(),
                CustomerId = Guid.NewGuid(),
                PersonToSiteAccessesJson = null,
                PersonToDepartmentAccessesJson = null,
                PersonToModelAccessesJson = null,
                PersonToVehicleAccessesJson = null,
                CreatedAt = DateTime.UtcNow,
                InitiatedByUserId = Guid.NewGuid(),
                CorrelationId = Guid.NewGuid().ToString(),
                Priority = "Normal"
            };

            // Act
            var json = JsonSerializer.Serialize(message);
            var deserializedMessage = JsonSerializer.Deserialize<UserAccessUpdateMessage>(json);

            // Assert
            Check.That(deserializedMessage).IsNotNull();
            Check.That(deserializedMessage.PersonToSiteAccessesJson).IsNull();
            Check.That(deserializedMessage.PersonToDepartmentAccessesJson).IsNull();
            Check.That(deserializedMessage.PersonToModelAccessesJson).IsNull();
            Check.That(deserializedMessage.PersonToVehicleAccessesJson).IsNull();
        }

        [Test]
        public void UserAccessUpdateMessage_WithDifferentPriorityLevels_ShouldPreservePriority()
        {
            // Arrange & Act & Assert
            var priorities = new[] { "Normal", "High", "Critical", "Low" };

            foreach (var priority in priorities)
            {
                var message = new UserAccessUpdateMessage
                {
                    PersonId = Guid.NewGuid(),
                    Priority = priority
                };

                var json = JsonSerializer.Serialize(message);
                var deserializedMessage = JsonSerializer.Deserialize<UserAccessUpdateMessage>(json);

                Check.That(deserializedMessage.Priority).IsEqualTo(priority);
            }
        }

        [Test]
        public void UserAccessUpdateMessage_TimestampAccuracy_ShouldPreserveDateTime()
        {
            // Arrange
            var specificTime = new DateTime(2024, 7, 18, 12, 30, 45, 123, DateTimeKind.Utc);
            var message = new UserAccessUpdateMessage
            {
                PersonId = Guid.NewGuid(),
                CreatedAt = specificTime
            };

            // Act
            var json = JsonSerializer.Serialize(message);
            var deserializedMessage = JsonSerializer.Deserialize<UserAccessUpdateMessage>(json);

            // Assert
            Check.That(deserializedMessage.CreatedAt).IsEqualTo(specificTime);
        }

        [Test]
        public void UserAccessUpdateMessage_LargeJsonPayload_ShouldHandleCorrectly()
        {
            // Arrange
            var largeJsonArray = "[" + string.Join(",",
                Enumerable.Repeat("{\"id\":\"" + Guid.NewGuid() + "\",\"access\":true}", 100)) + "]";

            var message = new UserAccessUpdateMessage
            {
                PersonId = Guid.NewGuid(),
                PersonToVehicleAccessesJson = largeJsonArray
            };

            // Act
            var json = JsonSerializer.Serialize(message);
            var deserializedMessage = JsonSerializer.Deserialize<UserAccessUpdateMessage>(json);

            // Assert
            Check.That(deserializedMessage.PersonToVehicleAccessesJson).IsEqualTo(largeJsonArray);
        }

        #region Helper Methods

        private UserAccessUpdateMessage CreateTestMessage()
        {
            return new UserAccessUpdateMessage
            {
                PersonId = Guid.NewGuid(),
                CustomerId = Guid.NewGuid(),
                PersonToSiteAccessesJson = "[]",
                PersonToDepartmentAccessesJson = "[]",
                PersonToModelAccessesJson = "[]",
                PersonToVehicleAccessesJson = "[]",
                CreatedAt = DateTime.UtcNow,
                InitiatedByUserId = Guid.NewGuid(),
                CorrelationId = Guid.NewGuid().ToString(),
                Priority = "Normal"
            };
        }

        #endregion
    }
}