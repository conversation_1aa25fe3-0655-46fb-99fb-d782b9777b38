﻿using FleetXQ.BusinessLayer.Components.Server.Custom;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer.Extensions;
using Microsoft.Extensions.DependencyInjection;
using MimeKit.Utils;
using System;
using System.Linq;
using System.Threading.Tasks;
using GenerativeObjects.Practices.ExceptionHandling;
using System.Collections.Generic;


namespace FleetXQ.Data.DataProvidersExtensions.Custom
{
    public class GOUserDataProviderExtension : IDataProviderExtension<GOUserDataObject>
    {
        private readonly IDataFacade _dataFacade;
        private readonly IServiceProvider _serviceProvider;
        private readonly IDeviceMessageHandler _deviceMessageHandler;

        public GOUserDataProviderExtension(IServiceProvider serviceProvider, IDataFacade dataFacade, IDeviceMessageHandler deviceMessageHandler)
        {
            _dataFacade = dataFacade;
            _serviceProvider = serviceProvider;
            _deviceMessageHandler = deviceMessageHandler;
        }

        public void Init(IDataProviderExtensionProvider dataProvider)
        {
            dataProvider.OnAfterSave += DataProvider_OnAfterSaveAsync;
            dataProvider.OnBeforeSaveDataSet += OnBeforeSaveDataSetAsync;
            dataProvider.OnBeforeSave += DataProvider_OnBeforeSave;
        }

        private async Task CheckForDuplicateUsernameAndEmail(GOUserDataObject goUser)
        {
            if (!string.IsNullOrEmpty(goUser.UserName))
            {
                var existingUserWithUsername = (await _dataFacade.GOUserDataProvider.GetCollectionAsync(
                    null,
                    "UserName == @0 && Id != @1",
                    new object[] { goUser.UserName, goUser.Id },
                    skipSecurity: true
                )).FirstOrDefault();

                if (existingUserWithUsername != null)
                {
                    throw new GOServerException("A user with this username already exists. Please choose a different username.");
                }
            }

            // TODO: Temporary disabled email check          
            // if (!string.IsNullOrEmpty(goUser.EmailAddress))
            // {
            //     var existingUserWithEmail = (await _dataFacade.GOUserDataProvider.GetCollectionAsync(
            //         null,
            //         "EmailAddress == @0 && Id != @1",
            //         new object[] { goUser.EmailAddress, goUser.Id },
            //         skipSecurity: true
            //     )).FirstOrDefault();

            //     if (existingUserWithEmail != null)
            //     {
            //         throw new GOServerException("A user with this email address already exists. Please use a different email address.");
            //     }
            // }
        }

        private async Task DataProvider_OnBeforeSave(OnBeforeSaveEventArgs arg)
        {
            var goUser = arg.Entity as GOUserDataObject;

            if (goUser != null)
            {
                await CheckForDuplicateUsernameAndEmail(goUser);

                if (goUser.PreferredLocale.HasValue)
                {
                    goUser.PreferredLocaleString = DataUtils.GetLocaleString(goUser.PreferredLocale.Value);
                }
                else
                {
                    goUser.PreferredLocaleString = null;
                }

                goUser.WebsiteAccessLevelValue = (short)goUser.WebsiteAccessLevel;

                // Populate AllowedDepartmentNames using GOUserDepartments
                await PopulateAllowedDepartmentNames(goUser);
            }
        }

        private async Task PopulateAllowedDepartmentNames(GOUserDataObject goUser)
        {
            // Only populate AllowedDepartmentNames when WebsiteAccessLevel is MultiDepartment
            if (goUser.WebsiteAccessLevel != WebsiteAccessLevelEnum.MultiDepartment)
            {
                // Clear AllowedDepartmentNames for non-MultiDepartment users
                goUser.AllowedDepartmentNames = null;
                return;
            }

            // Load the GOUserDepartmentItems for the user
            var goUserDepartments = await goUser.LoadGOUserDepartmentItemsAsync(skipSecurity: true);
            
            if (goUserDepartments != null && goUserDepartments.Any())
            {
                // Load departments and get department names
                var departmentNames = new List<string>();
                
                foreach (var goUserDepartment in goUserDepartments)
                {
                    // Load the department navigation property
                    var department = await goUserDepartment.LoadDepartmentAsync(skipSecurity: true);
                    
                    if (department != null && !string.IsNullOrEmpty(department.Name))
                    {
                        departmentNames.Add(department.Name);
                    }
                }

                // Remove duplicates, sort, and join
                var uniqueDepartmentNames = departmentNames
                    .Distinct()
                    .OrderBy(name => name)
                    .ToList();

                goUser.AllowedDepartmentNames = uniqueDepartmentNames.Any() 
                    ? string.Join(",", uniqueDepartmentNames) 
                    : null;
            }
            else
            {
                // If no departments assigned, clear the AllowedDepartmentNames
                goUser.AllowedDepartmentNames = null;
            }
        }

        private async Task DataProvider_OnAfterSaveAsync(OnAfterSaveEventArgs e)
        {
            var GOUser = e.Entity as GOUserDataObject;

            if (GOUser == null)
            {
                return;
            }

            var existingGoUserRole = (await _dataFacade.GOUserRoleDataProvider.GetCollectionAsync(null, "GOUserId == @0 && GORoleName == @1", new object[] { GOUser.Id, "DealerAdmin" }, skipSecurity: true)).SingleOrDefault();

            // provide Customer GoUser Role to the person if it has AccessGroupId
            if (GOUser.DealerId != null && existingGoUserRole == null)
            {
                var GOUserRole = _serviceProvider.GetRequiredService<GOUserRoleDataObject>();
                GOUserRole.GORoleName = "DealerAdmin";
                GOUserRole.UserForeignKey = GOUser.Id;
                // save the GOUserRole
                await _dataFacade.GOUserRoleDataProvider.SaveAsync(GOUserRole, skipSecurity: true);
            }

            // check if dealer user and create dealer driver for the user
            if (GOUser.DealerId != null)
            {
                var existingDealerDriver = (await _dataFacade.DealerDriverDataProvider.GetCollectionAsync(null, "GOUserId == @0", new object[] { GOUser.Id }, skipSecurity: true)).SingleOrDefault();
                if (existingDealerDriver == null)
                {
                    var dealerDriver = _serviceProvider.GetRequiredService<DealerDriverDataObject>();
                    dealerDriver.GOUserId = GOUser.Id;
                    dealerDriver.DriverType = DriverTypeEnum.Technician;
                    // save the dealer driver
                    await _dataFacade.DealerDriverDataProvider.SaveAsync(dealerDriver, skipSecurity: true);
                }
            }
            return;
        }

        private async Task OnBeforeSaveDataSetAsync(OnBeforeSaveDataSetEventArgs e)
        {
            if (e.IsDeleteInProgress)
            {
                return;
            }

            var GOUser = e.Entity as GOUserDataObject;

            await CheckForDuplicateUsernameAndEmail(GOUser);

            if (GOUser.DealerAdmin == true)
            {
                // get all customer of the dealer
                var customers = await _dataFacade.CustomerDataProvider.GetCollectionAsync(null, "DealerId == @0", new object[] { GOUser.DealerId }, skipSecurity: true);
                // get all GoUserToCustomer of the user
                var existingGoUserToCustomer = await _dataFacade.GoUserToCustomerDataProvider.GetCollectionAsync(null, "GOUserId == @0", new object[] { GOUser.Id }, skipSecurity: true);
                // remove customers that already exist in GoUserToCustomer
                var customersToSave = customers.Where(c => !existingGoUserToCustomer.Any(el => el.CustomerId == c.Id)).ToList();
                // create GoUserToCustomer for each customer
                foreach (var customer in customersToSave)
                {
                    var goUserToCustomer = _serviceProvider.GetRequiredService<GoUserToCustomerDataObject>();
                    goUserToCustomer.CustomerId = customer.Id;
                    goUserToCustomer.GOUserId = GOUser.Id;
                    GOUser.GoUserToCustomerItems.Add(goUserToCustomer);
                }
                // save GOUser
                // await _dataFacade.GOUserDataProvider.SaveAsync(GOUser, skipSecurity:true);
            }
            else if (GOUser.DealerAdmin == false)
            {
                await GOUser.LoadGoUserToCustomerItemsAsync(skipSecurity: true);
                // get all GoUserToCustomer of the user
                var existingGoUserToCustomer = GOUser.GoUserToCustomerItems;
                // remove all GoUserToCustomer of the user
                foreach (var goUserToCustomer in existingGoUserToCustomer)
                {
                    // mark each for deletion
                    goUserToCustomer.IsMarkedForDeletion = true;
                }
                // save the GO User
                // await _dataFacade.GOUserDataProvider.SaveAsync(GOUser, skipSecurity:true);
            }

            if (GOUser.PreferredLocale.HasValue)
            {
                GOUser.PreferredLocaleString = DataUtils.GetLocaleString(GOUser.PreferredLocale.Value);
            }
            else
            {
                GOUser.PreferredLocaleString = null;
            }

            GOUser.WebsiteAccessLevelValue = (short)GOUser.WebsiteAccessLevel;

            // Populate AllowedDepartmentNames using GOUserDepartments
            await PopulateAllowedDepartmentNames(GOUser);
        }
    }
}
