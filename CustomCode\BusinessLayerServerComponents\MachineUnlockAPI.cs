﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.BusinessLayer.Tasks;
using GenerativeObjects.Practices.ORMSupportClasses;
using FleetXQ.Data.DataObjects.Custom;
using Newtonsoft.Json;
using FleetXQ.BusinessLayer.Components.Server.Custom;
using GenerativeObjects.Practices.ExceptionHandling;
using Microsoft.Azure.Devices;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using DocumentFormat.OpenXml.Bibliography;

namespace FleetXQ.BusinessLayer.Components.Server
{
    /// <summary>
	/// MachineUnlockAPI Component
	///  
	/// </summary>
    public partial class MachineUnlockAPI : BaseServerComponent, IMachineUnlockAPI
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILoggingService _logger;
        public MachineUnlockAPI(IServiceProvider serviceProvider, IConfiguration configuration, IDataFacade dataFacade, ILoggingService logger) : base(serviceProvider, configuration, dataFacade)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        /// <summary>
        /// EditVehicleLockoutFromReportAsync Method
        ///  
        /// </summary>
        /// <param name="vehicleLockoutId"></param>
        /// <returns></returns>		
        /// <param name="unlockReason"></param>
        /// <returns></returns>		
        /// <param name="note"></param>
        /// <returns></returns>		
        /// <param name="realImpact"></param>
        /// <returns></returns>		
        public async System.Threading.Tasks.Task<ComponentResponse<System.Boolean>> EditVehicleLockoutFromReportAsyncAsync(System.Guid vehicleLockoutId, System.String unlockReason, System.Int32 realImpact, Dictionary<string, object> parameters = null)
        {
            var vehicleLockout = _serviceProvider.GetRequiredService<VehicleLockoutDataObject>();
            vehicleLockout.Id = vehicleLockoutId;
            vehicleLockout = await _dataFacade.VehicleLockoutDataProvider.GetAsync(vehicleLockout);
            if (vehicleLockout == null)
            {
                _logger.LogError(new GOServerException("Invalid Vehicle Lockout Id"));
                throw new GOServerException("Invalid Vehicle Lockout Id");
            }

            vehicleLockout.Note = unlockReason;
            vehicleLockout.RealImpact = (ImpactLockoutConfirmationEnum)realImpact;

            // Save the updated vehicle lockout
            await _dataFacade.VehicleLockoutDataProvider.SaveAsync(vehicleLockout);

            return new ComponentResponse<bool>(true);
        }

        /// <summary>
        /// processUnlockMessage Method
        /// </summary>
        /// <returns></returns>
        public async System.Threading.Tasks.Task<ComponentResponse<System.String>> ProcessUnlockMessageAsync(System.String Message, Dictionary<string, object> parameters = null)
        {
            // Deserialize the entire message
            dynamic messageObject = JsonConvert.DeserializeObject<dynamic>(Message);
            PayloadDataObject payloadObject;
            Guid sessionGuid;

            // Extract session_id and payload information
            if (messageObject?.originalMessage != null)
            {
                // This is a wrapper message with originalMessage containing the actual data
                string sessionId = messageObject.originalMessage.session_id?.ToString();
                if (string.IsNullOrEmpty(sessionId) || !Guid.TryParse(sessionId, out sessionGuid))
                {
                    _logger.LogError(new GOServerException("Invalid Session Id format in wrapped message"));
                    throw new Exception("Invalid Session Id format in wrapped message");
                }

                payloadObject = new PayloadDataObject
                {
                    IoTDeviceId = messageObject.originalMessage.IotDeviceId?.ToString(),
                    EventType = messageObject.originalMessage.event_type?.ToString(),
                    Payload = messageObject.originalMessage.payload?.ToString()
                };
            }
            else if (messageObject?.session_id != null)
            {
                // Direct message with session_id as a top-level field
                string sessionId = messageObject.session_id.ToString();
                if (string.IsNullOrEmpty(sessionId) || !Guid.TryParse(sessionId, out sessionGuid))
                {
                    _logger.LogError(new GOServerException("Invalid Session Id format in direct message"));
                    throw new Exception("Invalid Session Id format in direct message");
                }

                payloadObject = new PayloadDataObject
                {
                    IoTDeviceId = messageObject.IotDeviceId?.ToString(),
                    EventType = messageObject.event_type?.ToString(),
                    Payload = messageObject.payload?.ToString()
                };
            }
            else
            {
                // Legacy format - try to extract session ID from payload
                payloadObject = JsonConvert.DeserializeObject<PayloadDataObject>(Message);

                // Check if payload contains SID
                var payloadParts = payloadObject.Payload.Split(' ');
                string sid = null;

                foreach (var part in payloadParts)
                {
                    if (part.StartsWith("SID="))
                    {
                        sid = part.Substring(4);
                        break;
                    }
                }

                if (string.IsNullOrEmpty(sid) || !Guid.TryParse(sid, out sessionGuid))
                {
                    _logger.LogError(new GOServerException("Session Id not found or invalid in legacy format"));
                    throw new Exception("Session Id not found or invalid in legacy format");
                }
            }

            var module = (await _dataFacade.ModuleDataProvider.GetCollectionAsync(null, "IoTDevice == @0", new object[] { payloadObject.IoTDeviceId })).SingleOrDefault();
            if (module == null)
            {
                _logger.LogError(new GOServerException("Invalid IoTDeviceId"));
                throw new GOServerException("Invalid IoTDeviceId");
            }

            var vehile = await module.LoadVehicleAsync();
            if (vehile == null)
            {
                _logger.LogError(new GOServerException("Invalid Vehicle"));
                throw new GOServerException("Invalid Vehicle");
            }

            var site = await vehile.LoadSiteAsync();

            if (site == null)
            {
                _logger.LogError(new GOServerException("Invalid Site"));
                throw new GOServerException("Invalid Site");
            }

            var permissionDriver = (await _dataFacade.PermissionDataProvider.GetCollectionAsync(null, "LevelName == @0", new object[] { 3 })).SingleOrDefault();
            // get all driver access list
            var siteAccessList = (await _dataFacade.SiteVehicleNormalCardAccessDataProvider.GetCollectionAsync(null, "SiteId == @0", new object[] { site.Id })).ToList();
            if (siteAccessList == null)
            {
                _logger.LogError(new GOServerException("Invalid Site Access List"));
                throw new GOServerException("Invalid Site Access List");
            }
            if (payloadObject == null)
            {
                _logger.LogError(new GOServerException("Invalid Payload"));
                throw new Exception("Invalid Payload");
            }
            if (payloadObject.EventType != "UNLK")
            {
                _logger.LogError(new GOServerException("Invalid Payload Type"));
                throw new Exception("Invalid Payload Type");
            }

            int startIndex = payloadObject.Payload.IndexOf("UNLK=");
            string[] unlockPayload;
            if (startIndex != -1)
            {
                // Extract the substring starting after "UNLK=" and split it by commas
                string unlkValues = payloadObject.Payload.Substring(startIndex + 5);
                unlockPayload = unlkValues.Split(',');
            }
            else
            {
                _logger.LogError(new GOServerException("UNLK not found."));
                throw new Exception("UNLK not found.");
            }

            if (unlockPayload == null || unlockPayload.Length < 5)
            {
                _logger.LogError(new GOServerException("Invalid Unlock Payload: insufficient data"));
                throw new Exception("Invalid Unlock Payload: insufficient data");
            }

            // Add safe values with appropriate validation
            string unlockDriver = !string.IsNullOrEmpty(unlockPayload[0]) ? unlockPayload[0] : null;
            if (string.IsNullOrEmpty(unlockDriver))
            {
                _logger.LogError(new GOServerException("Invalid Unlock Driver"));
                throw new Exception("Invalid Unlock Driver");
            }

            DateTime? unlockTime = null;
            try
            {
                if (!string.IsNullOrEmpty(unlockPayload[1]))
                    unlockTime = DataUtils.HexToUtcTime(unlockPayload[1]);
            }
            catch (Exception ex)
            {
                _logger.LogError(new GOServerException($"Invalid Unlock Time format: {ex.Message}"));
                throw new Exception($"Invalid Unlock Time format: {ex.Message}");
            }

            int reason = 0;
            try
            {
                if (!string.IsNullOrEmpty(unlockPayload[2]))
                    reason = Convert.ToInt32(unlockPayload[2], 16);
            }
            catch (Exception ex)
            {
                _logger.LogError(new GOServerException($"Invalid Reason format: {ex.Message}"));
                throw new Exception($"Invalid Reason format: {ex.Message}");
            }

            string lockoutDriver = unlockPayload.Length > 3 ? unlockPayload[3] : null;

            DateTime? lockoutTime = null;
            try
            {
                if (!string.IsNullOrEmpty(unlockPayload[4]))
                    lockoutTime = DataUtils.HexToUtcTime(unlockPayload[4]);
            }
            catch (Exception ex)
            {
                _logger.LogError(new GOServerException($"Invalid Lockout Time format: {ex.Message}"));
                throw new Exception($"Invalid Lockout Time format: {ex.Message}");
            }

            string comment = unlockPayload.Length > 5 ? unlockPayload[5] : null;
            string realImpactStr = unlockPayload.Length > 6 ? unlockPayload[6] : "0";

            int realImpact = 0;
            try
            {
                if (!string.IsNullOrEmpty(realImpactStr))
                    realImpact = Convert.ToInt32(realImpactStr);
            }
            catch (Exception ex)
            {
                _logger.LogError(new GOServerException($"Invalid Real Impact format: {ex.Message}"));
                throw new Exception($"Invalid Real Impact format: {ex.Message}");
            }

            // Check if the driver is in the access list
            SiteVehicleNormalCardAccessDataObject cardAccess = null;

            foreach (var access in siteAccessList)
            {
                var accessCard = await access.LoadCardAsync();
                if (accessCard != null && accessCard.Weigand == unlockDriver)
                {
                    cardAccess = access;
                    break; // Exit the loop once the matching card is found
                }
            }

            if (cardAccess == null)
            {
                _logger.LogError(new GOServerException("Invalid card"));
                throw new GOServerException("Invalid card");
            }

            var driverCard = await cardAccess.LoadCardAsync();
            if (driverCard == null)
            {
                _logger.LogError(new GOServerException("Unable to load card"));
                throw new GOServerException("Unable to load card");
            }

            var driver = await driverCard.LoadDriverAsync();
            if (driver == null)
            {
                _logger.LogError(new GOServerException("Invalid Driver"));
                throw new GOServerException("Invalid Driver");
            }

            VehicleLockoutDataObject vehicleLockoutDataObject = _serviceProvider.GetRequiredService<VehicleLockoutDataObject>();
            vehicleLockoutDataObject.Id = Guid.NewGuid();
            vehicleLockoutDataObject.SessionId = sessionGuid;
            vehicleLockoutDataObject.DriverId = driver.Id;
            vehicleLockoutDataObject.LockoutTime = lockoutTime ?? DateTime.UtcNow;
            vehicleLockoutDataObject.UnlockDateTime = unlockTime ?? DateTime.UtcNow;
            vehicleLockoutDataObject.Reason = (LockoutReasonEnum)reason;
            vehicleLockoutDataObject.Comment = comment;
            vehicleLockoutDataObject.RealImpact = (ImpactLockoutConfirmationEnum)realImpact;
            try
            {

                await _dataFacade.VehicleLockoutDataProvider.SaveAsync(vehicleLockoutDataObject);

            }
            catch (Exception ex)
            {
                _logger.LogError(new GOServerException($"Error saving vehicle lockout data: {ex.Message}"));
                throw new GOServerException($"Error saving vehicle lockout data: {ex.Message}");
            }

            return new ComponentResponse<string>(JsonConvert.SerializeObject(new DefaultResponse(200, "Success")));
        }

    }
}
// "payload":"SID=61382798-bded-4e21-aa4f-772176e12779 UNLK=6697,65CD1BC9,FB,25FA3,65CD1BBD,,(null)"
