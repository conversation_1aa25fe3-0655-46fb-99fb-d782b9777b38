using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Text.Json.Nodes;
using System.Threading.Tasks;

namespace FleetXQFunctionService.Services
{
    public class SyncProcessService : ISyncProcessService
    {
        private readonly ILogger<SyncProcessService> _logger;
        private readonly IConfiguration _configuration;
        private readonly HttpClient _httpClient;
        private readonly JsonSerializerOptions _jsonOptions;
        private readonly IAuthenticationCacheService _authCache;

        public SyncProcessService(ILogger<SyncProcessService> logger, IConfiguration configuration, IAuthenticationCacheService authCache)
        {
            _logger = logger;
            _configuration = configuration;
            _authCache = authCache;
            _httpClient = new HttpClient
            {
                Timeout = TimeSpan.FromMinutes(10) // Shorter timeout for sync operations
            };

            // Configure HTTP client for better concurrent performance
            _httpClient.DefaultRequestHeaders.ConnectionClose = false;
            _httpClient.DefaultRequestHeaders.Connection.Add("keep-alive");

            _jsonOptions = new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase };
        }

        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                var baseUrl = _configuration["FleetXQApiBaseUrl"];
                if (string.IsNullOrEmpty(baseUrl))
                {
                    _logger.LogWarning("FleetXQApiBaseUrl not configured");
                    return false;
                }

                _logger.LogInformation("Testing FleetXQ API connection to {BaseUrl}", baseUrl);

                // Try to get a CSRF token to verify API is accessible
                var csrfResponse = await _httpClient.GetAsync($"{baseUrl}/dataset/api/goservices/csrf-token");
                return csrfResponse.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Connection test failed: {Message}", ex.Message);
                return false;
            }
        }

        public async Task<bool> ProcessVehicleSyncAsync(string message)
        {
            try
            {
                var baseUrl = _configuration["FleetXQApiBaseUrl"];
                if (string.IsNullOrEmpty(baseUrl))
                {
                    _logger.LogError("FleetXQApiBaseUrl not configured");
                    return false;
                }

                // Try to get cached authentication first
                var (csrfToken, applicationToken, userToken) = await _authCache.GetCachedAuthenticationAsync(baseUrl);

                // If no cached auth or tokens are null/empty, authenticate fresh
                if (string.IsNullOrEmpty(csrfToken) || string.IsNullOrEmpty(applicationToken) || string.IsNullOrEmpty(userToken))
                {
                    (csrfToken, applicationToken, userToken) = await AuthenticateAsync(baseUrl);
                    if (string.IsNullOrEmpty(csrfToken) || string.IsNullOrEmpty(applicationToken) || string.IsNullOrEmpty(userToken))
                    {
                        _logger.LogError("Failed to authenticate with FleetXQ API");
                        return false;
                    }
                }

                // Build API URL with authentication tokens as query parameters
                var apiUrl = $"{baseUrl}/dataset/api/syncprocess/managesync?_user_token={userToken}&_application_token={applicationToken}";

                // Use multipart/form-data with Message field containing the message (matching existing pattern)
                var content = new MultipartFormDataContent();
                content.Add(new StringContent(message), "Message");

                // Use HttpRequestMessage with per-request headers instead of modifying shared HttpClient
                var request = new HttpRequestMessage(HttpMethod.Post, apiUrl)
                {
                    Content = content
                };
                request.Headers.Add("x-csrf-token", csrfToken);

                _logger.LogDebug("Calling ManageSyncAsync API for vehicle sync message");

                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadAsStringAsync();
                    _logger.LogDebug("ManageSyncAsync API response: {Response}", result);

                    // Check if response indicates success
                    if (!string.IsNullOrWhiteSpace(result))
                    {
                        try
                        {
                            var apiResponse = JsonSerializer.Deserialize<ApiResponse>(result, _jsonOptions);
                            return apiResponse?.IsSuccess == true || !string.IsNullOrEmpty(apiResponse?.Result);
                        }
                        catch (JsonException)
                        {
                            // If we can't parse the response, consider it successful if we got a 200 response
                            return true;
                        }
                    }
                    return true;
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError("ManageSyncAsync API failed with status {StatusCode}: {Error}",
                        response.StatusCode, errorContent);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling ManageSyncAsync API: {Error}", ex.Message);
                throw;
            }
        }

        private async Task<(string csrfToken, string applicationToken, string userToken)> AuthenticateAsync(string baseUrl)
        {
            try
            {
                // Get CSRF token
                var csrfResponse = await _httpClient.GetAsync($"{baseUrl}/dataset/api/goservices/csrf-token");
                csrfResponse.EnsureSuccessStatusCode();
                var csrfContent = await csrfResponse.Content.ReadAsStringAsync();
                var csrfJson = JsonSerializer.Deserialize<JsonObject>(csrfContent);
                var csrfToken = csrfJson?["csrfToken"]?.GetValue<string>();

                if (string.IsNullOrEmpty(csrfToken))
                {
                    _logger.LogError("Failed to retrieve CSRF token from response");
                    return (null, null, null);
                }

                // Authenticate using HttpRequestMessage to avoid concurrent header modification
                var loginUrl = $"{baseUrl}/dataset/api/gosecurityprovider/authenticate";
                var credentials = new Dictionary<string, string>
                {
                    {"username", _configuration["FleetXQUsername"] ?? "Admin"},
                    {"password", _configuration["FleetXQPassword"] ?? "Admin"}
                };

                var content = new MultipartFormDataContent();
                foreach (var kvp in credentials)
                {
                    content.Add(new StringContent(kvp.Value), kvp.Key);
                }

                // Use HttpRequestMessage with per-request headers instead of modifying shared HttpClient
                var request = new HttpRequestMessage(HttpMethod.Post, loginUrl)
                {
                    Content = content
                };
                request.Headers.Add("x-csrf-token", csrfToken);

                var response = await _httpClient.SendAsync(request);
                response.EnsureSuccessStatusCode();

                var responseContent = await response.Content.ReadAsStringAsync();
                var jsonResponse = JsonSerializer.Deserialize<JsonObject>(responseContent);

                var tokensObject = jsonResponse?["ObjectsDataSet"]?["GOSecurityTokensObjectsDataSet"]?["GOSecurityTokensObjects"]?["1"];
                var applicationToken = tokensObject?["ApplicationToken"]?.GetValue<string>();
                var userToken = tokensObject?["AuthenticationToken"]?.GetValue<string>();

                if (string.IsNullOrEmpty(applicationToken) || string.IsNullOrEmpty(userToken))
                {
                    _logger.LogError("Failed to retrieve authentication tokens from response");
                    return (null, null, null);
                }

                _logger.LogDebug("Successfully authenticated with FleetXQ API");

                // Cache the successful authentication
                var authCacheService = _authCache as AuthenticationCacheService;
                authCacheService?.CacheAuthentication(baseUrl, csrfToken, applicationToken, userToken);

                return (csrfToken, applicationToken, userToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to authenticate with FleetXQ API: {Message}", ex.Message);

                // Invalidate cache on authentication failure
                _authCache.InvalidateCache(baseUrl);

                return (null, null, null);
            }
        }
    }

    public class ApiResponse
    {
        public bool IsSuccess { get; set; }
        public string Result { get; set; }
        public string ErrorMessage { get; set; }
    }
}