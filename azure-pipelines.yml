trigger:
  branches: 
    include:
      - '*'  # This will trigger the pipeline for any branch
    exclude:
      - 'release_pipeline'  # Exclude the release_pipeline branch

variables:
  System.Debug: true  # Enable global verbose logging

pool:
  vmImage: 'windows-latest'

steps:
  # Step 1: Check Internet Access
  - powershell: |
      try {
          $response = Invoke-WebRequest -Uri "https://www.google.com"
          if ($response.StatusCode -eq 200) {
              Write-Host "Internet is accessible"
          }
      } catch {
          Write-Host "Internet is not accessible"
          exit 1
      }
    displayName: 'Check Internet Access'

  # Step 2: Check SQL Server Connection
  - powershell: |
      try {
          $connection = New-Object System.Data.SqlClient.SqlConnection
          $connection.ConnectionString = "Data Source=fleetxqdb.database.windows.net,1433;Initial Catalog=FleetXQ.Application-8735218d-3aeb-4563-bccb-8cdfcdf1188f;User ID=fleetxqdb;Password=****************;Connect Timeout=30;Encrypt=True;TrustServerCertificate=False"
          $connection.Open()
          Write-Host "SQL Server connection successful"
          $connection.Close()
      } catch {
          Write-Host "Failed to connect to SQL Server"
          exit 1
      }
    displayName: 'Check SQL Server Connection'

  # Step 3: Install .NET Core SDK
  - task: UseDotNet@2
    displayName: 'Install .NET Core SDK'
    inputs:
      packageType: 'sdk'
      version: '7.x'

  # Step 4: Restore NuGet Packages
  - task: DotNetCoreCLI@2
    displayName: 'Restore NuGet Packages'
    inputs:
      command: 'restore'
      projects: '**/*.csproj'

  # Step 5: Build FleetXQ.ConstructViews Project
  - task: DotNetCoreCLI@2
    displayName: 'Build FleetXQ.ConstructViews Project'
    inputs:
      command: 'build'
      projects: 'GeneratedCode/ConstructViews/FleetXQ.ConstructViews.csproj'
      arguments: '--configuration Debug'

  # Step 6: Verify FleetXQ.ConstructViews.exe Exists
  - powershell: |
      if (Test-Path "$(Build.SourcesDirectory)\GeneratedCode\ConstructViews\bin\Debug\net7.0\FleetXQ.ConstructViews.exe") {
          Write-Host "FleetXQ.ConstructViews.exe exists."
      } else {
          Write-Host "FleetXQ.ConstructViews.exe does not exist."
          exit 1
      }
    displayName: 'Verify FleetXQ.ConstructViews.exe Exists'

  # Step 7: Run FleetXQ.ConstructViews.exe with Verbose Logging
  - powershell: |
      $rootDir = "$(Build.SourcesDirectory)"
      $outputLog = "$(Build.ArtifactStagingDirectory)\constructviews-output.log"
      $errorLog = "$(Build.ArtifactStagingDirectory)\constructviews-error.log"
      
      Write-Host "Running FleetXQ.ConstructViews.exe with root directory: $rootDir"

      Start-Process -FilePath "$(Build.SourcesDirectory)\GeneratedCode\ConstructViews\bin\Debug\net7.0\FleetXQ.ConstructViews.exe" `
          -ArgumentList "-rootdir=$rootDir", "--verbose" `
          -NoNewWindow -Wait `
          -RedirectStandardOutput $outputLog `
          -RedirectStandardError $errorLog

      if ($LASTEXITCODE -ne 0) {
          Write-Host "FleetXQ.ConstructViews.exe failed with exit code $LASTEXITCODE"
          Write-Host "Error Logs:"
          Get-Content $errorLog
          Write-Host "Output Logs:"
          Get-Content $outputLog
          exit $LASTEXITCODE
      } else {
          Write-Host "FleetXQ.ConstructViews.exe completed successfully."
          Write-Host "Output Logs:"
          Get-Content $outputLog
      }
    displayName: 'Run FleetXQ.ConstructViews.exe with Verbose Logging'

  # Step 8: Build the Solution
  - task: VSBuild@1
    displayName: 'Build Solution with VSBuild'
    inputs:
      solution: '**/FleetXQ.sln'
      platform: 'Any CPU'
      configuration: 'Release'