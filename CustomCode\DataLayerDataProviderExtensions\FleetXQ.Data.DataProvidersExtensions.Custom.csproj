﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net7.0</TargetFramework>
    <GeneratePackageOnBuild>True</GeneratePackageOnBuild>
  </PropertyGroup>
  <PropertyGroup>
    <VersionPrefix>*********</VersionPrefix>
    <VersionSuffix></VersionSuffix>
    <Authors>Generative Objects</Authors>
    <Description>FleetXQ Business Layer Data Provider Custom Extensions</Description>
  </PropertyGroup>  
  <ItemGroup>
    <PackageReference Include="GenerativeObjects.Practices.LayerSupportClasses" Version="2.0.3" />
    <PackageReference Include="Microsoft.Azure.Devices" Version="1.39.0" />
    <PackageReference Include="Microsoft.CSharp" Version="4.7.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.2" />
    <PackageReference Include="NLog" Version="5.1.4" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\GeneratedCode\BusinessLayerORMSupportClasses\FleetXQ.BusinessLayer.ORMSupportClasses.csproj" />
    <ProjectReference Include="..\..\GeneratedCode\DataLayer\FleetXQ.Data.DataObjects.csproj" />
    <ProjectReference Include="..\BusinessLayerServerComponents\FleetXQ.BusinessLayer.Components.Server.Custom.csproj" />
  </ItemGroup>
</Project>