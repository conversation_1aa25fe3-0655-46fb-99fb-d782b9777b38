@echo off
setlocal enabledelayedexpansion

:: FleetXQ Targeted Build Script
:: This script performs a clean build of specific projects in the correct order
echo ========================================
echo FleetXQ Targeted Build Script
echo ========================================
echo.

:: Set error handling
set "SCRIPT_ERROR=0"

:: Define paths
set "SOLUTION_FILE=FleetXQ.sln"
set "CONSTRUCT_VIEWS_PROJECT=GeneratedCode\ConstructViews\FleetXQ.ConstructViews.csproj"
set "WEB_APP_PROJECT=GeneratedCode\WebApplicationLayer\FleetXQ.Application.Web.csproj"

:: Check if dotnet CLI is available
echo [1/5] Checking .NET CLI availability...
dotnet --version >nul 2>&1
if !ERRORLEVEL! neq 0 (
    echo ERROR: .NET CLI is not available. Please install .NET SDK.
    set "SCRIPT_ERROR=1"
    goto :error_exit
)
echo .NET CLI is available.
echo.

:: Step 1: Clean the solution
echo [2/5] Cleaning solution...
dotnet clean "%SOLUTION_FILE%" --configuration Debug --verbosity minimal
if !ERRORLEVEL! neq 0 (
    echo ERROR: Failed to clean solution.
    set "SCRIPT_ERROR=1"
    goto :error_exit
)
echo Solution cleaned successfully.
echo.

:: Step 2: Restore packages for the solution
echo [3/5] Restoring NuGet packages...
dotnet restore "%SOLUTION_FILE%" --verbosity minimal
if !ERRORLEVEL! neq 0 (
    echo ERROR: Failed to restore NuGet packages.
    set "SCRIPT_ERROR=1"
    goto :error_exit
)
echo NuGet packages restored successfully.
echo.

:: Step 3: Build ConstructViews project first
echo [4/5] Building FleetXQ.ConstructViews project...
dotnet build "%CONSTRUCT_VIEWS_PROJECT%" --configuration Debug --no-restore --verbosity minimal
if !ERRORLEVEL! neq 0 (
    echo ERROR: Failed to build FleetXQ.ConstructViews project.
    set "SCRIPT_ERROR=1"
    goto :error_exit
)
echo FleetXQ.ConstructViews project built successfully.
echo.

:: Step 4: Build Web Application project
echo [5/5] Building FleetXQ.Application.Web project...
dotnet build "%WEB_APP_PROJECT%" --configuration Debug --no-restore --verbosity minimal
if !ERRORLEVEL! neq 0 (
    echo ERROR: Failed to build FleetXQ.Application.Web project.
    set "SCRIPT_ERROR=1"
    goto :error_exit
)
echo FleetXQ.Application.Web project built successfully.
echo.

:: Success
echo ========================================
echo BUILD COMPLETED SUCCESSFULLY!
echo ========================================
echo.
echo Built projects:
echo   1. %CONSTRUCT_VIEWS_PROJECT%
echo   2. %WEB_APP_PROJECT%
echo.
goto :end

:error_exit
echo.
echo ========================================
echo BUILD FAILED!
echo ========================================
echo Please check the error messages above and fix any issues.
echo.

:end
exit /b %SCRIPT_ERROR%
