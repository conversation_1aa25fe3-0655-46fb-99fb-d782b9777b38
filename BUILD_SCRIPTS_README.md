# FleetXQ Targeted Build Scripts

This directory contains targeted build scripts for the FleetXQ project that perform a clean build of specific projects in the correct order.

## Available Scripts

### 1. Windows Batch Script (`build-targeted.cmd`)
**Usage:**
```cmd
build-targeted.cmd
```

### 2. PowerShell Script (`build-targeted.ps1`)
**Usage:**
```powershell
# Default (Debug configuration)
.\build-targeted.ps1

# Release configuration
.\build-targeted.ps1 -Configuration Release

# Verbose output
.\build-targeted.ps1 -Verbose

# Release with verbose output
.\build-targeted.ps1 -Configuration Release -Verbose
```

### 3. <PERSON><PERSON>t (`build-targeted.sh`)
**Usage:**
```bash
# Default (Debug configuration)
./build-targeted.sh

# Release configuration
./build-targeted.sh Release
```

## What These Scripts Do

All scripts perform the following steps in order:

1. **Check .NET CLI availability** - Verifies that the .NET SDK is installed
2. **Clean solution** - Runs `dotnet clean` on the entire solution
3. **Restore NuGet packages** - Runs `dotnet restore` to download dependencies
4. **Build ConstructViews project** - Builds `GeneratedCode\ConstructViews\FleetXQ.ConstructViews.csproj`
5. **Build Web Application project** - Builds `GeneratedCode\WebApplicationLayer\FleetXQ.Application.Web.csproj`

## Build Order Importance

The build order is critical because:
- The `FleetXQ.ConstructViews` project creates an executable that is used by the Web Application project's post-build event
- The Web Application project has a dependency on the ConstructViews executable being available

## Error Handling

All scripts include comprehensive error handling:
- Exit immediately if any step fails
- Display clear error messages
- Return appropriate exit codes for CI/CD integration

## Requirements

- .NET 7.0 SDK or later
- All project files must be present in their expected locations

## Troubleshooting

If the build fails:
1. Ensure .NET SDK is installed and accessible via command line
2. Check that all project files exist in the expected locations
3. Verify that you have sufficient permissions to build the projects
4. Check the detailed error output for specific issues

## Integration with CI/CD

These scripts are designed to work well with CI/CD pipelines:
- Return proper exit codes (0 for success, 1 for failure)
- Provide clear, parseable output
- Support different build configurations
