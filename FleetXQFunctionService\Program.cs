using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Caching.Memory;
using FleetXQFunctionService.Services;

var host = new HostBuilder()
    .ConfigureFunctionsWorkerDefaults()
    .ConfigureAppConfiguration((context, config) =>
    {
        config.AddJsonFile("local.settings.json", optional: true, reloadOnChange: true);
        config.AddEnvironmentVariables();
    })
    .ConfigureServices((context, services) =>
    {
        // Register memory cache for authentication caching
        services.AddMemoryCache();

        // Register authentication cache service
        services.AddSingleton<IAuthenticationCacheService, AuthenticationCacheService>();

        // Register circuit breaker service for resilient API calls
        services.AddSingleton<ICircuitBreakerService, CircuitBreakerService>();

        // Register message lock renewal service for long-running operations
        services.AddSingleton<IMessageLockRenewalService, MessageLockRenewalService>();

        // Register our custom VehicleAccessCreationService
        services.AddSingleton<IVehicleAccessCreationService, VehicleAccessCreationService>();

        // Register sync process service for vehicle sync operations
        services.AddSingleton<ISyncProcessService, SyncProcessService>();

        // Register delay service for retry logic
        services.AddSingleton<IDelayService, DelayService>();
    })
    .Build();

host.Run();