# FleetXQ Targeted Build Script (PowerShell)
# This script performs a clean build of specific projects in the correct order
# and optionally runs the web application

param(
    [string]$Configuration = "Debug",
    [switch]$Verbose,
    [switch]$Run,
    [string]$Urls = "http://localhost:5000"
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Define colors for output
$SuccessColor = "Green"
$ErrorColor = "Red"
$InfoColor = "Cyan"
$WarningColor = "Yellow"

function Write-Step {
    param([string]$Message, [string]$Color = $InfoColor)
    Write-Host $Message -ForegroundColor $Color
}

function Write-Success {
    param([string]$Message)
    Write-Host $Message -ForegroundColor $SuccessColor
}

function Write-Error-Custom {
    param([string]$Message)
    Write-Host $Message -ForegroundColor $ErrorColor
}

try {
    Write-Step "========================================"
    Write-Step "FleetXQ Targeted Build Script"
    if ($Run) {
        Write-Step "(Build and Run Mode)"
    } else {
        Write-Step "(Build Only Mode)"
    }
    Write-Step "========================================"
    Write-Host ""

    # Define paths
    $SolutionFile = "FleetXQ.sln"
    $ConstructViewsProject = "GeneratedCode\ConstructViews\FleetXQ.ConstructViews.csproj"
    $WebAppProject = "GeneratedCode\WebApplicationLayer\FleetXQ.Application.Web.csproj"

    # Check if files exist
    if (-not (Test-Path $SolutionFile)) {
        throw "Solution file not found: $SolutionFile"
    }
    if (-not (Test-Path $ConstructViewsProject)) {
        throw "ConstructViews project not found: $ConstructViewsProject"
    }
    if (-not (Test-Path $WebAppProject)) {
        throw "Web Application project not found: $WebAppProject"
    }

    # Set verbosity level
    $VerbosityLevel = if ($Verbose) { "normal" } else { "minimal" }

    # Step 2: Clean the solution
    Write-Step "[2/5] Cleaning solution..."
    dotnet clean $SolutionFile --configuration $Configuration --verbosity $VerbosityLevel
    if ($LASTEXITCODE -ne 0) {
        throw "Failed to clean solution"
    }
    Write-Success "Solution cleaned successfully."
    Write-Host ""

    # Step 3: Restore packages
    Write-Step "[3/5] Restoring NuGet packages..."
    dotnet restore $SolutionFile --verbosity $VerbosityLevel
    if ($LASTEXITCODE -ne 0) {
        throw "Failed to restore NuGet packages"
    }
    Write-Success "NuGet packages restored successfully."
    Write-Host ""

    # Step 4: Build ConstructViews project
    Write-Step "[4/5] Building FleetXQ.ConstructViews project..."
    dotnet build $ConstructViewsProject --configuration $Configuration --no-restore --verbosity $VerbosityLevel
    if ($LASTEXITCODE -ne 0) {
        throw "Failed to build FleetXQ.ConstructViews project"
    }
    Write-Success "FleetXQ.ConstructViews project built successfully."
    Write-Host ""

    # Step 5: Build Web Application project
    Write-Step "[5/5] Building FleetXQ.Application.Web project..."
    dotnet build $WebAppProject --configuration $Configuration --no-restore --verbosity $VerbosityLevel
    if ($LASTEXITCODE -ne 0) {
        throw "Failed to build FleetXQ.Application.Web project"
    }
    Write-Success "FleetXQ.Application.Web project built successfully."
    Write-Host ""

    # Success message
    Write-Step "========================================" $SuccessColor
    Write-Step "BUILD COMPLETED SUCCESSFULLY!" $SuccessColor
    Write-Step "========================================" $SuccessColor
    Write-Host ""
    Write-Step "Built projects:"
    Write-Step "  1. $ConstructViewsProject"
    Write-Step "  2. $WebAppProject"
    Write-Host ""

    # Step 6: Run the web application if requested
    if ($Run) {
        Write-Step "[6/6] Starting web application..."
        Write-Step "Web application will be available at: $Urls" $InfoColor
        Write-Step "Press Ctrl+C to stop the application" $WarningColor
        Write-Host ""

        try {
            # Change to the web project directory for running
            $WebProjectDir = Split-Path $WebAppProject -Parent
            Push-Location $WebProjectDir

            # Run the web application
            dotnet run --configuration $Configuration --urls $Urls --no-build
        }
        catch {
            Write-Error-Custom "Failed to start web application: $($_.Exception.Message)"
        }
        finally {
            Pop-Location
        }
    }

    exit 0
}
catch {
    Write-Host ""
    Write-Error-Custom "========================================"
    Write-Error-Custom "BUILD FAILED!"
    Write-Error-Custom "========================================"
    Write-Error-Custom "Error: $($_.Exception.Message)"
    Write-Host ""
    Write-Step "Please check the error messages above and fix any issues." $WarningColor
    Write-Host ""
    
    exit 1
}
