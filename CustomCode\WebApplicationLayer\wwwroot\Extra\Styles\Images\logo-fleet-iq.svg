<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="150" height="56.527" viewBox="0 0 150 56.527">
  <defs>
    <clipPath id="clip-path">
      <rect id="Rectangle_1584" data-name="Rectangle 1584" width="150" height="56.527" fill="#fff"/>
    </clipPath>
  </defs>
  <g id="fleet-main" transform="translate(-8.5 -44)">
    <g id="FLEET" transform="translate(8.5 44)" opacity="0.901">
      <g id="Groupe_1434" data-name="Groupe 1434" clip-path="url(#clip-path)">
        <path id="Tracé_3507" data-name="Tracé 3507" d="M14.28,48.969v5.212H33.32v4.969H14.28v7.832H8.5V44H35.913v4.969Z" transform="translate(-8.5 -44)" fill="#fff"/>
      </g>
      <g id="Groupe_1435" data-name="Groupe 1435" clip-path="url(#clip-path)">
        <path id="Tracé_3508" data-name="Tracé 3508" d="M122.2,66.983V44h5.78V62.041H148.37v4.942Z" transform="translate(-91.492 -44)" fill="#fff"/>
      </g>
      <g id="Groupe_1436" data-name="Groupe 1436" clip-path="url(#clip-path)">
        <path id="Tracé_3509" data-name="Tracé 3509" d="M230.6,66.983V44h27.683v4.969h-21.9V53.1h19.283v4.456H236.38v4.483h22.767v4.942Z" transform="translate(-170.616 -44)" fill="#fff"/>
      </g>
      <g id="Groupe_1437" data-name="Groupe 1437" clip-path="url(#clip-path)">
        <path id="Tracé_3510" data-name="Tracé 3510" d="M347.9,66.983V44h27.683v4.969h-21.9V53.1h19.256v4.456H353.68v4.483h22.74v4.942Z" transform="translate(-256.236 -44)" fill="#fff"/>
      </g>
      <g id="Groupe_1438" data-name="Groupe 1438" clip-path="url(#clip-path)">
        <path id="Tracé_3511" data-name="Tracé 3511" d="M477.5,48.969V66.983h-5.753V48.969H461.4V44h26.44v4.969Z" transform="translate(-339.083 -44)" fill="#fff"/>
      </g>
      <g id="Groupe_1439" data-name="Groupe 1439" clip-path="url(#clip-path)">
        <path id="Tracé_3512" data-name="Tracé 3512" d="M230.1,555.566A2.582,2.582,0,0,1,232.666,553a2.566,2.566,0,0,1,0,5.131,2.582,2.582,0,0,1-2.566-2.566m.432,4.969h4.294v16.313h-4.294Z" transform="translate(-170.251 -415.531)" fill="#fff"/>
      </g>
      <g id="Groupe_1440" data-name="Groupe 1440" clip-path="url(#clip-path)">
        <path id="Tracé_3513" data-name="Tracé 3513" d="M277.207,576.061l1.647,1.918-3.349,2.728-1.783-2.134a12.448,12.448,0,0,1-5.374,1.161,11.668,11.668,0,1,1,0-23.335c6.833,0,11.8,4.861,11.8,11.667a11.639,11.639,0,0,1-2.944,7.994m-6.482-.972-2.566-3,3.349-2.7,2.566,3a7.974,7.974,0,0,0,1.161-4.294c0-4.213-2.7-7.4-6.86-7.4s-6.833,3.187-6.833,7.4c0,4.186,2.674,7.4,6.833,7.4a6.822,6.822,0,0,0,2.35-.405" transform="translate(-189.594 -418.013)" fill="#fff"/>
      </g>
      <g id="Groupe_1441" data-name="Groupe 1441" clip-path="url(#clip-path)">
        <path id="Tracé_3514" data-name="Tracé 3514" d="M349.839,573.056a9,9,0,0,0,6.212,2.566c2.728,0,4.267-1.188,4.267-2.917,0-1.837-1.377-2.674-4.537-2.674-.945,0-2.458,0-2.809.027v-4.186c.405.027,1.918.027,2.809.027,2.512,0,4.159-.81,4.159-2.512,0-1.782-1.837-2.7-4.186-2.7A8.137,8.137,0,0,0,350,563.063l-2.377-2.971a11.159,11.159,0,0,1,8.615-3.592c5.266,0,8.507,2.377,8.507,6.1,0,2.836-2.431,4.7-4.942,5.131,2.3.243,5.266,2.026,5.266,5.4,0,3.889-3.484,6.617-8.886,6.617-4.159,0-7.238-1.593-8.885-3.619Z" transform="translate(-255.798 -418.086)" fill="#fff"/>
      </g>
      <g id="Groupe_1442" data-name="Groupe 1442" clip-path="url(#clip-path)">
        <path id="Tracé_3515" data-name="Tracé 3515" d="M430.006,556.7a10.189,10.189,0,0,1,6.644,2.323l-2.161,3.619a5.957,5.957,0,0,0-4.483-1.783c-3.4,0-5.645,2.944-5.645,6.509a2.777,2.777,0,0,0,.027.486,7.35,7.35,0,0,1,5.78-2.863c4.051,0,7.67,2.458,7.67,7.265,0,4.4-3.484,7.724-8.615,7.724-6.86,0-9.723-5.266-9.723-11.613,0-6.86,3.835-11.667,10.506-11.667m-1.242,12.261a5.758,5.758,0,0,0-4.4,2.161c.216,2.161,1.431,4.672,4.591,4.672,2.593,0,4-1.755,4-3.349.027-2.458-1.972-3.484-4.186-3.484" transform="translate(-308.499 -418.232)" fill="#fff"/>
      </g>
      <g id="Groupe_1443" data-name="Groupe 1443" clip-path="url(#clip-path)">
        <path id="Tracé_3516" data-name="Tracé 3516" d="M501.718,556.7c6.428,0,9.318,5.807,9.318,11.613s-2.917,11.667-9.318,11.667c-6.455,0-9.318-5.834-9.318-11.667,0-5.807,2.863-11.613,9.318-11.613m0,4.24c-3.241,0-4.456,3.214-4.456,7.373s1.215,7.4,4.456,7.4c3.214,0,4.429-3.241,4.429-7.4s-1.215-7.373-4.429-7.373" transform="translate(-361.71 -418.232)" fill="#fff"/>
      </g>
      <g id="Groupe_1444" data-name="Groupe 1444" clip-path="url(#clip-path)">
        <path id="Tracé_3517" data-name="Tracé 3517" d="M18.78,453.342v5.239H37.82v4.942H18.78v7.859H13V448.4H40.413v4.942Z" transform="translate(-11.785 -339.181)" fill="#fff"/>
      </g>
      <g id="Groupe_1445" data-name="Groupe 1445" clip-path="url(#clip-path)">
        <path id="Tracé_3518" data-name="Tracé 3518" d="M126.7,471.383V448.4h5.78v18.014H152.87v4.969Z" transform="translate(-94.777 -339.181)" fill="#fff"/>
      </g>
      <g id="Groupe_1446" data-name="Groupe 1446" clip-path="url(#clip-path)">
        <path id="Tracé_3519" data-name="Tracé 3519" d="M235.1,471.383V448.4h27.71v4.942h-21.9v4.132h19.256v4.483H240.907v4.456h22.74v4.969Z" transform="translate(-173.901 -339.181)" fill="#fff"/>
      </g>
      <g id="Groupe_1447" data-name="Groupe 1447" clip-path="url(#clip-path)">
        <path id="Tracé_3520" data-name="Tracé 3520" d="M352.4,471.383V448.4h27.683v4.942h-21.9v4.132h19.283v4.483H358.18v4.456h22.74v4.969Z" transform="translate(-259.521 -339.181)" fill="#fff"/>
      </g>
      <g id="Groupe_1448" data-name="Groupe 1448" clip-path="url(#clip-path)">
        <path id="Tracé_3521" data-name="Tracé 3521" d="M482,453.342v18.041h-5.726V453.342H465.9V448.4h26.44v4.942Z" transform="translate(-342.367 -339.181)" fill="#fff"/>
      </g>
    </g>
    <g id="IQ">
      <path id="Tracé_3522" data-name="Tracé 3522" d="M212.9,150.212a2.512,2.512,0,1,1,2.512,2.512,2.542,2.542,0,0,1-2.512-2.512m.432,4.861h4.186v15.961h-4.186Z" transform="translate(-149.197 -75.693)" fill="#fab03f"/>
      <path id="Tracé_3523" data-name="Tracé 3523" d="M258.975,170.375l1.62,1.891-3.268,2.674-1.755-2.08a12.435,12.435,0,0,1-5.266,1.134,11.4,11.4,0,1,1,0-22.794c6.671,0,11.532,4.753,11.532,11.4a11.332,11.332,0,0,1-2.863,7.778m-6.347-.945-2.512-2.944,3.268-2.647,2.512,2.944a7.886,7.886,0,0,0,1.134-4.186c0-4.132-2.647-7.238-6.7-7.238-4.078,0-6.671,3.106-6.671,7.238,0,4.105,2.62,7.238,6.671,7.238A6.412,6.412,0,0,0,252.628,169.43Z" transform="translate(-168.102 -78.248)" fill="#fab03f"/>
    </g>
    <g id="_360" data-name="360">
      <path id="Tracé_3524" data-name="Tracé 3524" d="M329.912,167.3a8.749,8.749,0,0,0,6.077,2.512c2.674,0,4.159-1.161,4.159-2.836,0-1.783-1.35-2.62-4.429-2.62-.918,0-2.4,0-2.755.027v-4.105c.405.027,1.891.027,2.755.027,2.458,0,4.051-.783,4.051-2.458,0-1.755-1.783-2.647-4.105-2.647a8,8,0,0,0-5.618,2.323l-2.323-2.917a10.823,10.823,0,0,1,8.426-3.511c5.158,0,8.318,2.323,8.318,5.969,0,2.782-2.377,4.591-4.834,5.023,2.242.243,5.158,1.972,5.158,5.294,0,3.808-3.4,6.482-8.7,6.482-4.051,0-7.076-1.566-8.7-3.538l2.512-3.025" transform="translate(-232.773 -78.175)" fill="#26b8b6"/>
      <path id="Tracé_3525" data-name="Tracé 3525" d="M408.462,151.3a9.982,9.982,0,0,1,6.509,2.269l-2.107,3.538a5.854,5.854,0,0,0-4.4-1.755c-3.349,0-5.51,2.863-5.51,6.374a2.471,2.471,0,0,0,.027.459,7.154,7.154,0,0,1,5.645-2.809c3.97,0,7.508,2.4,7.508,7.1,0,4.294-3.4,7.562-8.426,7.562-6.7,0-9.507-5.158-9.507-11.37-.027-6.644,3.754-11.37,10.263-11.37m-1.188,11.991a5.7,5.7,0,0,0-4.294,2.107c.189,2.107,1.377,4.564,4.483,4.564,2.539,0,3.889-1.728,3.889-3.268C411.379,164.318,409.408,163.291,407.274,163.291Z" transform="translate(-284.451 -78.321)" fill="#26b8b6"/>
      <path id="Tracé_3526" data-name="Tracé 3526" d="M478.629,151.3c6.266,0,9.129,5.672,9.129,11.37s-2.836,11.4-9.129,11.4c-6.32,0-9.129-5.726-9.129-11.4s2.809-11.37,9.129-11.37m0,4.159c-3.187,0-4.375,3.133-4.375,7.211,0,4.051,1.188,7.238,4.375,7.238,3.133,0,4.321-3.16,4.321-7.238S481.761,155.459,478.629,155.459Z" transform="translate(-336.496 -78.321)" fill="#26b8b6"/>
    </g>
  </g>
</svg>
