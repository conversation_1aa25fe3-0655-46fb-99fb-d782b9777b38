using System;
using System.Collections.Generic;

namespace FleetXQFunctionService.Messages
{
    public class VehicleAccessCreationMessage
    {
        public Guid VehicleId { get; set; }
        public Guid CustomerId { get; set; }
        public Guid ModelId { get; set; }
        public Guid DepartmentId { get; set; }
        public Guid SiteId { get; set; }
        public bool IsNewVehicle { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public string IoTDevice { get; set; } = string.Empty;
        public int RetryCount { get; set; } = 0;
        public int MaxRetries { get; set; } = 5;

        // Properties for department change processing
        public bool IsDepartmentChange { get; set; } = false;
        public Guid? OldDepartmentId { get; set; }
        public Guid? OldSiteId { get; set; }

        // Permission levels to process (Master=1, NormalDriver=3)
        // If null or empty, defaults to both levels for backward compatibility
        public List<int> PermissionLevels { get; set; }
    }
}