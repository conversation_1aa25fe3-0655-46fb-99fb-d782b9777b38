﻿(function () {
    /**
     * Custom controller for Vehicle Calibration Report Page
     * Extends the base functionality with custom behavior for grid visibility and filtering
     * @param {object} controller - The base controller instance
     */
    FleetXQ.Web.Controllers.VehicleCalibrationReportPageControllerCustom = function (controller) {
        var self = this;
        this.controller = controller;

        /**
         * HACK: This is a temporary solution to hide "vehicle calibration value" grid column from customer users
         * as there is no easy way to hide grid columns in GO at the moment.
         * Controls the visibility of admin and customer grids based on user claims
         * Shows admin grid if user has no CustomerId (is admin)
         * Shows customer grid if user has CustomerId (is customer)
         */
        this.updateGridVisibility = function() {
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            var isCustomer = customerId != null;
            
            // Control visibility of admin grid (visible when not a customer)
            if (self.controller.AllVehicleCalibrationStoreProcedureGridViewModel) {
                self.controller.AllVehicleCalibrationStoreProcedureGridViewModel.StatusData.IsVisible(!isCustomer);
            }
            
            // Control visibility of customer grid (visible when is a customer)
            if (self.controller.AllVehicleCalibrationStoreProcedureGridCustomerViewModel) {
                self.controller.AllVehicleCalibrationStoreProcedureGridCustomerViewModel.StatusData.IsVisible(isCustomer);
            }
        };

        /**
         * Gets default configuration for filtering based on user claims
         * Applies CustomerId and SiteId filters if available
         * @returns {object} Configuration object with filter predicates and parameters
         */
        this.getDefaultConfiguration = function () {
            var configuration = {};
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            var AllowedSiteIds = self.controller.applicationController.viewModel.security.currentUserClaims().AllowedSiteIds;
            // Remove the curly braces and spaces, then split the string into an array
            var array = AllowedSiteIds.replace(/[{} ]/g, '').split(',');
            // Get the first element of the array, if empty null
            var siteId = array[0] || null;
    
            var parameterCount = 0;
        
            // Add CustomerId filter if available
            if (customerId != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters = configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += 'CustomerId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + customerId + '" }';
            }
        
            // Add SiteId filter if available
            if (siteId != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters = configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += 'SiteId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + siteId + '" }';
            }

            if (configuration.filterParameters) {
                configuration.filterParameters = '[' + configuration.filterParameters + ']';
            }
            return configuration;
        };

        /**
         * Gets configuration based on current filter form values
         * Falls back to default configuration if no CustomerId is selected for a customer user
         * @returns {object} Configuration object with filter predicates and parameters
         */
        this.getConfiguration = function () {
            var configuration = {};
            configuration.filterPredicate = '';
            configuration.filterParameters = [];
            var currentData = self.controller.AllVehicleCalibrationFilterFormViewModel.CurrentObject().Data;

            var parameterIndex = 0; // Start indexing after the initial three parameters.

            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            if (currentData.CustomerId() == null && customerId != null) {
                return self.getDefaultConfiguration();
            }

            if (currentData.CustomerId() != null) {
                configuration.filterPredicate += `CustomerId == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Guid", "IsNullable": false, "Value": currentData.CustomerId() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.SiteId() != null) {
                configuration.filterPredicate += `SiteId == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Guid", "IsNullable": false, "Value": currentData.SiteId() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.DepartmentId() != null) {
                configuration.filterPredicate += `DepartmentId == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Guid", "IsNullable": false, "Value": currentData.DepartmentId() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            // Convert configuration.filterParameters array to a JSON string.
            configuration.filterParameters = JSON.stringify(configuration.filterParameters);

            return configuration;
        };

        /**
         * Adds multi-search filter to existing configuration
         * @param {object} configuration - Existing filter configuration
         * @returns {object} Updated configuration with multi-search filter
         */
        this.addMultiSearchFilter = function (configuration) {
            var updatedConfiguration = configuration;
            var currentData = self.controller.AllVehicleCalibrationFilterFormViewModel.CurrentObject().Data;
            if (currentData.MultiSearch()) {
                if (updatedConfiguration) {
                    var filterParameters = JSON.parse(updatedConfiguration.filterParameters);
                    updatedConfiguration.filterPredicate = updatedConfiguration.filterPredicate ? 
                        updatedConfiguration.filterPredicate + ' && MultiSearch == @' + filterParameters.length :
                        'MultiSearch == @' + filterParameters.length;
                    filterParameters.push({ "TypeName": "System.String", "IsNullable": true, "Value": currentData.MultiSearch() });
                    updatedConfiguration.filterParameters = JSON.stringify(filterParameters);
                    return updatedConfiguration;
                } else {
                    // Create new configuration if none exists
                    return {
                        filterPredicate: 'MultiSearch == @0',
                        filterParameters: JSON.stringify([{ 
                            "TypeName": "System.String", 
                            "IsNullable": true, 
                            "Value": currentData.MultiSearch() 
                        }])
                    };
                }
            }
            return updatedConfiguration;
        };
     
        /**
         * Loads page data with current configuration and multi-search filter
         */
        this.loadPageData = function () {
            var configuration = this.getConfiguration();
            // Add the MultiSearch filter to the configuration.
            configuration = this.addMultiSearchFilter(configuration);

            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            var isCustomer = customerId != null;

            // For Admin User Grid
            if (!isCustomer) {
                self.controller.AllVehicleCalibrationStoreProcedureGridViewModel.exportFilterPredicate = configuration.filterPredicate;
                self.controller.AllVehicleCalibrationStoreProcedureGridViewModel.exportFilterParameters = configuration.filterParameters;
                
                // Subscribe to the CollectionLoaded event to trigger sort
                var subscription = self.controller.AllVehicleCalibrationStoreProcedureGridViewModel.Events.CollectionLoaded.subscribe(function() {
                    self.controller.AllVehicleCalibrationStoreProcedureGridViewModel.sortingOptions({
                        columnName: "Vehicle.Module.CalibrationDate",
                        order: "desc"
                    });
                    subscription.dispose(); // Unsubscribe after first load
                });
                
                self.controller.AllVehicleCalibrationStoreProcedureGridViewModel.LoadAllVehicleCalibrationStoreProcedureObjectCollection(configuration);
            }
            // For Customer User Grid
            if (isCustomer) {
                self.controller.AllVehicleCalibrationStoreProcedureGridCustomerViewModel.exportFilterPredicate = configuration.filterPredicate;
                self.controller.AllVehicleCalibrationStoreProcedureGridCustomerViewModel.exportFilterParameters = configuration.filterParameters;
                
                // Subscribe to the CollectionLoaded event to trigger sort
                var subscription = self.controller.AllVehicleCalibrationStoreProcedureGridCustomerViewModel.Events.CollectionLoaded.subscribe(function() {
                    self.controller.AllVehicleCalibrationStoreProcedureGridCustomerViewModel.sortingOptions({
                        columnName: "Vehicle.Module.CalibrationDate",
                        order: "desc"
                    });
                    subscription.dispose(); // Unsubscribe after first load
                });
                
                self.controller.AllVehicleCalibrationStoreProcedureGridCustomerViewModel.LoadAllVehicleCalibrationStoreProcedureObjectCollection(configuration);
            }
        };

        /**
         * Loads initial grid data based on user claims
         */
        // this.loadInitialGridData = function () {
        //     var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
        //     if (!GO.Filter.hasUrlFilter(self.controller.AllVehicleCalibrationStoreProcedureGridViewModel.FILTER_NAME, self.controller.AllVehicleCalibrationStoreProcedureGridViewModel)) {
        //         if (customerId != null) {
        //             var configuration = this.getConfiguration();
        //             self.controller.AllVehicleCalibrationStoreProcedureGridViewModel.LoadAllVehicleCalibrationStoreProcedureObjectCollection(configuration);
        //             return;
        //         }
        //         self.controller.AllVehicleCalibrationStoreProcedureGridViewModel.LoadAllVehicleCalibrationStoreProcedureObjectCollection();
        //     }
        // }

        /**
         * Initializes the controller
         * Sets up event handlers, subscriptions, and initial grid visibility
         */
        this.initialize = function () {
            // Disable edit mode confirmation
            self.controller.IsInEditMode = function () {
                return false;
            }

            // Initial visibility update
            self.updateGridVisibility();

            if (!sessionStorage.getItem('hasReloaded')) {
                // Set the flag before reloading
                sessionStorage.setItem('hasReloaded', 'true');
                
                // Force a reload after a brief delay to ensure hash is set
                window.location.reload();
            } else {
                // Clear the flag for next time
                sessionStorage.removeItem('hasReloaded');
            }

            // Set up filter data callback
            self.controller.AllVehicleCalibrationFilterFormViewModel.filterData = function () {
                // Check if user is DealerAdmin
                var userRole = self.controller.applicationController.viewModel.security.currentUserClaims().role;
                if (userRole === 'DealerAdmin') {
                    // For DealerAdmin, verify that a customer is selected
                    var customerId = self.controller.AllVehicleCalibrationFilterFormViewModel.CurrentObject().Data.CustomerId();
                    if (!customerId) {
                        self.controller.AllVehicleCalibrationFilterFormViewModel.ShowError('Please select a customer');
                        return;
                    }
                }
                
                self.loadPageData();
            };

            // self.loadInitialGridData();
        };
    };
})();