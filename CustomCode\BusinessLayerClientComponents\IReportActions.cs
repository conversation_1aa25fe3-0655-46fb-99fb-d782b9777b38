﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using GenerativeObjects.Practices.Commands;

namespace FleetXQ.BusinessLayer.Components.Client
{
    /// <summary>
	/// ReportActions Component
	///  
	/// </summary>
	public interface IReportActions : IModelBase 
    {
		/// <summary>
		/// Email Method
		///  
      /// </summary>
		/// <param name="email"></param>
        /// <returns></returns>		
		/// <param name="reportType"></param>
        /// <returns></returns>		
		/// <param name="filterPredicate"></param>
        /// <returns></returns>		
		/// <param name="filterParameters"></param>
        /// <returns></returns>		
		System.Threading.Tasks.Task<ComponentResponse<System.Boolean>> EmailAsync(EmailContainer email, System.Int32 reportType, System.String filterPredicate, System.String filterParameters, Dictionary<string, object> parameters = null);
		
		/// <summary>
		/// Print Method
		///  
      /// </summary>
		System.Threading.Tasks.Task<ComponentResponse<System.Boolean>> PrintAsync(Dictionary<string, object> parameters = null);
		
		/// <summary>
		/// Subscribe Method
		///  
      /// </summary>
		/// <param name="reportType"></param>
        /// <returns></returns>		
		/// <param name="reportSubscription"></param>
        /// <returns></returns>		
		System.Threading.Tasks.Task<ComponentResponse<ReportSubscriptionContainer>> SubscribeAsync(System.Int32 reportType, ReportSubscriptionContainer reportSubscription, Dictionary<string, object> parameters = null);
		
	}
}
