﻿(function () {

    FleetXQ.Web.Controllers.SynchronizationStatusReportPageControllerCustom = function (controller) {
        var self = this;
        this.controller = controller;
        this.IoTHubManagerProxy = new FleetXQ.Web.Model.Components.IoTHubManagerProxy(this.ObjectsDataSet);

        this.getDefaultConfiguration = function () {
            var configuration = {};
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            var AllowedSiteIds = self.controller.applicationController.viewModel.security.currentUserClaims().AllowedSiteIds;
            // Remove the curly braces and spaces, then split the string into an array
            var array = AllowedSiteIds.replace(/[{} ]/g, '').split(',');
            // Get the first element of the array, if empty null
            var siteId = array[0] || null;
    
        
            var parameterCount = 0;
        
            if (customerId != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += 'Vehicle.Department.Site.CustomerId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + customerId + '" }';
            }
        
            if (siteId != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += 'Vehicle.Department.SiteId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + siteId + '" }';
            }

            if (configuration.filterParameters) {
                configuration.filterParameters = '[' + configuration.filterParameters + ']';
            }
            return configuration;
        };

        this.getConfiguration = function () {
            var configuration = {};
            configuration.filterPredicate = '';
            configuration.filterParameters = [];
            var currentData = self.controller.SynchronizationStatusReportFilterFormViewModel.CurrentObject().Data;

            var parameterIndex = 0; // Start indexing after the initial three parameters.

            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            if (currentData.CustomerId() == null && customerId != null) {
                return self.getDefaultConfiguration();
            }

            if (currentData.CustomerId() != null) {
                configuration.filterPredicate += `Vehicle.Department.Site.CustomerId == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Guid", "IsNullable": false, "Value": currentData.CustomerId() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.SiteId() != null) {
                configuration.filterPredicate += `Vehicle.Department.SiteId == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Guid", "IsNullable": false, "Value": currentData.SiteId() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.DepartmentId() != null) {
                configuration.filterPredicate += `Vehicle.DepartmentId == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Guid", "IsNullable": false, "Value": currentData.DepartmentId() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.StartDate() != null) {
                // Add StartDate Year, Month, and Day predicates and parameters.
                configuration.filterPredicate += ` && StartDate == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.DateTime", "IsNullable": false, "Value": currentData.StartDate() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.EndDate() != null) {
                // Add StartDate Year, Month, and Day predicates and parameters.
                configuration.filterPredicate += ` && EndDate == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.DateTime", "IsNullable": false, "Value": currentData.EndDate() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            // Convert configuration.filterParameters array to a JSON string.
            configuration.filterParameters = JSON.stringify(configuration.filterParameters);

            return configuration;
        };

        this.addMultiSearchFilter = function (configuration) {
            var updatedConfiguration = configuration;
            var currentData = self.controller.SynchronizationStatusReportFilterFormViewModel.CurrentObject().Data;
            if (currentData.MultiSearch()) {
                if (updatedConfiguration) {
                    var filterParameters = JSON.parse(updatedConfiguration.filterParameters);
                    updatedConfiguration.filterPredicate = updatedConfiguration.filterPredicate ? 
                        updatedConfiguration.filterPredicate + ' && MultiSearch == @' + filterParameters.length :
                        'MultiSearch == @' + filterParameters.length;
                    filterParameters.push({ "TypeName": "System.String", "IsNullable": true, "Value": currentData.MultiSearch() });
                    updatedConfiguration.filterParameters = JSON.stringify(filterParameters);
                    return updatedConfiguration;
                } else {
                    // Create new configuration if none exists
                    return {
                        filterPredicate: 'MultiSearch == @0',
                        filterParameters: JSON.stringify([{ 
                            "TypeName": "System.String", 
                            "IsNullable": true, 
                            "Value": currentData.MultiSearch() 
                        }])
                    };
                }
            }
            return updatedConfiguration;
        };
     
        /**
         * Helper method to update IoT device last activity time
         * This method queries the IoT Hub to get the last activity time of all devices
         * based on the current filter criteria (customer, site, department)
         * @returns {Promise} - Promise that resolves when the update is complete
         */
        this.updateIoTDeviceLastActivityTime = async function() {
            // Set busy indicators to show loading state
            self.controller.SynchronizationStatusReportFilterFormViewModel.StatusData.IsBusy(true);
            self.controller.AllMessageHistoryStoreProcedureGridViewModel.StatusData.IsBusy(true);
            
            // Create configuration object for the API call
            var configuration = {};
            
            // Get current filter values from the filter form
            var currentData = self.controller.SynchronizationStatusReportFilterFormViewModel.CurrentObject().Data;
            
            // Use filter values if available, otherwise fall back to user claims or empty GUID
            // Priority: Filter value > User claim > Empty GUID
            var customerId = currentData.CustomerId() || 
                self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId || 
                "00000000-0000-0000-0000-000000000000";
                
            var siteId = currentData.SiteId() || "00000000-0000-0000-0000-000000000000";
            var departmentId = currentData.DepartmentId() || "00000000-0000-0000-0000-000000000000";
            
            // Add IDs to configuration object
            configuration.customerId = customerId;
            configuration.siteId = siteId;
            configuration.departmentId = departmentId;
            
            // Success handler - clears busy indicators when complete
            configuration.successHandler = function (result) {
                self.controller.SynchronizationStatusReportFilterFormViewModel.StatusData.IsBusy(false);
                self.controller.AllMessageHistoryStoreProcedureGridViewModel.StatusData.IsBusy(false);

                // Then load grid data with updated activity times
                self.loadPageData();
            };
            
            // Error handler - shows error popup and clears busy indicators
            configuration.errorHandler = function () {
                self.controller.applicationController.showAlertPopup(
                    self.controller, 
                    "Failed to update vehicle last activity time", 
                    "Error", 
                    null, 
                    self.controller.contextId
                );
                self.controller.SynchronizationStatusReportFilterFormViewModel.StatusData.IsBusy(false);
                self.controller.AllMessageHistoryStoreProcedureGridViewModel.StatusData.IsBusy(false);
            }; 
            
            // Call the API to update device last activity time
            return self.IoTHubManagerProxy.GetAllDeviceTwinsLastActiveTime(configuration);
        };

        this.loadPageData = function () {
            var configuration = this.getConfiguration();
            // Add the MultiSearch filter to the configuration.
            configuration = this.addMultiSearchFilter(configuration);

            self.controller.AllMessageHistoryStoreProcedureGridViewModel.exportFilterPredicate = configuration.filterPredicate;
            self.controller.AllMessageHistoryStoreProcedureGridViewModel.exportFilterParameters = configuration.filterParameters;
            self.controller.AllMessageHistoryStoreProcedureGridViewModel.LoadAllMessageHistoryStoreProcedureObjectCollection(configuration);
        };

        this.loadInitialGridData = function () {
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            if (!GO.Filter.hasUrlFilter(self.controller.AllMessageHistoryStoreProcedureGridViewModel.FILTER_NAME, self.controller.AllMessageHistoryStoreProcedureGridViewModel)) {
                if (customerId != null) {
                    var configuration = this.getConfiguration();
                    self.controller.AllMessageHistoryStoreProcedureGridViewModel.LoadAllMessageHistoryStoreProcedureObjectCollection(configuration);
                    return;
                }
				self.controller.AllMessageHistoryStoreProcedureGridViewModel.LoadAllMessageHistoryStoreProcedureObjectCollection();
			}
        }

        this.initialize = async function () {
            // to avoid to have the message asking to confirm changing page and lose changes (caused at the moment by the dashboard filter that is a view in edit mode)
            self.controller.IsInEditMode = function () {
                return false;
            }

            if (!sessionStorage.getItem('hasReloaded')) {
                // Set the flag before reloading
                sessionStorage.setItem('hasReloaded', 'true');
                
                // Force a reload after a brief delay to ensure hash is set
                window.location.reload();
            } else {
                // Clear the flag for next time
                sessionStorage.removeItem('hasReloaded');
            }

            // Modified filterData function to update IoT device last activity time before loading grid data
            self.controller.SynchronizationStatusReportFilterFormViewModel.filterData = async function () {
                // Check if the user is a DealerAdmin
                var userRole = self.controller.applicationController.viewModel.security.currentUserClaims().role;
                
                if (userRole === 'DealerAdmin') {
                    // Get the customer ID from the form
                    var currentObject = self.controller.SynchronizationStatusReportFilterFormViewModel.CurrentObject();
                    var customerId = currentObject.Data.CustomerId();
                    
                    // If no customer is selected, show an error and return
                    if (!customerId || customerId === '') {
                        self.controller.SynchronizationStatusReportFilterFormViewModel.ShowError('Please select a customer');
                        return;
                    }
                }
                
                try {
                    // First update IoT device last activity time
                    await self.updateIoTDeviceLastActivityTime();
                } catch (error) {
                    console.error("Error in filterData:", error);
                    // Continue with data loading even if IoT update fails
                    self.loadPageData();
                }
            };
        };
    };

})();