﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net7.0</TargetFramework>
  </PropertyGroup>
  <ItemGroup>
    <None Remove="testhost.runtimeconfig.json" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="testhost.runtimeconfig.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="GenerativeObjects.Infrastructure.Database" Version="2.0.3" />
    <PackageReference Include="GenerativeObjects.Practices.LayerSupportClasses" Version="2.0.3" />
    <PackageReference Include="Moq" Version="4.20.72" />
    <PackageReference Include="NFluent" Version="3.0.3" />
    <PackageReference Include="NSubstitute" Version="5.1.0" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.7.2" />
    <PackageReference Include="NUnit" Version="4.1.0" />
    <PackageReference Include="NUnit3TestAdapter" Version="4.5.0" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\GeneratedCode\ServiceLayer\FleetXQ.ServiceLayer.csproj" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Cypress\node_modules\.bin\cypress" />
    <None Include="Cypress\node_modules\.bin\cypress.cmd" />
    <None Include="Cypress\node_modules\.bin\cypress.ps1" />
    <None Include="Cypress\node_modules\.bin\extract-zip" />
    <None Include="Cypress\node_modules\.bin\extract-zip.cmd" />
    <None Include="Cypress\node_modules\.bin\extract-zip.ps1" />
    <None Include="Cypress\node_modules\.bin\is-ci" />
    <None Include="Cypress\node_modules\.bin\is-ci.cmd" />
    <None Include="Cypress\node_modules\.bin\is-ci.ps1" />
    <None Include="Cypress\node_modules\.bin\node-which" />
    <None Include="Cypress\node_modules\.bin\node-which.cmd" />
    <None Include="Cypress\node_modules\.bin\node-which.ps1" />
    <None Include="Cypress\node_modules\.bin\semver" />
    <None Include="Cypress\node_modules\.bin\semver.cmd" />
    <None Include="Cypress\node_modules\.bin\semver.ps1" />
    <None Include="Cypress\node_modules\.bin\sshpk-conv" />
    <None Include="Cypress\node_modules\.bin\sshpk-conv.cmd" />
    <None Include="Cypress\node_modules\.bin\sshpk-conv.ps1" />
    <None Include="Cypress\node_modules\.bin\sshpk-sign" />
    <None Include="Cypress\node_modules\.bin\sshpk-sign.cmd" />
    <None Include="Cypress\node_modules\.bin\sshpk-sign.ps1" />
    <None Include="Cypress\node_modules\.bin\sshpk-verify" />
    <None Include="Cypress\node_modules\.bin\sshpk-verify.cmd" />
    <None Include="Cypress\node_modules\.bin\sshpk-verify.ps1" />
    <None Include="Cypress\node_modules\.bin\uuid" />
    <None Include="Cypress\node_modules\.bin\uuid.cmd" />
    <None Include="Cypress\node_modules\.bin\uuid.ps1" />
    <None Include="Cypress\node_modules\call-bind\.github\FUNDING.yml" />
    <None Include="Cypress\node_modules\define-data-property\.github\FUNDING.yml" />
    <None Include="Cypress\node_modules\es-define-property\.github\FUNDING.yml" />
    <None Include="Cypress\node_modules\es-errors\.github\FUNDING.yml" />
    <None Include="Cypress\node_modules\function-bind\.github\FUNDING.yml" />
    <None Include="Cypress\node_modules\function-bind\.github\SECURITY.md" />
    <None Include="Cypress\node_modules\get-intrinsic\.github\FUNDING.yml" />
    <None Include="Cypress\node_modules\gopd\.github\FUNDING.yml" />
    <None Include="Cypress\node_modules\has-property-descriptors\.github\FUNDING.yml" />
    <None Include="Cypress\node_modules\has-proto\.github\FUNDING.yml" />
    <None Include="Cypress\node_modules\has-symbols\.github\FUNDING.yml" />
    <None Include="Cypress\node_modules\hasown\.github\FUNDING.yml" />
    <None Include="Cypress\node_modules\minimist\.github\FUNDING.yml" />
    <None Include="Cypress\node_modules\object-inspect\.github\FUNDING.yml" />
    <None Include="Cypress\node_modules\pump\.github\FUNDING.yml" />
    <None Include="Cypress\node_modules\qs\.github\FUNDING.yml" />
    <None Include="Cypress\node_modules\rfdc\.github\workflows\ci.yml" />
    <None Include="Cypress\node_modules\set-function-length\.github\FUNDING.yml" />
    <None Include="Cypress\node_modules\side-channel\.github\FUNDING.yml" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Cypress\cypress\downloads\" />
  </ItemGroup>

</Project>
