using Azure.Messaging.ServiceBus;
using FleetXQ.Data.DataObjects;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Text.Json;
using System.Threading.Tasks;

namespace FleetXQ.BusinessLayer.Components.Server.Custom
{
    public interface IVehicleAccessQueueService
    {
        Task SendVehicleAccessCreationMessageAsync(VehicleAccessCreationMessage message);
    }

    public class VehicleAccessQueueService : IVehicleAccessQueueService
    {
        private readonly ServiceBusClient _serviceBusClient;
        private readonly ServiceBusSender _sender;
        private readonly ILogger<VehicleAccessQueueService> _logger;
        private readonly string _queueName;

        public VehicleAccessQueueService(IConfiguration configuration, ILogger<VehicleAccessQueueService> logger)
        {
            _logger = logger;

            var connectionString = configuration.GetConnectionString("ServiceBus");
            _queueName = configuration.GetValue<string>("ServiceBus:VehicleAccessQueue", "vehicle-access-creation");

            // if (string.IsNullOrEmpty(connectionString))
            // {
            //     throw new InvalidOperationException("ServiceBus connection string is not configured");
            // }

            // _serviceBusClient = new ServiceBusClient(connectionString);
            // _sender = _serviceBusClient.CreateSender(_queueName);
        }

        public async Task SendVehicleAccessCreationMessageAsync(VehicleAccessCreationMessage message)
        {
            try
            {
                var messageJson = JsonSerializer.Serialize(message);
                var serviceBusMessage = new ServiceBusMessage(messageJson)
                {
                    MessageId = Guid.NewGuid().ToString(),
                    SessionId = message.VehicleId.ToString(), // Use vehicle ID as session ID for ordered processing
                    Subject = "VehicleAccessCreation",
                    TimeToLive = TimeSpan.FromHours(24) // Message expires after 24 hours
                };

                // Add custom properties for easier filtering and monitoring
                serviceBusMessage.ApplicationProperties["VehicleId"] = message.VehicleId.ToString();
                serviceBusMessage.ApplicationProperties["CustomerId"] = message.CustomerId.ToString();
                serviceBusMessage.ApplicationProperties["IsNewVehicle"] = message.IsNewVehicle;
                serviceBusMessage.ApplicationProperties["IsDepartmentChange"] = message.IsDepartmentChange;
                serviceBusMessage.ApplicationProperties["CreatedAt"] = message.CreatedAt.ToString("O");

                if (message.OldDepartmentId.HasValue)
                {
                    serviceBusMessage.ApplicationProperties["OldDepartmentId"] = message.OldDepartmentId.Value.ToString();
                }

                if (message.OldSiteId.HasValue)
                {
                    serviceBusMessage.ApplicationProperties["OldSiteId"] = message.OldSiteId.Value.ToString();
                }

                await _sender.SendMessageAsync(serviceBusMessage);

                var messageType = message.IsDepartmentChange ? "department change" :
                                 message.IsNewVehicle ? "new vehicle" : "vehicle access update";
                _logger.LogInformation("[PERF] Vehicle access {MessageType} message sent to queue for vehicle {VehicleId}", messageType, message.VehicleId);
            }
            catch (Exception ex)
            {
                var messageType = message.IsDepartmentChange ? "department change" :
                                 message.IsNewVehicle ? "new vehicle" : "vehicle access update";
                _logger.LogError(ex, "[PERF] Failed to send vehicle access {MessageType} message to queue for vehicle {VehicleId}", messageType, message.VehicleId);
                throw;
            }
        }

        public async ValueTask DisposeAsync()
        {
            if (_sender != null)
            {
                await _sender.DisposeAsync();
            }

            if (_serviceBusClient != null)
            {
                await _serviceBusClient.DisposeAsync();
            }
        }
    }
}