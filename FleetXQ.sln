﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.5.33424.131
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "05. Data Layer", "05. Data Layer", "{9C192A7E-5097-40F6-82F3-915D95833C20}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "04. Business Layer", "04. Business Layer", "{1C018E27-60E0-4173-A638-1E3E549B9DEA}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "06. Features", "06. Features", "{3CFA64F5-A5FF-4EDF-8E98-33A5B4E5F8C1}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "02. SDK Layer", "02. SDK Layer", "{88854C45-A97A-443B-982A-FBC3E14F1B8D}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "03. Service Layer", "03. Service Layer", "{D0877E9B-E4D7-4FEE-9200-84E6D981016B}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "01. Web Application Layer", "01. Web Application Layer", "{DB14448E-E055-49C5-973A-D730FD4FDC68}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "C. External Scripts", "C. External Scripts", "{8ECF8DAB-1EB4-4CD1-B744-9229135A6149}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "T1. Test Layer", "T1. Test Layer", "{E5542C2D-FAAE-42BB-9D1E-87DDC61E2F04}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "FleetXQ.Data.Resources", "GeneratedCode\Resources\FleetXQ.Data.Resources.csproj", "{1F058464-80EF-42BF-84A1-F13F3FC219A4}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "FleetXQ.Data.DataProviders", "GeneratedCode\DataLayerDataProviders\FleetXQ.Data.DataProviders.csproj", "{17BF5F1E-BD48-4325-89A3-2380393D0A64}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "FleetXQ.Data.DeleteHandlers", "GeneratedCode\DataLayerDeleteHandlers\FleetXQ.Data.DeleteHandlers.csproj", "{6949431E-3219-4B5B-8061-324EEC796E53}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "FleetXQ.BusinessLayer", "GeneratedCode\BusinessLayer\FleetXQ.BusinessLayer.csproj", "{309755D3-515C-4C79-915A-37B17FB51841}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "FleetXQ.BusinessLayer.Components.Server", "GeneratedCode\BusinessLayerServerComponents\FleetXQ.BusinessLayer.Components.Server.csproj", "{D36F2F9C-64FD-4DBC-B649-29FC9514B089}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "FleetXQ.BusinessLayer.Components.Server.Extensions", "GeneratedCode\BusinessLayerServerComponentsExtensions\FleetXQ.BusinessLayer.Components.Server.Extensions.csproj", "{07073E76-4064-41F7-B079-C76E3C703165}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "FleetXQ.Features.Security.Common", "GeneratedCode\Features\Security\Common\FleetXQ.Features.Security.Common.csproj", "{D3059D42-A112-46DF-9E86-F41722B7A1BD}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "FleetXQ.Features.Security.DataLayer", "GeneratedCode\Features\Security\DataLayer\FleetXQ.Features.Security.DataLayer.csproj", "{F7F77C21-97A2-4DE7-B175-39BA3F67E1D0}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "FleetXQ.Features.Security.ServiceLayer", "GeneratedCode\Features\Security\ServiceLayer\FleetXQ.Features.Security.ServiceLayer.csproj", "{131AF5A5-9109-4451-BDDC-562550F46DA5}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "FleetXQ.Features.Security.BusinessLayer", "GeneratedCode\Features\Security\BusinessLayer\FleetXQ.Features.Security.BusinessLayer.csproj", "{5A6A1DD1-5FF5-41B7-B983-295B8E9EBA79}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "FleetXQ.Client.Model", "GeneratedCode\ModelLayer\FleetXQ.Client.Model.csproj", "{A10C40F5-4963-436B-BD91-C90E6AF5CDDA}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "FleetXQ.ServiceLayer", "GeneratedCode\ServiceLayer\FleetXQ.ServiceLayer.csproj", "{99343A8B-BBF7-4E34-97A6-11BABBD2B6A3}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "FleetXQ.Application.Web", "GeneratedCode\WebApplicationLayer\FleetXQ.Application.Web.csproj", "{902A89D7-A446-4723-9953-FD2ABC6978A8}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "FleetXQ.Application.Web.Custom", "CustomCode\WebApplicationLayer\FleetXQ.Application.Web.Custom.csproj", "{075A1C4D-7724-42DA-8409-21810F6B602C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "FleetXQ.ConstructViews", "GeneratedCode\ConstructViews\FleetXQ.ConstructViews.csproj", "{9DCE8806-AD07-44CE-AE2F-D8F2652300DA}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "FleetXQ.Data.DataObjects", "GeneratedCode\DataLayer\FleetXQ.Data.DataObjects.csproj", "{59BE3C01-B51C-4120-AF21-862AD18AFE54}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "FleetXQ.BusinessLayer.ORMSupportClasses", "GeneratedCode\BusinessLayerORMSupportClasses\FleetXQ.BusinessLayer.ORMSupportClasses.csproj", "{C56783C5-2204-41B8-A668-89E25D778E3C}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "A. Generated code", "A. Generated code", "{D7F12D4A-2924-48D1-8D1C-D777AE0CFB03}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "B. Custom code", "B. Custom code", "{375A9C44-2D9D-4680-BFB4-9E0081EC3068}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "01. Web Application Layer", "01. Web Application Layer", "{85AE7BD8-6D59-47A4-BF98-A7973A669F2F}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "02. SDK Layer", "02. SDK Layer", "{5BB53210-A130-4699-8958-377CF6191248}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "03. Service Layer", "03. Service Layer", "{94EFCF9F-5B4A-4AC1-A4AC-945EEA56D961}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "04. Business Layer", "04. Business Layer", "{8A1E421A-36FF-4782-AAA6-62E55DC7E518}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "05. Data Layer", "05. Data Layer", "{726130F0-89A7-4632-B586-46B9A9E1603B}"
	ProjectSection(SolutionItems) = preProject
		PredicateParser.cs = PredicateParser.cs
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "06. Features", "06. Features", "{6EBF3583-8F26-4223-9FDE-630B3DBB673A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "FleetXQ.Data.DataProviders.Custom", "CustomCode\DataLayerDataProviders\FleetXQ.Data.DataProviders.Custom.csproj", "{A149C021-15AA-4435-821B-574D84342A67}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "FleetXQ.BusinessLayer.Components.Client.Custom", "CustomCode\BusinessLayerClientComponents\FleetXQ.BusinessLayer.Components.Client.Custom.csproj", "{742CD19D-6A4C-47AC-BA08-6530E63F8EF6}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "FleetXQ.BusinessLayer.Components.Server.Custom", "CustomCode\BusinessLayerServerComponents\FleetXQ.BusinessLayer.Components.Server.Custom.csproj", "{EF750C94-7F93-4DE8-AA22-62BD86E656AE}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "FleetXQ.Data.DataObjects.Custom", "CustomCode\DataLayer\FleetXQ.Data.DataObjects.Custom.csproj", "{2B76B015-6B96-4A6E-B38C-08652D1B8204}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "FleetXQ.ServiceLayer.Custom", "CustomCode\ServiceLayer\FleetXQ.ServiceLayer.Custom.csproj", "{A9A6DA1C-826D-498F-BB4E-65F1498461FA}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "FleetXQ.Client.Model.Custom", "CustomCode\ModelLayer\FleetXQ.Client.Model.Custom.csproj", "{A62EEFDA-95DC-42CB-8771-1B87A8C6E587}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "FleetXQ.Data.DataProvidersExtensions.Custom", "CustomCode\DataLayerDataProviderExtensions\FleetXQ.Data.DataProvidersExtensions.Custom.csproj", "{D2D6446A-802B-4A2A-9FF9-CE3BD3BC1147}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "FleetXQ.BusinessLayer.Components.Server.Extensions.Interfaces", "GeneratedCode\BusinessLayerServerComponentsExtensionsInterfaces\FleetXQ.BusinessLayer.Components.Server.Extensions.Interfaces.csproj", "{6779FC90-1B5C-417B-828D-1B4E6A40D13A}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Tests", "Tests", "{8B074695-2364-4BD7-83C8-B8B96CF8B3EE}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "FleetXQ.BusinessLayer.Components.Server.Custom.Tests", "CustomCode\BusinessLayerServerComponentsTests\FleetXQ.BusinessLayer.Components.Server.Custom.Tests\FleetXQ.BusinessLayer.Components.Server.Custom.Tests.csproj", "{F532CD10-0E36-442F-8366-38E5B5BCB790}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "07. Tests", "07. Tests", "{8D7CF4A1-75F2-4AFF-93AF-12E56D565E24}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "FleetXQ.Tests.Common", "CustomCode\TestsLayer\FleetXQ.Tests.Common.csproj", "{0A80AF3A-CDCA-4CBD-B59B-10F2A6222F15}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "DailyTaskRunner", "DailyTaskRunner", "{067445AE-E72B-43D1-A10A-765BDFF96D4B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DailyTaskRunner", "DailyTaskRunner\DailyTaskRunner\DailyTaskRunner.csproj", "{F219E3B3-8F79-47A2-8010-67326409B524}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "FleetXQ.Features.ChangeTracking.BusinessLayer", "GeneratedCode\Features\ChangeTracking\BusinessLayer\FleetXQ.Features.ChangeTracking.BusinessLayer.csproj", "{C629A886-576E-4153-83C0-45BCD0C1C1BA}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "LocaleTranslator", "LocaleTranslator", "{02EA681E-C7D8-13C7-8484-4AC65E1B71E8}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LocaleTranslator", "LocaleTranslator\LocaleTranslator.csproj", "{B4DF129B-43C7-790A-CFC1-C1E67AA790DA}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LocaleTranslator.Tests", "LocaleTranslator.Tests\LocaleTranslator.Tests.csproj", "{6FDAEEBB-B2E9-4738-9007-D999EAAD4293}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "CustomCode", "CustomCode", "{7EADBF07-B5DE-4DB1-A011-AC26B6AD6F14}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "UnitTests.Tests", "CustomCode\UnitTests\UnitTests.Tests.csproj", "{F963E3FB-06A5-426C-BF41-9DD0CCFEBB02}"
EndProject


Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{1F058464-80EF-42BF-84A1-F13F3FC219A4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1F058464-80EF-42BF-84A1-F13F3FC219A4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1F058464-80EF-42BF-84A1-F13F3FC219A4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1F058464-80EF-42BF-84A1-F13F3FC219A4}.Release|Any CPU.Build.0 = Release|Any CPU
		{17BF5F1E-BD48-4325-89A3-2380393D0A64}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{17BF5F1E-BD48-4325-89A3-2380393D0A64}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{17BF5F1E-BD48-4325-89A3-2380393D0A64}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{17BF5F1E-BD48-4325-89A3-2380393D0A64}.Release|Any CPU.Build.0 = Release|Any CPU
		{6949431E-3219-4B5B-8061-324EEC796E53}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6949431E-3219-4B5B-8061-324EEC796E53}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6949431E-3219-4B5B-8061-324EEC796E53}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6949431E-3219-4B5B-8061-324EEC796E53}.Release|Any CPU.Build.0 = Release|Any CPU
		{309755D3-515C-4C79-915A-37B17FB51841}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{309755D3-515C-4C79-915A-37B17FB51841}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{309755D3-515C-4C79-915A-37B17FB51841}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{309755D3-515C-4C79-915A-37B17FB51841}.Release|Any CPU.Build.0 = Release|Any CPU
		{D36F2F9C-64FD-4DBC-B649-29FC9514B089}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D36F2F9C-64FD-4DBC-B649-29FC9514B089}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D36F2F9C-64FD-4DBC-B649-29FC9514B089}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D36F2F9C-64FD-4DBC-B649-29FC9514B089}.Release|Any CPU.Build.0 = Release|Any CPU
		{07073E76-4064-41F7-B079-C76E3C703165}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{07073E76-4064-41F7-B079-C76E3C703165}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{07073E76-4064-41F7-B079-C76E3C703165}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{07073E76-4064-41F7-B079-C76E3C703165}.Release|Any CPU.Build.0 = Release|Any CPU
		{D3059D42-A112-46DF-9E86-F41722B7A1BD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D3059D42-A112-46DF-9E86-F41722B7A1BD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D3059D42-A112-46DF-9E86-F41722B7A1BD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D3059D42-A112-46DF-9E86-F41722B7A1BD}.Release|Any CPU.Build.0 = Release|Any CPU
		{F7F77C21-97A2-4DE7-B175-39BA3F67E1D0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F7F77C21-97A2-4DE7-B175-39BA3F67E1D0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F7F77C21-97A2-4DE7-B175-39BA3F67E1D0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F7F77C21-97A2-4DE7-B175-39BA3F67E1D0}.Release|Any CPU.Build.0 = Release|Any CPU
		{131AF5A5-9109-4451-BDDC-562550F46DA5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{131AF5A5-9109-4451-BDDC-562550F46DA5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{131AF5A5-9109-4451-BDDC-562550F46DA5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{131AF5A5-9109-4451-BDDC-562550F46DA5}.Release|Any CPU.Build.0 = Release|Any CPU
		{5A6A1DD1-5FF5-41B7-B983-295B8E9EBA79}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5A6A1DD1-5FF5-41B7-B983-295B8E9EBA79}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5A6A1DD1-5FF5-41B7-B983-295B8E9EBA79}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5A6A1DD1-5FF5-41B7-B983-295B8E9EBA79}.Release|Any CPU.Build.0 = Release|Any CPU
		{A10C40F5-4963-436B-BD91-C90E6AF5CDDA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A10C40F5-4963-436B-BD91-C90E6AF5CDDA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A10C40F5-4963-436B-BD91-C90E6AF5CDDA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A10C40F5-4963-436B-BD91-C90E6AF5CDDA}.Release|Any CPU.Build.0 = Release|Any CPU
		{99343A8B-BBF7-4E34-97A6-11BABBD2B6A3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{99343A8B-BBF7-4E34-97A6-11BABBD2B6A3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{99343A8B-BBF7-4E34-97A6-11BABBD2B6A3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{99343A8B-BBF7-4E34-97A6-11BABBD2B6A3}.Release|Any CPU.Build.0 = Release|Any CPU
		{902A89D7-A446-4723-9953-FD2ABC6978A8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{902A89D7-A446-4723-9953-FD2ABC6978A8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{902A89D7-A446-4723-9953-FD2ABC6978A8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{902A89D7-A446-4723-9953-FD2ABC6978A8}.Release|Any CPU.Build.0 = Release|Any CPU
		{075A1C4D-7724-42DA-8409-21810F6B602C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{075A1C4D-7724-42DA-8409-21810F6B602C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{075A1C4D-7724-42DA-8409-21810F6B602C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{075A1C4D-7724-42DA-8409-21810F6B602C}.Release|Any CPU.Build.0 = Release|Any CPU
		{9DCE8806-AD07-44CE-AE2F-D8F2652300DA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9DCE8806-AD07-44CE-AE2F-D8F2652300DA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9DCE8806-AD07-44CE-AE2F-D8F2652300DA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9DCE8806-AD07-44CE-AE2F-D8F2652300DA}.Release|Any CPU.Build.0 = Release|Any CPU
		{59BE3C01-B51C-4120-AF21-862AD18AFE54}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{59BE3C01-B51C-4120-AF21-862AD18AFE54}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{59BE3C01-B51C-4120-AF21-862AD18AFE54}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{59BE3C01-B51C-4120-AF21-862AD18AFE54}.Release|Any CPU.Build.0 = Release|Any CPU
		{C56783C5-2204-41B8-A668-89E25D778E3C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C56783C5-2204-41B8-A668-89E25D778E3C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C56783C5-2204-41B8-A668-89E25D778E3C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C56783C5-2204-41B8-A668-89E25D778E3C}.Release|Any CPU.Build.0 = Release|Any CPU
		{A149C021-15AA-4435-821B-574D84342A67}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A149C021-15AA-4435-821B-574D84342A67}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A149C021-15AA-4435-821B-574D84342A67}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A149C021-15AA-4435-821B-574D84342A67}.Release|Any CPU.Build.0 = Release|Any CPU
		{742CD19D-6A4C-47AC-BA08-6530E63F8EF6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{742CD19D-6A4C-47AC-BA08-6530E63F8EF6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{742CD19D-6A4C-47AC-BA08-6530E63F8EF6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{742CD19D-6A4C-47AC-BA08-6530E63F8EF6}.Release|Any CPU.Build.0 = Release|Any CPU
		{EF750C94-7F93-4DE8-AA22-62BD86E656AE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EF750C94-7F93-4DE8-AA22-62BD86E656AE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EF750C94-7F93-4DE8-AA22-62BD86E656AE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EF750C94-7F93-4DE8-AA22-62BD86E656AE}.Release|Any CPU.Build.0 = Release|Any CPU
		{2B76B015-6B96-4A6E-B38C-08652D1B8204}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2B76B015-6B96-4A6E-B38C-08652D1B8204}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2B76B015-6B96-4A6E-B38C-08652D1B8204}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2B76B015-6B96-4A6E-B38C-08652D1B8204}.Release|Any CPU.Build.0 = Release|Any CPU
		{A9A6DA1C-826D-498F-BB4E-65F1498461FA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A9A6DA1C-826D-498F-BB4E-65F1498461FA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A9A6DA1C-826D-498F-BB4E-65F1498461FA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A9A6DA1C-826D-498F-BB4E-65F1498461FA}.Release|Any CPU.Build.0 = Release|Any CPU
		{A62EEFDA-95DC-42CB-8771-1B87A8C6E587}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A62EEFDA-95DC-42CB-8771-1B87A8C6E587}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A62EEFDA-95DC-42CB-8771-1B87A8C6E587}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A62EEFDA-95DC-42CB-8771-1B87A8C6E587}.Release|Any CPU.Build.0 = Release|Any CPU
		{D2D6446A-802B-4A2A-9FF9-CE3BD3BC1147}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D2D6446A-802B-4A2A-9FF9-CE3BD3BC1147}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D2D6446A-802B-4A2A-9FF9-CE3BD3BC1147}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D2D6446A-802B-4A2A-9FF9-CE3BD3BC1147}.Release|Any CPU.Build.0 = Release|Any CPU
		{6779FC90-1B5C-417B-828D-1B4E6A40D13A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6779FC90-1B5C-417B-828D-1B4E6A40D13A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6779FC90-1B5C-417B-828D-1B4E6A40D13A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6779FC90-1B5C-417B-828D-1B4E6A40D13A}.Release|Any CPU.Build.0 = Release|Any CPU
		{F532CD10-0E36-442F-8366-38E5B5BCB790}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F532CD10-0E36-442F-8366-38E5B5BCB790}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F532CD10-0E36-442F-8366-38E5B5BCB790}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F532CD10-0E36-442F-8366-38E5B5BCB790}.Release|Any CPU.Build.0 = Release|Any CPU
		{0A80AF3A-CDCA-4CBD-B59B-10F2A6222F15}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0A80AF3A-CDCA-4CBD-B59B-10F2A6222F15}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0A80AF3A-CDCA-4CBD-B59B-10F2A6222F15}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0A80AF3A-CDCA-4CBD-B59B-10F2A6222F15}.Release|Any CPU.Build.0 = Release|Any CPU
		{F219E3B3-8F79-47A2-8010-67326409B524}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F219E3B3-8F79-47A2-8010-67326409B524}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F219E3B3-8F79-47A2-8010-67326409B524}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F219E3B3-8F79-47A2-8010-67326409B524}.Release|Any CPU.Build.0 = Release|Any CPU
		{C629A886-576E-4153-83C0-45BCD0C1C1BA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C629A886-576E-4153-83C0-45BCD0C1C1BA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C629A886-576E-4153-83C0-45BCD0C1C1BA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C629A886-576E-4153-83C0-45BCD0C1C1BA}.Release|Any CPU.Build.0 = Release|Any CPU
		{B4DF129B-43C7-790A-CFC1-C1E67AA790DA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B4DF129B-43C7-790A-CFC1-C1E67AA790DA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B4DF129B-43C7-790A-CFC1-C1E67AA790DA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B4DF129B-43C7-790A-CFC1-C1E67AA790DA}.Release|Any CPU.Build.0 = Release|Any CPU
		{6FDAEEBB-B2E9-4738-9007-D999EAAD4293}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6FDAEEBB-B2E9-4738-9007-D999EAAD4293}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6FDAEEBB-B2E9-4738-9007-D999EAAD4293}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6FDAEEBB-B2E9-4738-9007-D999EAAD4293}.Release|Any CPU.Build.0 = Release|Any CPU
		{F963E3FB-06A5-426C-BF41-9DD0CCFEBB02}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F963E3FB-06A5-426C-BF41-9DD0CCFEBB02}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F963E3FB-06A5-426C-BF41-9DD0CCFEBB02}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F963E3FB-06A5-426C-BF41-9DD0CCFEBB02}.Release|Any CPU.Build.0 = Release|Any CPU


	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{9C192A7E-5097-40F6-82F3-915D95833C20} = {D7F12D4A-2924-48D1-8D1C-D777AE0CFB03}
		{1C018E27-60E0-4173-A638-1E3E549B9DEA} = {D7F12D4A-2924-48D1-8D1C-D777AE0CFB03}
		{3CFA64F5-A5FF-4EDF-8E98-33A5B4E5F8C1} = {D7F12D4A-2924-48D1-8D1C-D777AE0CFB03}
		{88854C45-A97A-443B-982A-FBC3E14F1B8D} = {D7F12D4A-2924-48D1-8D1C-D777AE0CFB03}
		{D0877E9B-E4D7-4FEE-9200-84E6D981016B} = {D7F12D4A-2924-48D1-8D1C-D777AE0CFB03}
		{DB14448E-E055-49C5-973A-D730FD4FDC68} = {D7F12D4A-2924-48D1-8D1C-D777AE0CFB03}
		{E5542C2D-FAAE-42BB-9D1E-87DDC61E2F04} = {D7F12D4A-2924-48D1-8D1C-D777AE0CFB03}
		{1F058464-80EF-42BF-84A1-F13F3FC219A4} = {9C192A7E-5097-40F6-82F3-915D95833C20}
		{17BF5F1E-BD48-4325-89A3-2380393D0A64} = {9C192A7E-5097-40F6-82F3-915D95833C20}
		{6949431E-3219-4B5B-8061-324EEC796E53} = {9C192A7E-5097-40F6-82F3-915D95833C20}
		{309755D3-515C-4C79-915A-37B17FB51841} = {1C018E27-60E0-4173-A638-1E3E549B9DEA}
		{D36F2F9C-64FD-4DBC-B649-29FC9514B089} = {1C018E27-60E0-4173-A638-1E3E549B9DEA}
		{07073E76-4064-41F7-B079-C76E3C703165} = {1C018E27-60E0-4173-A638-1E3E549B9DEA}
		{D3059D42-A112-46DF-9E86-F41722B7A1BD} = {3CFA64F5-A5FF-4EDF-8E98-33A5B4E5F8C1}
		{F7F77C21-97A2-4DE7-B175-39BA3F67E1D0} = {3CFA64F5-A5FF-4EDF-8E98-33A5B4E5F8C1}
		{131AF5A5-9109-4451-BDDC-562550F46DA5} = {3CFA64F5-A5FF-4EDF-8E98-33A5B4E5F8C1}
		{5A6A1DD1-5FF5-41B7-B983-295B8E9EBA79} = {3CFA64F5-A5FF-4EDF-8E98-33A5B4E5F8C1}
		{A10C40F5-4963-436B-BD91-C90E6AF5CDDA} = {88854C45-A97A-443B-982A-FBC3E14F1B8D}
		{99343A8B-BBF7-4E34-97A6-11BABBD2B6A3} = {D0877E9B-E4D7-4FEE-9200-84E6D981016B}
		{902A89D7-A446-4723-9953-FD2ABC6978A8} = {DB14448E-E055-49C5-973A-D730FD4FDC68}
		{075A1C4D-7724-42DA-8409-21810F6B602C} = {85AE7BD8-6D59-47A4-BF98-A7973A669F2F}
		{9DCE8806-AD07-44CE-AE2F-D8F2652300DA} = {8ECF8DAB-1EB4-4CD1-B744-9229135A6149}
		{59BE3C01-B51C-4120-AF21-862AD18AFE54} = {9C192A7E-5097-40F6-82F3-915D95833C20}
		{C56783C5-2204-41B8-A668-89E25D778E3C} = {1C018E27-60E0-4173-A638-1E3E549B9DEA}
		{85AE7BD8-6D59-47A4-BF98-A7973A669F2F} = {375A9C44-2D9D-4680-BFB4-9E0081EC3068}
		{5BB53210-A130-4699-8958-377CF6191248} = {375A9C44-2D9D-4680-BFB4-9E0081EC3068}
		{94EFCF9F-5B4A-4AC1-A4AC-945EEA56D961} = {375A9C44-2D9D-4680-BFB4-9E0081EC3068}
		{8A1E421A-36FF-4782-AAA6-62E55DC7E518} = {375A9C44-2D9D-4680-BFB4-9E0081EC3068}
		{726130F0-89A7-4632-B586-46B9A9E1603B} = {375A9C44-2D9D-4680-BFB4-9E0081EC3068}
		{6EBF3583-8F26-4223-9FDE-630B3DBB673A} = {375A9C44-2D9D-4680-BFB4-9E0081EC3068}
		{A149C021-15AA-4435-821B-574D84342A67} = {726130F0-89A7-4632-B586-46B9A9E1603B}
		{742CD19D-6A4C-47AC-BA08-6530E63F8EF6} = {8A1E421A-36FF-4782-AAA6-62E55DC7E518}
		{EF750C94-7F93-4DE8-AA22-62BD86E656AE} = {8A1E421A-36FF-4782-AAA6-62E55DC7E518}
		{2B76B015-6B96-4A6E-B38C-08652D1B8204} = {726130F0-89A7-4632-B586-46B9A9E1603B}
		{A9A6DA1C-826D-498F-BB4E-65F1498461FA} = {94EFCF9F-5B4A-4AC1-A4AC-945EEA56D961}
		{A62EEFDA-95DC-42CB-8771-1B87A8C6E587} = {5BB53210-A130-4699-8958-377CF6191248}
		{D2D6446A-802B-4A2A-9FF9-CE3BD3BC1147} = {8A1E421A-36FF-4782-AAA6-62E55DC7E518}
		{6779FC90-1B5C-417B-828D-1B4E6A40D13A} = {1C018E27-60E0-4173-A638-1E3E549B9DEA}
		{8B074695-2364-4BD7-83C8-B8B96CF8B3EE} = {8A1E421A-36FF-4782-AAA6-62E55DC7E518}
		{F532CD10-0E36-442F-8366-38E5B5BCB790} = {8B074695-2364-4BD7-83C8-B8B96CF8B3EE}
		{8D7CF4A1-75F2-4AFF-93AF-12E56D565E24} = {375A9C44-2D9D-4680-BFB4-9E0081EC3068}
		{0A80AF3A-CDCA-4CBD-B59B-10F2A6222F15} = {8D7CF4A1-75F2-4AFF-93AF-12E56D565E24}
		{F219E3B3-8F79-47A2-8010-67326409B524} = {067445AE-E72B-43D1-A10A-765BDFF96D4B}
		{C629A886-576E-4153-83C0-45BCD0C1C1BA} = {3CFA64F5-A5FF-4EDF-8E98-33A5B4E5F8C1}
		{B4DF129B-43C7-790A-CFC1-C1E67AA790DA} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{6FDAEEBB-B2E9-4738-9007-D999EAAD4293} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{F963E3FB-06A5-426C-BF41-9DD0CCFEBB02} = {7EADBF07-B5DE-4DB1-A011-AC26B6AD6F14}


	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {12F67045-EB69-475A-BE2D-1A64A6E3B4B2}
	EndGlobalSection
EndGlobal
