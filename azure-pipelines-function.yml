# Azure DevOps Pipeline for FleetXQFunctionService Azure Function
# Supports deployment to Development, PILOT, PROD_US, PROD_AU, and PROD_UK environments
#
# Required Azure DevOps Variable Groups:
# - FleetXQFunctionService-DEV (for Development environment)
# - FleetXQFunctionService-PILOT (for PILOT environment)
# - FleetXQFunctionService-PROD_US (for PROD_US environment)
# - FleetXQFunctionService-PROD_AU (for PROD_AU environment)
# - FleetXQFunctionService-PROD_UK (for PROD_UK environment)
#
# Required Variables in each group:
# - ServiceBusConnection
# - VehicleAccessQueue
# - UserAccessQueue
# - VehicleSyncQueue
# - FleetXQApiBaseUrl
# - FleetXQUsername
# - FleetXQPassword
#
# Required Pipeline Variables:
# Development:
# - FunctionAppNameDEV
# - ResourceGroupNameDEV
# PILOT:
# - FunctionAppNamePILOT
# - ResourceGroupNamePILOT
# PROD_US:
# - FunctionAppNamePROD_US
# - ResourceGroupNamePROD_US
# PROD_AU:
# - FunctionAppNamePROD_AU
# - ResourceGroupNamePROD_AU
# PROD_UK:
# - FunctionAppNamePROD_UK
# - ResourceGroupNamePROD_UK
#
# Required Service Connections:
# - CIG DEV (for Development)
# - PILOT (for PILOT)
# - PROD - US (for PROD_US)
# - PROD - AU (for PROD_AU)
# - PROD - UK (for PROD_UK)

trigger: none

parameters:
  - name: environment
    displayName: Environment to deploy to
    type: string
    default: Development
    values:
      - Development
      - PILOT
      - PROD_US
      - PROD_AU
      - PROD_UK

pool:
  vmImage: "windows-latest"

variables:
  buildConfiguration: "Release"
  # Environment-specific variables
  ${{ if eq(parameters.environment, 'Development') }}:
    functionAppName: "$(FunctionAppNameDEV)"
    azureSubscription: "CIG DEV"
    resourceGroupName: "$(ResourceGroupNameDEV)"
    environmentSuffix: "Development"
  ${{ if eq(parameters.environment, 'PILOT') }}:
    functionAppName: "$(FunctionAppNamePILOT)"
    azureSubscription: "PROD - PILOT"
    resourceGroupName: "$(ResourceGroupNamePILOT)"
    environmentSuffix: "PROD - PILOT"
  ${{ if eq(parameters.environment, 'PROD_US') }}:
    functionAppName: "$(FunctionAppNamePROD_US)"
    azureSubscription: "PROD - US"
    resourceGroupName: "$(ResourceGroupNamePROD_US)"
    environmentSuffix: "PROD_US"
  ${{ if eq(parameters.environment, 'PROD_AU') }}:
    functionAppName: "$(FunctionAppNamePROD_AU)"
    azureSubscription: "PROD - AU"
    resourceGroupName: "$(ResourceGroupNamePROD_AU)"
    environmentSuffix: "PROD_AU"
  ${{ if eq(parameters.environment, 'PROD_UK') }}:
    functionAppName: "$(FunctionAppNamePROD_UK)"
    azureSubscription: "PROD - UK"
    resourceGroupName: "$(ResourceGroupNamePROD_UK)"
    environmentSuffix: "PROD_UK"

stages:
  - stage: Build
    displayName: "Build Azure Function"
    jobs:
      - job: BuildFunction
        displayName: "Build FleetXQFunctionService"
        steps:
          - checkout: self
            clean: true
            fetchDepth: 1
            fetchTags: false
            lfs: false
            submodules: false
            persistCredentials: false

          - powershell: |
              Write-Host "##[section]Post-checkout verification:"
              Write-Host "Current location: $(Get-Location)"
              Write-Host "Repository structure:"
              Get-ChildItem -Path "." -Force | Select-Object Name, Mode | Format-Table -AutoSize
              Write-Host "Git status:"
              git status --porcelain
              Write-Host "Git branch:"
              git branch -v
            displayName: "Verify Checkout"

          - task: UseDotNet@2
            displayName: "Install .NET 8.0 SDK"
            inputs:
              packageType: "sdk"
              version: "8.x"

          - powershell: |
              Write-Host "##[section]Current working directory:"
              Get-Location
              Write-Host "##[section]Repository root contents:"
              Get-ChildItem -Path "." -Force | Select-Object Name, Mode | Format-Table -AutoSize
              Write-Host "##[section]Looking for FleetXQFunctionService directory:"
              if (Test-Path "FleetXQFunctionService") {
                Write-Host "✅ FleetXQFunctionService directory found"
                Get-ChildItem -Path "FleetXQFunctionService" -Force | Select-Object Name | Format-Table -AutoSize
              } else {
                Write-Host "❌ FleetXQFunctionService directory not found"
                Write-Host "##[section]All directories in root:"
                Get-ChildItem -Path "." -Directory -Force | Select-Object Name | Format-Table -AutoSize
              }
              Write-Host "##[section]Git branch information:"
              git branch -a
              Write-Host "##[section]Pipeline variables:"
              Write-Host "Pipeline.Workspace: $(Pipeline.Workspace)"
              Write-Host "System.DefaultWorkingDirectory: $(System.DefaultWorkingDirectory)"
              Write-Host "Build.SourcesDirectory: $(Build.SourcesDirectory)"
              Write-Host "Build.SourceBranch: $(Build.SourceBranch)"
              Write-Host "Build.SourceBranchName: $(Build.SourceBranchName)"
            displayName: "Debug Pipeline Paths"

          - powershell: |
              $projectPath = "$(Build.SourcesDirectory)\FleetXQFunctionService\FleetXQFunctionService.csproj"
              Write-Host "##[section]Project path resolution:"
              Write-Host "Build.SourcesDirectory: $(Build.SourcesDirectory)"
              Write-Host "Full project path: $projectPath"
              Write-Host "Project file exists: $(Test-Path $projectPath)"
              if (Test-Path $projectPath) {
                  Write-Host "✅ Project file found"
              } else {
                  Write-Host "❌ Project file NOT found"
                  Write-Host "FleetXQFunctionService directory contents:"
                  Get-ChildItem "$(Build.SourcesDirectory)\FleetXQFunctionService" -Force | Format-Table -AutoSize
              }
            displayName: "Debug Project Path"

          - powershell: |
              $projectPath = "FleetXQFunctionService.csproj"
              Write-Host "##[section]Starting restore for FleetXQFunctionService only..."
              Write-Host "Working directory: $(Get-Location)"
              Write-Host "Project file: $projectPath"

              # Restore only this project
              dotnet restore $projectPath --verbosity normal
              if ($LASTEXITCODE -ne 0) {
                  Write-Host "##[error]Restore failed with exit code: $LASTEXITCODE"
                  exit 1
              }
              Write-Host "✅ Restore completed successfully"
            displayName: "Restore NuGet packages"
            workingDirectory: "$(Build.SourcesDirectory)/FleetXQFunctionService"

          - powershell: |
              $projectPath = "FleetXQFunctionService.csproj"
              Write-Host "##[section]Starting build for FleetXQFunctionService only..."
              Write-Host "Working directory: $(Get-Location)"
              Write-Host "Project file: $projectPath"

              # Build only this project  
              dotnet build $projectPath --configuration $(buildConfiguration) --no-restore --verbosity normal
              if ($LASTEXITCODE -ne 0) {
                  Write-Host "##[error]Build failed with exit code: $LASTEXITCODE"
                  exit 1
              }
              Write-Host "✅ Build completed successfully"
            displayName: "Build Azure Function"
            workingDirectory: "$(Build.SourcesDirectory)/FleetXQFunctionService"

          - powershell: |
              $projectPath = "FleetXQFunctionService.csproj"
              $outputPath = "$(Pipeline.Workspace)/function"
              Write-Host "##[section]Starting publish for FleetXQFunctionService only..."
              Write-Host "Working directory: $(Get-Location)"
              Write-Host "Project file: $projectPath"
              Write-Host "Output path: $outputPath"

              # Publish only this project
              dotnet publish $projectPath --configuration $(buildConfiguration) --output $outputPath --verbosity normal
              if ($LASTEXITCODE -ne 0) {
                  Write-Host "##[error]Publish failed with exit code: $LASTEXITCODE"
                  exit 1
              }

              Write-Host "✅ Publish completed successfully"
              Write-Host "##[section]Published files:"
              Get-ChildItem $outputPath -Recurse | Select-Object Name, FullName | Format-Table -AutoSize
            displayName: "Publish Azure Function"
            workingDirectory: "$(Build.SourcesDirectory)/FleetXQFunctionService"

          - powershell: |
              $functionPath = "$(Pipeline.Workspace)/function"
              Write-Host "##[section]Verifying published function files before archiving..."
              Write-Host "Function output directory: $functionPath"

              if (Test-Path $functionPath) {
                  Write-Host "✅ Function output directory exists"
                  Write-Host "##[section]Contents of function directory:"
                  Get-ChildItem $functionPath -Recurse | Select-Object FullName, Length | Format-Table -AutoSize
                  
                  # Check for essential Azure Function files
                  $requiredFiles = @("host.json", "FleetXQFunctionService.dll")
                  foreach ($file in $requiredFiles) {
                      $filePath = Join-Path $functionPath $file
                      if (Test-Path $filePath) {
                          Write-Host "✅ Required file found: $file"
                      } else {
                          Write-Host "❌ Required file missing: $file"
                      }
                  }
                  
                  # Check for Azure Functions executable
                  $functionFiles = Get-ChildItem $functionPath -Name "*.exe" -ErrorAction SilentlyContinue
                  if ($functionFiles) {
                      Write-Host "✅ Function executable found: $($functionFiles -join ', ')"
                  } else {
                      Write-Host "⚠️ No executable files found (this might be OK for isolated functions)"
                  }
              } else {
                  Write-Host "❌ Function output directory does not exist!"
                  exit 1
              }
            displayName: "Verify Function Output"

          - task: ArchiveFiles@2
            displayName: "Archive Function App"
            inputs:
              rootFolderOrFile: "$(Pipeline.Workspace)/function"
              includeRootFolder: false
              archiveType: "zip"
              archiveFile: "$(Pipeline.Workspace)/FleetXQFunctionService.zip"
              verbose: true

          - powershell: |
              $zipFile = "$(Pipeline.Workspace)/FleetXQFunctionService.zip"
              Write-Host "##[section]Verifying deployment package..."
              Write-Host "ZIP file path: $zipFile"

              if (Test-Path $zipFile) {
                  $zipInfo = Get-Item $zipFile
                  Write-Host "✅ ZIP file created successfully"
                  Write-Host "File size: $($zipInfo.Length / 1MB) MB"
                  
                  # Extract and verify ZIP contents
                  Write-Host "##[section]ZIP file contents:"
                  Add-Type -AssemblyName System.IO.Compression.FileSystem
                  $zip = [System.IO.Compression.ZipFile]::OpenRead($zipFile)
                  $zip.Entries | Select-Object Name, Length | Format-Table -AutoSize
                  $zip.Dispose()
              } else {
                  Write-Host "❌ ZIP file was not created!"
                  exit 1
              }
            displayName: "Verify Deployment Package"

          - publish: "$(Pipeline.Workspace)/FleetXQFunctionService.zip"
            artifact: "function-package"
            displayName: "Publish Function Package"

  - stage: Deploy
    displayName: "Deploy to Azure Function App"
    dependsOn: Build
    condition: succeeded()
    jobs:
      - deployment: DeployFunction
        displayName: "Deploy FleetXQFunctionService"
        environment: "FleetXQFunctionService-$(environmentSuffix)"
        variables:
          - ${{ if eq(parameters.environment, 'Development') }}:
              - group: FleetXQFunctionService-DEV
          - ${{ if eq(parameters.environment, 'PILOT') }}:
              - group: FleetXQFunctionService-PILOT
          - ${{ if eq(parameters.environment, 'PROD_US') }}:
              - group: FleetXQFunctionService-PROD_US
          - ${{ if eq(parameters.environment, 'PROD_AU') }}:
              - group: FleetXQFunctionService-PROD_AU
          - ${{ if eq(parameters.environment, 'PROD_UK') }}:
              - group: FleetXQFunctionService-PROD_UK
        strategy:
          runOnce:
            deploy:
              steps:
                - download: current
                  artifact: "function-package"
                  displayName: "Download Function Package"

                - powershell: |
                    Write-Host "##[section]Pre-deployment verification..."
                    Write-Host "Function App Name: $(functionAppName)"
                    Write-Host "Resource Group: $(resourceGroupName)"
                    Write-Host "Package Path: $(Pipeline.Workspace)/function-package/FleetXQFunctionService.zip"
                    Write-Host "Azure Subscription: $(azureSubscription)"

                    # Verify the downloaded package exists
                    $packagePath = "$(Pipeline.Workspace)/function-package/FleetXQFunctionService.zip"
                    if (Test-Path $packagePath) {
                        Write-Host "✅ Package downloaded successfully"
                        $packageInfo = Get-Item $packagePath
                        Write-Host "Package size: $($packageInfo.Length / 1MB) MB"
                    } else {
                        Write-Host "❌ Package not found at: $packagePath"
                        exit 1
                    }
                  displayName: "Pre-deployment Check"

                - task: AzureFunctionApp@1
                  displayName: "Deploy to Azure Function App"
                  inputs:
                    azureSubscription: "$(azureSubscription)"
                    appType: "functionApp"
                    appName: "$(functionAppName)"
                    resourceGroupName: "$(resourceGroupName)"
                    package: "$(Pipeline.Workspace)/function-package/FleetXQFunctionService.zip"
                    deploymentMethod: "zipDeploy"
                    appSettings: >-
                      -FUNCTIONS_WORKER_RUNTIME "dotnet-isolated"
                      -FUNCTIONS_EXTENSION_VERSION "~4"
                      -ServiceBusConnection "$(ServiceBusConnection)"
                      -VehicleAccessQueue "$(VehicleAccessQueue)"
                      -UserAccessQueue "$(UserAccessQueue)"
                      -VehicleSyncQueue "$(VehicleSyncQueue)"
                      -FleetXQApiBaseUrl "$(FleetXQApiBaseUrl)"
                      -FleetXQUsername "$(FleetXQUsername)"
                      -FleetXQPassword "$(FleetXQPassword)"
                      -ASPNETCORE_ENVIRONMENT "${{ parameters.environment }}"
                  continueOnError: true

                - powershell: |
                    Write-Host "##[section]Post-deployment verification..."
                    Write-Host "Checking if previous deployment step failed..."

                    # If the deployment failed, let's try an alternative approach
                    if ($env:AGENT_JOBSTATUS -ne "Succeeded") {
                        Write-Host "⚠️ Primary deployment failed, attempting alternative deployment..."
                        Write-Host "This will be handled by the next step"
                    } else {
                        Write-Host "✅ Primary deployment succeeded"
                    }
                  displayName: "Check Deployment Status"

                - task: AzureCLI@2
                  displayName: "Fallback Deployment (if needed)"
                  condition: failed()
                  inputs:
                    azureSubscription: "$(azureSubscription)"
                    scriptType: "ps"
                    scriptLocation: "inlineScript"
                    inlineScript: |
                      Write-Host "##[section]Attempting fallback deployment using Azure CLI..."
                      $zipFile = "$(Pipeline.Workspace)/function-package/FleetXQFunctionService.zip"

                      try {
                        Write-Host "Deploying function app using az functionapp deployment..."
                        az functionapp deployment source config-zip --name $(functionAppName) --resource-group $(resourceGroupName) --src $zipFile
                        
                        if ($LASTEXITCODE -eq 0) {
                          Write-Host "✅ Fallback deployment succeeded"
                        } else {
                          Write-Host "❌ Fallback deployment failed"
                          exit 1
                        }
                      } catch {
                        Write-Host "❌ Fallback deployment failed: $_"
                        exit 1
                      }

                - task: AzureCLI@2
                  displayName: "Verify Deployment"
                  inputs:
                    azureSubscription: "$(azureSubscription)"
                    scriptType: "ps"
                    scriptLocation: "inlineScript"
                    inlineScript: |
                      Write-Host "##[section]Verifying Function App deployment..."
                      $functionApp = az functionapp show --name $(functionAppName) --resource-group $(resourceGroupName) | ConvertFrom-Json
                      Write-Host "Function App State: $($functionApp.state)"
                      Write-Host "Function App URL: https://$($functionApp.defaultHostName)"

                      # Wait a moment for deployment to settle
                      Write-Host "Waiting 30 seconds for deployment to settle..."
                      Start-Sleep -Seconds 30

                      # Test the health check endpoint
                      Write-Host "##[section]Testing health check endpoint..."
                      try {
                        $healthResponse = Invoke-RestMethod -Uri "https://$($functionApp.defaultHostName)/api/HealthCheck" -Method GET -TimeoutSec 30
                        Write-Host "✅ Health check passed: $($healthResponse | ConvertTo-Json)"
                      } catch {
                        Write-Warning "Health check failed: $_"
                        Write-Host "##[section]Checking function app logs for more details..."
                        
                        # Get recent logs
                        try {
                          $logs = az functionapp log tail --name $(functionAppName) --resource-group $(resourceGroupName) --timeout 10
                          Write-Host "Recent logs: $logs"
                        } catch {
                          Write-Host "Could not retrieve logs: $_"
                        }
                      }

                - powershell: |
                    Write-Host "##[section]✅ FleetXQFunctionService Deployment Complete!"
                    Write-Host "##[command]Function App: $(functionAppName)"
                    Write-Host "##[command]Resource Group: $(resourceGroupName)"
                    Write-Host "##[command]Environment: ${{ parameters.environment }}"
                  displayName: "Deployment Summary"
