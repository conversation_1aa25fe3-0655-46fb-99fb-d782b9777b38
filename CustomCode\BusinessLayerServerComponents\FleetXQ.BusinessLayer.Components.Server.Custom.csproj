﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net7.0</TargetFramework>
    <GeneratePackageOnBuild>True</GeneratePackageOnBuild>
  </PropertyGroup>
  <PropertyGroup>
    <VersionPrefix>*********</VersionPrefix>
    <VersionSuffix></VersionSuffix>
    <Authors>Generative Objects</Authors>
    <Description>FleetXQ Business Layer Server Components Custom</Description>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="GenerativeObjects.Practices.LayerSupportClasses" Version="2.0.3" />
    <PackageReference Include="GenerativeObjects.Services" Version="2.0.3" />
    <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.22.0" />
    <PackageReference Include="Microsoft.Azure.Devices" Version="1.39.0" />
    <PackageReference Include="Azure.Storage.Blobs" Version="12.19.1" />
    <PackageReference Include="Azure.Messaging.ServiceBus" Version="7.17.5" />
    <PackageReference Include="System.IO" Version="4.3.0" />
    <PackageReference Include="Microsoft.CSharp" Version="4.7.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.ApplicationInsights" Version="2.22.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.2" />
    <PackageReference Include="NLog" Version="5.1.4" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\GeneratedCode\BusinessLayerORMSupportClasses\FleetXQ.BusinessLayer.ORMSupportClasses.csproj" />
    <ProjectReference Include="..\..\GeneratedCode\BusinessLayerServerComponents\FleetXQ.BusinessLayer.Components.Server.csproj" />
    <ProjectReference Include="..\..\GeneratedCode\DataLayer\FleetXQ.Data.DataObjects.csproj" />
    <ProjectReference Include="..\DataLayer\FleetXQ.Data.DataObjects.Custom.csproj" />
    <ProjectReference Include="..\DataLayerDataProviders\FleetXQ.Data.DataProviders.Custom.csproj" />
  </ItemGroup>
</Project>