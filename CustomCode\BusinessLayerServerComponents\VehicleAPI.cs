using FleetXQ.BusinessLayer.Components.Server.Custom;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Data.DataObjects.Custom;
using FleetXQ.Data.DataProvidersExtensions.Custom;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using GenerativeObjects.Practices.ORMSupportClasses;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Hosting;

namespace FleetXQ.BusinessLayer.Components.Server
{
    /// <summary>
	/// VehicleAPI Component
	///  
	/// </summary>
    public partial class VehicleAPI : BaseServerComponent, IVehicleAPI
    {
        private readonly IModuleUtilities _moduleUtilities;
        private readonly IDeviceTwinHandler _deviceTwinHandler;
        private readonly ILoggingService _logger;
        private readonly IVehicleUtils _vehicleUtils;
        private readonly IDeviceMessageHandler _deviceMessageHandler;
        private readonly IStorageClientFactory _storageClientFactory;
        private readonly IWebHostEnvironment _webHostEnvironment;
        private readonly IFileService _fileService;
        public VehicleAPI(
            IServiceProvider provider,
            IVehicleUtils vehicleUtils,
            IConfiguration configuration,
            IDataFacade dataFacade,
            IModuleUtilities moduleUtilities,
            IDeviceTwinHandler deviceTwinHandler,
            ILoggingService logger,
            IDeviceMessageHandler deviceMessageHandler,
            IStorageClientFactory storageClientFactory,
            IWebHostEnvironment webHostEnvironment,
            IFileService fileService) : base(provider, configuration, dataFacade)
        {
            _moduleUtilities = moduleUtilities;
            _deviceTwinHandler = deviceTwinHandler;
            _logger = logger;
            _vehicleUtils = vehicleUtils;
            _deviceMessageHandler = deviceMessageHandler;
            _storageClientFactory = storageClientFactory;
            _webHostEnvironment = webHostEnvironment;
            _fileService = fileService;
        }
        /// <summary>
        /// HireDehireVehicle Method
        ///  
        /// </summary>
        /// <param name="vehicleId">vehicleId</param>
        /// <returns></returns>		
        public async System.Threading.Tasks.Task<ComponentResponse<bool>> HireDehireVehicleAsync(System.Guid vehicleId, System.Guid newDepartmentId, System.Boolean syncDriverList, System.Boolean syncMasterList, System.Boolean syncChecklistSetting, System.Boolean syncIdleSettings, System.Boolean resetImpactCalibration, System.Boolean syncOtherSettings, Dictionary<string, object> parameters = null)
        {
            var vehicle = _serviceProvider.GetRequiredService<VehicleDataObject>();
            if (vehicle == null)
            {
                _logger.LogError(new GOServerException("vehicle not found"));
                throw new GOServerException("vehicle not found");
            }
            vehicle.Id = vehicleId;
            vehicle = await _dataFacade.VehicleDataProvider.GetAsync(vehicle);

            // Dehire Process if vehicle is on hire
            if (vehicle.OnHire)
            {
                var currentCustomer = await vehicle.LoadCustomerAsync();
                var newDepartment = _serviceProvider.GetRequiredService<DepartmentDataObject>();
                newDepartment.Id = newDepartmentId;
                newDepartment = await _dataFacade.DepartmentDataProvider.GetAsync(newDepartment);
                if (newDepartmentId == Guid.Empty)
                {
                    _logger.LogError(new GOServerException("please select a department"));
                    throw new GOServerException("please select a department");
                }
                if (newDepartment == null)
                {
                    _logger.LogError(new GOServerException("new department not found"));
                    throw new GOServerException("new department not found");
                }

                // Assuming Dealer Customer
                var newSite = await newDepartment.LoadSiteAsync();
                var newCustomer = await (await newDepartment.LoadSiteAsync()).LoadCustomerAsync();
                if (currentCustomer.Id == newCustomer.Id && (currentCustomer.DealerCustomer == false || currentCustomer.DealerCustomer == null))
                {
                    _logger.LogError(new GOServerException("The vehicle has to be dehired to the dealer customer"));
                    throw new GOServerException("The vehicle has to be dehired to the dealer customer");
                }

                // dehire process starts here
                // 1. set the vehicle.DepartmentId, vehicle.SiteId, vehicle.CustomerId to the new department, site, customer
                // store the old department, site, customer first
                var oldDepartmentId = vehicle.DepartmentId;
                vehicle.DepartmentId = newDepartmentId;
                vehicle.SiteId = newSite.Id;
                vehicle.CustomerId = newCustomer.Id;

                // 2. set the vehicle.OnHire to false, set vehicle.DeHireTime to now
                vehicle.OnHire = false;
                vehicle.DehireTime = DateTime.UtcNow;

                // 3. based on the sync flags, sync the driver list, master list, checklist questions, checklist setting, idle settings, impact calibration, other settings
                await SyncVehicleSettingsForNewDepartmentAsync(vehicle, newDepartmentId, syncDriverList, syncMasterList, syncChecklistSetting, syncIdleSettings, resetImpactCalibration, syncOtherSettings);

                // 4. Create a DeHire History record for the vehicle on the old department
                var dehireHistory = _serviceProvider.GetRequiredService<VehicleHireDehireHistoryDataObject>();
                var module = await vehicle.LoadModuleAsync();
                dehireHistory.VehicleId = vehicleId;
                dehireHistory.DepartmentId = oldDepartmentId;
                dehireHistory.HireTime = vehicle.HireTime;
                dehireHistory.DehireTime = vehicle.DehireTime;
                dehireHistory.DeviceIdWhenSaved = module.IoTDevice;
                dehireHistory.SerialNoWhenSaved = vehicle.SerialNo;
                dehireHistory.VehicleIdWhenSaved = vehicle.HireNo;

                // 5. Save the dehire history
                await _dataFacade.VehicleHireDehireHistoryDataProvider.SaveAsync(dehireHistory);

            }
            else
            {
                // Hire Process
                var currentCustomer = await vehicle.LoadCustomerAsync();
                var newDepartment = _serviceProvider.GetRequiredService<DepartmentDataObject>();
                newDepartment.Id = newDepartmentId;
                newDepartment = await _dataFacade.DepartmentDataProvider.GetAsync(newDepartment);
                if (newDepartmentId == Guid.Empty)
                {
                    _logger.LogError(new GOServerException("please select a department"));
                    throw new GOServerException("please select a department");
                }
                if (newDepartment == null)
                {
                    _logger.LogError(new GOServerException("new department not found"));
                    throw new GOServerException("new department not found");
                }

                // Any customer of the dealer
                var newCustomer = await (await newDepartment.LoadSiteAsync()).LoadCustomerAsync();
                var newSite = await newDepartment.LoadSiteAsync();
                // 1. set the vehicle.DepartmentId, vehicle.SiteId, vehicle.CustomerId to the new department, site, customer
                vehicle.DepartmentId = newDepartmentId;
                vehicle.SiteId = newSite.Id;
                vehicle.CustomerId = newCustomer.Id;

                // 2. set the vehicle.OnHire to true and set vehicle.HireTime to now
                vehicle.OnHire = true;
                vehicle.HireTime = DateTime.UtcNow;

                // 3. based on the sync flags, sync the driver list, master list, checklist questions, checklist setting, idle settings, impact calibration, other settings
                await SyncVehicleSettingsForNewDepartmentAsync(vehicle, newDepartmentId, syncDriverList, syncMasterList, syncChecklistSetting, syncIdleSettings, resetImpactCalibration, syncOtherSettings);
            }

            return new ComponentResponse<bool>(true);
        }

        /// <summary>
        /// GetSuperMastersForVehicle Method
        ///  
        /// </summary>
        /// <param name="vehicleId"></param>
        /// <returns></returns>		
        public async System.Threading.Tasks.Task<ComponentResponse<DataObjectCollection<PersonDataObject>>> GetSuperMastersForVehicleAsync(System.Guid vehicleId, Dictionary<string, object> parameters = null)
        {
            var superMasterUsers = new List<PersonDataObject>();
            // load the vehicle
            var vehicle = _serviceProvider.GetRequiredService<VehicleDataObject>();
            vehicle.Id = vehicleId;
            vehicle = await _dataFacade.VehicleDataProvider.GetAsync(vehicle);
            if (vehicle != null)
            {
                // get PermissionData for OnDemandUser
                var permission = (await _dataFacade.PermissionDataProvider.GetCollectionAsync(null, "LevelName == @0", new object[] { (int)PermissionLevelEnum.OnDemandUser }, skipSecurity: true)).SingleOrDefault();
                if (permission != null)
                {
                    // get all the PerVehicleNormalCardAccess for the vehicle where PermissionData.LevelName is OndemandUser (4)
                    var accesses = await _dataFacade.PerVehicleNormalCardAccessDataProvider.GetCollectionAsync(null, "VehicleId == @0 and PermissionId == @1", new object[] { vehicle.Id, permission.Id }, skipSecurity: true);
                    // for each access, get the card and then get the driver
                    if (accesses?.Any() == true)
                    {
                        foreach (var access in accesses)
                        {
                            var card = await access.LoadCardAsync();
                            if (card != null)
                            {
                                var driver = await card.LoadDriverAsync();
                                if (driver != null)
                                {
                                    var person = await driver.LoadPersonAsync();
                                    if (person != null)
                                    {
                                        superMasterUsers.Add(person);
                                    }
                                }
                            }
                        }
                    }
                }
            }
            return new ComponentResponse<DataObjectCollection<PersonDataObject>>(new DataObjectCollection<PersonDataObject>(superMasterUsers));
        }

        /// <summary>
        /// SendOnDemandSettingToVehicle Method
        ///  
        /// </summary>
        /// <param name="onDemandSettingId"></param>
        /// <returns></returns>		
        public async System.Threading.Tasks.Task<ComponentResponse<System.Boolean>> SendOnDemandSettingToVehicleAsync(System.Guid onDemandPersonId, System.Int32 option, System.Int32 sessionTime, System.Guid vehicleId, Dictionary<string, object> parameters = null)
        {
            var vehicle = _serviceProvider.GetRequiredService<VehicleDataObject>();
            vehicle.Id = vehicleId;
            vehicle = await _dataFacade.VehicleDataProvider.GetAsync(vehicle);
            // check that the vehicle has no person currently related
            var person = await vehicle.LoadPersonAsync();
            if (person != null)
            {
                _logger.LogError(new GOServerException("The vehicle currently has an active on demand user"));
                throw new GOServerException("The vehicle currently has an active on demand user");
            }
            // get all the on demand session of vehicle
            var onDemandSessions = await _dataFacade.OnDemandSessionDataProvider.GetCollectionAsync(null, "VehicleId == @0", new object[] { vehicleId });
            // get the latest on demand session based on StartTime
            var latestOnDemandSession = onDemandSessions?.OrderByDescending(x => x.StartTime).FirstOrDefault();
            // check that all conditions pass
            // if latestOnDemandSession is not null and latestOnDemandSession.SendFlag is not EndSession and vehicle.PersonId is null and option is StartSession, throw an exception
            // if latestOnDemandSession is not null and latestOnDemandSession.SendFlag is not EndSession and vehicle.PersonId is not equal to onDemandPersonId, throw an exception
            if (latestOnDemandSession != null && latestOnDemandSession.SendFlag != OnDemandCMDEnum.EndSession && option == (int)OnDemandCMDEnum.StartSession)
            {
                _logger.LogError(new GOServerException("The vehicle currently has an active on demand session"));
                throw new GOServerException("The vehicle currently has an active on demand session");
            }

            // get the ondemand setting of the vehicle
            var onDemandSetting = await vehicle.LoadOnDemandSettingsAsync();
            // if onDemandSetting is null, throw an exception
            if (onDemandSetting == null)
            {
                _logger.LogError(new GOServerException("The vehicle does not have an on demand setting"));
                throw new GOServerException("The vehicle does not have an on demand setting");
            }
            // if OnDemandSetting.Authorize is false, throw an exception
            if (onDemandSetting.Authorised == false)
            {
                _logger.LogError(new GOServerException("The vehicle is not authorised for on demand session"));
                throw new GOServerException("The vehicle is not authorised for on demand session");
            }

            // check that the onDemandPersonId is not null
            if (onDemandPersonId == Guid.Empty)
            {
                _logger.LogError(new GOServerException("Please select an active on demand user"));
                throw new GOServerException("Please select an active on demand user");
            }
            // check that the onDemandPerson.Driver.Card has ondemand access to the vehicle
            var onDemandPerson = _serviceProvider.GetRequiredService<PersonDataObject>();
            onDemandPerson.Id = onDemandPersonId;
            onDemandPerson = await _dataFacade.PersonDataProvider.GetAsync(onDemandPerson);
            if (onDemandPerson == null)
            {
                _logger.LogError(new GOServerException("On demand user not found"));
                throw new GOServerException("On demand user not found");
            }
            var onDemandDriver = await onDemandPerson.LoadDriverAsync();
            if (onDemandDriver == null)
            {
                _logger.LogError(new GOServerException("On demand user is not a driver"));
                throw new GOServerException("On demand user is not a driver");
            }
            var onDemandCard = await onDemandDriver.LoadCardAsync();
            if (onDemandCard == null)
            {
                _logger.LogError(new GOServerException("On demand user does not have a card"));
                throw new GOServerException("On demand user does not have a card");
            }
            var onDemandPermission = (await _dataFacade.PermissionDataProvider.GetCollectionAsync(null, "LevelName == @0", new object[] { (int)PermissionLevelEnum.OnDemandUser }, skipSecurity: true)).SingleOrDefault();
            if (onDemandPermission == null)
            {
                _logger.LogError(new GOServerException("On demand permission not found"));
                throw new GOServerException("On demand permission not found");
            }
            var onDemandAccessForPerson = (await _dataFacade.PerVehicleNormalCardAccessDataProvider.GetCollectionAsync(null, "VehicleId == @0 && PermissionId == @1 && CardId == @2", new object[] { vehicle.Id, onDemandPermission.Id, onDemandCard.Id })).SingleOrDefault();
            if (onDemandAccessForPerson == null)
            {
                _logger.LogError(new GOServerException("On demand user does not have access to the vehicle"));
                throw new GOServerException("On demand user does not have access to the vehicle");
            }

            // update the onDemandSetting
            onDemandSetting.OnDemandCommand = (OnDemandCMDEnum)option;
            onDemandSetting.SessionTime = sessionTime;
            // save the onDemandSetting
            await _dataFacade.OnDemandSettingsDataProvider.SaveAsync(onDemandSetting);

            // set the onDemandPersonId to the vehicle but by adding vehicle to the onDemandPerson.VehicleItems
            onDemandPerson.VehicleItems.Add(vehicle);
            // save the person because saving the vehicle causes error for some reason
            await _dataFacade.PersonDataProvider.SaveAsync(onDemandPerson);

            // send the on demand setting to the vehicle
            var module = await vehicle.LoadModuleAsync();
            if (module == null)
            {
                _logger.LogError(new GOServerException("The vehicle does not have a module"));
                throw new GOServerException("The vehicle does not have a module");
            }
            await _deviceTwinHandler.SyncOnDemandSettingAndUsersAsync(module.IoTDevice);

            return new ComponentResponse<bool>(true);
        }

        private async System.Threading.Tasks.Task<System.Boolean> SyncVehicleSettingsForNewDepartmentAsync(VehicleDataObject vehicle, System.Guid newDepartmentId, System.Boolean syncDriverList, System.Boolean syncMasterList, System.Boolean syncChecklistSetting, System.Boolean syncIdleSettings, System.Boolean resetImpactCalibration, System.Boolean syncOtherSettings)
        {
            // first delete all existing driver, master, and onDemandUser access for the vehicle in the old department
            var permissions = await _dataFacade.PermissionDataProvider.GetCollectionAsync(null, "LevelName == @0 or LevelName == @1 or LevelName == @2", new object[] { (int)PermissionLevelEnum.Master, (int)PermissionLevelEnum.NormalDriver, (int)PermissionLevelEnum.OnDemandUser }, skipSecurity: true);
            // get all the PerVehicleNormalCardAccess for the vehicle where PermissionData.LevelName is 3 (Normal Driver) or 1 (Master) and mark it for deletion
            if (permissions?.Any() == true)
            {
                foreach (var permission in permissions)
                {
                    var accesses = await _dataFacade.PerVehicleNormalCardAccessDataProvider.GetCollectionAsync(null, "VehicleId == @0 and PermissionId == @1", new object[] { vehicle.Id, permission.Id }, skipSecurity: true);
                    if (accesses?.Any() == true)
                    {
                        foreach (var access in accesses)
                        {
                            access.IsMarkedForDeletion = true;
                            // save the access
                            await _dataFacade.PerVehicleNormalCardAccessDataProvider.SaveAsync(access);
                        }
                    }
                }
            }

            // 3.1 sync driver list
            if (syncDriverList)
            {
                // get the model of the vehicle
                var model = await vehicle.LoadModelAsync();
                // get NormalDriver permission
                var normalDriverPermission = (await _dataFacade.PermissionDataProvider.GetCollectionAsync(null, "LevelName == @0", new object[] { (int)PermissionLevelEnum.NormalDriver }, skipSecurity: true)).SingleOrDefault();
                // get ModelVehicleNormalCardAccess where LevelName is 3 and model is the model of the vehicle and department is the new department
                var modelAccesses = await _dataFacade.ModelVehicleNormalCardAccessDataProvider.GetCollectionAsync(null, "PermissionId == @0 and ModelId == @1 and DepartmentId == @2", new object[] { normalDriverPermission.Id, model.Id, newDepartmentId }, skipSecurity: true);
                // for each card in ModelVehicleNormalCardAccessDataProvider, create a new PerVehicleNormalCardAccess with the card and the vehicle and save it
                if (normalDriverPermission != null)
                {
                    foreach (var modelAccess in modelAccesses)
                    {
                        var card = await modelAccess.LoadCardAsync();
                        var newPerVehicleNormalCardAccess = _serviceProvider.GetRequiredService<PerVehicleNormalCardAccessDataObject>();
                        newPerVehicleNormalCardAccess.PermissionId = modelAccess.PermissionId;
                        newPerVehicleNormalCardAccess.VehicleId = vehicle.Id;
                        newPerVehicleNormalCardAccess.CardId = card.Id;
                        await _dataFacade.PerVehicleNormalCardAccessDataProvider.SaveAsync(newPerVehicleNormalCardAccess);
                    }
                }
            }
            // 3.2 sync master list
            if (syncMasterList)
            {
                // get the model of the vehicle
                var model = await vehicle.LoadModelAsync();
                // get Master permission
                var masterPermission = (await _dataFacade.PermissionDataProvider.GetCollectionAsync(null, "LevelName == @0", new object[] { (int)PermissionLevelEnum.Master }, skipSecurity: true)).SingleOrDefault();
                // get ModelVehicleNormalCardAccess where LevelName is 1 and model is the model of the vehicle and department is the new department
                var modelAccesses = await _dataFacade.ModelVehicleNormalCardAccessDataProvider.GetCollectionAsync(null, "PermissionId == @0 and ModelId == @1 and DepartmentId == @2", new object[] { masterPermission.Id, model.Id, newDepartmentId }, skipSecurity: true);
                // for each card, create a new PerVehicleNormalCardAccess with the card and the vehicle
                if (masterPermission != null)
                {
                    foreach (var modelAccess in modelAccesses)
                    {
                        var card = await modelAccess.LoadCardAsync();
                        var newPerVehicleNormalCardAccess = _serviceProvider.GetRequiredService<PerVehicleNormalCardAccessDataObject>();
                        newPerVehicleNormalCardAccess.PermissionId = modelAccess.PermissionId;
                        newPerVehicleNormalCardAccess.VehicleId = vehicle.Id;
                        newPerVehicleNormalCardAccess.CardId = card.Id;
                        await _dataFacade.PerVehicleNormalCardAccessDataProvider.SaveAsync(newPerVehicleNormalCardAccess);
                    }
                }
            }

            // 3.3 sync checklist questions
            // NOTE: Nothing to do here because checklist questions are derived from department and model of the vehicle. For more info, check VehicleToPreOpChecklistView in CreateViews.custom.sql

            // 3.4 sync checklist setting
            // get the first vehicle of the same model in the new department
            var newVehicle = (await _dataFacade.VehicleDataProvider.GetCollectionAsync(null, "ModelId == @0 and DepartmentId == @1", new object[] { vehicle.ModelId, newDepartmentId }, skipSecurity: true)).FirstOrDefault();
            // if newVehicle is null, get the first vehicle in the new department
            if (newVehicle == null)
            {
                newVehicle = (await _dataFacade.VehicleDataProvider.GetCollectionAsync(null, "DepartmentId == @0", new object[] { newDepartmentId }, skipSecurity: true)).FirstOrDefault();
            }
            if (syncChecklistSetting)
            {


                if (newVehicle != null)
                {
                    // get the checklist setting of the new vehicle
                    var newCheckListSetting = await newVehicle.LoadChecklistSettingsAsync();
                    // if newCheckListSetting is not null, get the checklist setting of the vehicle and update it with the new checklist setting
                    if (newCheckListSetting != null)
                    {
                        var checkListSetting = await vehicle.LoadChecklistSettingsAsync();
                        if (checkListSetting != null)
                        {
                            checkListSetting.QuestionTimeout = newCheckListSetting.QuestionTimeout;
                            checkListSetting.ShowComment = newCheckListSetting.ShowComment;
                            checkListSetting.Randomisation = newCheckListSetting.Randomisation;
                            checkListSetting.Type = newCheckListSetting.Type;
                            checkListSetting.TimeslotOne = newCheckListSetting.TimeslotOne;
                            checkListSetting.TimeslotTwo = newCheckListSetting.TimeslotTwo;
                            checkListSetting.TimeslotThree = newCheckListSetting.TimeslotThree;
                            checkListSetting.TimeslotFour = newCheckListSetting.TimeslotFour;
                            // save checklist setting
                            // await _dataFacade.ChecklistSettingsDataProvider.SaveAsync(checkListSetting);
                        }
                    }
                }
            }
            // 3.5 sync idle settings
            if (syncIdleSettings)
            {
                if (newVehicle != null)
                {
                    // set the TimeoutEnabled and IDLETimer of the vehicle from the new vehicle
                    vehicle.TimeoutEnabled = newVehicle.TimeoutEnabled;
                    vehicle.IDLETimer = newVehicle.IDLETimer;
                }
            }
            // 3.6 reset impact calibration
            if (resetImpactCalibration)
            {
                // call the moduleUtilities.ResetCalibrationAsync with the module
                await _moduleUtilities.ResetCalibrationAsync(vehicle.ModuleId1);
            }
            // 3.7 sync other settings
            if (syncOtherSettings)
            {
                if (newVehicle != null)
                {
                    // get vehicleOtherSettings of the new vehicle
                    var newVehicleOtherSettings = await newVehicle.LoadVehicleOtherSettingsAsync();
                    if (newVehicleOtherSettings != null)
                    {
                        // copy the vehicleOtherSettings of the new vehicle to the vehicle
                        var vehicleOtherSettings = await vehicle.LoadVehicleOtherSettingsAsync();
                        if (vehicleOtherSettings == null)
                        {
                            // create new VehicleOtherSettings for the vehicle from the new vehicle
                            vehicleOtherSettings = _serviceProvider.GetRequiredService<VehicleOtherSettingsDataObject>();
                            vehicleOtherSettings.Id = Guid.NewGuid();
                            vehicleOtherSettings.FullLockout = newVehicleOtherSettings.FullLockout;
                            vehicleOtherSettings.PedestrianSafety = newVehicleOtherSettings.PedestrianSafety;
                            vehicle.VehicleOtherSettingsId = vehicleOtherSettings.Id;
                            await _dataFacade.VehicleOtherSettingsDataProvider.SaveAsync(vehicleOtherSettings);
                        }
                        else
                        {
                            vehicleOtherSettings.FullLockout = newVehicleOtherSettings.FullLockout;
                            vehicleOtherSettings.PedestrianSafety = newVehicleOtherSettings.PedestrianSafety;
                        }
                    }

                    // get all NetworkSettings of the new vehicle
                    var newNetworkSettings = await newVehicle.LoadNetworkSettingsItemsAsync();
                    // get all NetworkSettings of the vehicle and mark them for deletion
                    var networkSettings = await _dataFacade.NetworkSettingsDataProvider.GetCollectionAsync(null, "VehicleId == @0", new object[] { vehicle.Id }, skipSecurity: true);
                    if (networkSettings?.Any() == true)
                    {
                        foreach (var networkSetting in networkSettings)
                        {
                            networkSetting.IsMarkedForDeletion = true;
                            await _dataFacade.NetworkSettingsDataProvider.SaveAsync(networkSetting);
                        }
                    }
                    // create new NetworkSettings for the vehicle from the new vehicle
                    if (newNetworkSettings?.Any() == true)
                    {
                        foreach (var newNetworkSetting in newNetworkSettings)
                        {
                            var networkSetting = _serviceProvider.GetRequiredService<NetworkSettingsDataObject>();
                            networkSetting.VehicleId = vehicle.Id;
                            networkSetting.SSID = newNetworkSetting.SSID;
                            networkSetting.WifiPassword = newNetworkSetting.WifiPassword;
                            vehicle.NetworkSettingsItems.Add(networkSetting);
                        }
                    }
                }

            }

            // 3.8 Set DepartmentChecklist Id
            // [FXQ-2372] This relationship is added to enable us to add a subform in Vehicle Checklist, to update the Department Checklist directly 
            // from the Vehicle page. Because GO doesn't allow to create a subform on a subrelated field. The relationship is enforced 
            // between Vehicle and DepartmentChecklist in VehicleToPreOpChecklistViewDataProviderExtension.OnBeforeSave
            var departmentChecklist = (await _dataFacade.DepartmentChecklistDataProvider.GetCollectionAsync(null, "DepartmentId == @0 and ModelId == @1", new object[] { newDepartmentId, vehicle.ModelId })).FirstOrDefault();

            if (departmentChecklist != null)
            {
                vehicle.DepartmentChecklistId = departmentChecklist.Id;
            }

            // 3.9 Save the vehicle
            await _dataFacade.VehicleDataProvider.SaveAsync(vehicle);
            return true;
        }

        public async System.Threading.Tasks.Task<ComponentResponse<VehicleDiagnosticDataObject>> UpdateVehicleDiagnosticAsync(Guid vehicleId, Dictionary<string, object> parameters = null)
        {
            VehicleDiagnosticDataObject vehicleDiagnosticData = null;

            try
            {
                var vehicle = (await _dataFacade.VehicleDataProvider.GetCollectionAsync(null, "Id == @0", new object[] { vehicleId })).SingleOrDefault();
                if (vehicle == null)
                {
                    _logger.LogError(new GOServerException("vehicle not found"));
                    throw new GOServerException("vehicle not found");
                }

                vehicleDiagnosticData = await vehicle.LoadVehicleDiagnosticAsync();

                if (vehicleDiagnosticData == null)
                {
                    vehicleDiagnosticData = _serviceProvider.GetRequiredService<VehicleDiagnosticDataObject>();
                    vehicleDiagnosticData.VehicleId = vehicleId;
                }

                var module = await vehicle.LoadModuleAsync();

                DeviceTwinReportedProperties deviceTwinProperties = await _vehicleUtils.GetDeviceTwinReportedProperties(module.IoTDevice);

                if (deviceTwinProperties != null)
                {
                    vehicleDiagnosticData.APN = deviceTwinProperties.Status?.ModemApn ?? "N/A";
                    vehicleDiagnosticData.CANCRC = deviceTwinProperties.Status?.CanConfigCrc.ToString() ?? "N/A";
                    vehicleDiagnosticData.CCID = deviceTwinProperties.System?.ModemIccid ?? "N/A";
                    vehicleDiagnosticData.ExpansionModuleVersion = deviceTwinProperties.System?.ExpansionModuleVersion ?? "N/A";
                    vehicleDiagnosticData.FirmwareVersion = deviceTwinProperties.System?.AppVersion ?? "N/A";
                    vehicleDiagnosticData.HardwareVersion = deviceTwinProperties.System?.RootfsVersion ?? "N/A";
                    vehicleDiagnosticData.LastPreopCheck = (deviceTwinProperties.Configuration?.LastPreopCheck.HasValue == true && deviceTwinProperties.Configuration.LastPreopCheck.Value != 0)
                        ? DateTimeOffset.FromUnixTimeSeconds(deviceTwinProperties.Configuration.LastPreopCheck.Value).DateTime
                        : null;

                    vehicleDiagnosticData.RedImpactThreshold = deviceTwinProperties.Configuration?.ShockParameter?.RedImpact ?? 0;
                    vehicleDiagnosticData.ShockThreshold = deviceTwinProperties.Configuration?.ShockParameter?.Threshold ?? 0;
                    vehicleDiagnosticData.SeatIdles = deviceTwinProperties.Configuration?.IdleParameter?.Timeout.ToString() ?? "N/A";
                    vehicleDiagnosticData.SignalStrength = int.TryParse(deviceTwinProperties.Status?.Csq, out int signalStrength) ? signalStrength : 0;
                    vehicleDiagnosticData.SurveyTimeouts = deviceTwinProperties.Configuration?.ChecklistTimeout ?? 0;
                    vehicleDiagnosticData.Timezone = deviceTwinProperties.Configuration?.IdleParameter?.Timeout.ToString() ?? "N/A";
                    vehicleDiagnosticData.IsSynchronized = true;

                    var lastActivity = await _vehicleUtils.GetLastActivityTime(module.IoTDevice);
                    vehicleDiagnosticData.LastPARUpdate = lastActivity.Equals("N/A") ? null : DateTime.Parse(lastActivity);

                    vehicleDiagnosticData.KernelBuildDate = null;
                    vehicleDiagnosticData.ModemVersion = null;

                    // check vehicle diagnostic data synchronization
                    var threshold = module.FSSSBase * ((module.FSSXMulti / 100) + 1);
                    var redImpactThreshold = threshold * 10;

                    if (vehicleDiagnosticData.DatabaseRedImpactThreshold.HasValue)
                    {
                        var newFsssBase = (vehicleDiagnosticData.DatabaseRedImpactThreshold.Value / 10d) / ((module.FSSXMulti / 100) + 1);

                        if (newFsssBase != module.FSSSBase)
                        {
                            module.Calibration = 100;
                            module.FSSSBase = newFsssBase;
                            vehicleDiagnosticData.RedImpactThreshold = vehicleDiagnosticData.DatabaseRedImpactThreshold.Value;

                            vehicle.ImpactLockout = true;
                            await _dataFacade.VehicleDataProvider.SaveAsync(vehicle);

                            module = await _dataFacade.ModuleDataProvider.SaveAsync(module);
                        }
                    }

                    if (vehicleDiagnosticData.RedImpactThreshold != (vehicleDiagnosticData.DatabaseRedImpactThreshold ?? redImpactThreshold))
                    {
                        vehicleDiagnosticData.IsSynchronized = false;
                    }

                    var site = await vehicle.LoadSiteAsync();

                    var tzone = await vehicle.LoadSiteAsync().Result.LoadTimezoneAsync();
                    if (tzone != null && vehicleDiagnosticData.Timezone != "N/A" && tzone.UTCOffset * 60 != short.Parse(vehicleDiagnosticData.Timezone))
                    {
                        vehicleDiagnosticData.IsSynchronized = false;
                    }

                    var idleTime = vehicle.IDLETimer;
                    if (idleTime != null && vehicleDiagnosticData.SeatIdles != "N/A" && idleTime != int.Parse(vehicleDiagnosticData.SeatIdles))
                    {
                        vehicleDiagnosticData.IsSynchronized = false;
                    }

                    // update module.CCID if deviceTwinProperties.System?.ModemIccid ?? "N/A" and if module.CCID is null or "";
                    if (deviceTwinProperties.System?.ModemIccid != null && deviceTwinProperties.System?.ModemIccid != "N/A" && (module.CCID == null || module.CCID == ""))
                    {
                        module.CCID = deviceTwinProperties.System?.ModemIccid;
                        await _dataFacade.ModuleDataProvider.SaveAsync(module);
                    }

                    await _dataFacade.VehicleDiagnosticDataProvider.SaveAsync(vehicleDiagnosticData);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "VehicleAPI.UpdateVehicleDiagnosticAsync: Error updating vehicle diagnostic data");
            }

            return new ComponentResponse<VehicleDiagnosticDataObject>(vehicleDiagnosticData);
        }

        /// <summary>
        /// Reboots a vehicle by sending a reboot command to its IoT module.
        /// This method sends a cloud-to-device message to trigger a system reboot on the vehicle's IoT device.
        /// </summary>
        /// <param name="vehicleId">The unique identifier of the vehicle to reboot</param>
        /// <param name="parameters">Optional additional parameters (not used in this implementation)</param>
        /// <returns>A ComponentResponse containing a boolean indicating success</returns>
        /// <exception cref="GOServerException">Thrown when the vehicle with the specified ID cannot be found</exception>
        public async System.Threading.Tasks.Task<ComponentResponse<bool>> RebootAsync(Guid vehicleId, Dictionary<string, object> parameters = null)
        {
            // Create a new vehicle data object and set its ID
            var vehicle = _serviceProvider.GetService<VehicleDataObject>();
            vehicle.Id = vehicleId;

            // Retrieve the vehicle details from the database
            vehicle = await _dataFacade.VehicleDataProvider.GetAsync(vehicle);

            // Validate that the vehicle exists
            if (vehicle == null)
            {
                throw new GOServerException($"unknow vehicle id {vehicleId}");
            }

            // Load the vehicle's IoT module
            var module = await vehicle.LoadModuleAsync();
            if (module != null)
            {
                // Send the reboot command to the vehicle's IoT device
                await _deviceMessageHandler.SendCloudToDeviceMessageAsync(module.IoTDevice, "REBOOT");
            }

            // Return success response
            return new ComponentResponse<bool>(true);
        }

        /// <summary>
        /// Unlocks a vehicle by sending an unlock command to its IoT module.
        /// This method sends a maintenance command to the vehicle's IoT device to disable any lockout state.
        /// </summary>
        /// <param name="vehicleId">The unique identifier of the vehicle to unlock</param>
        /// <param name="parameters">Optional additional parameters (not used in this implementation)</param>
        /// <returns>A ComponentResponse containing the unlocked vehicle data object</returns>
        /// <exception cref="GOServerException">Thrown when the vehicle with the specified ID cannot be found</exception>
        public async System.Threading.Tasks.Task<ComponentResponse<VehicleDataObject>> UnlockAsync(Guid vehicleId, Dictionary<string, object> parameters = null)
        {
            // Create a new vehicle data object and set its ID
            var vehicle = _serviceProvider.GetService<VehicleDataObject>();
            vehicle.Id = vehicleId;

            // Retrieve the vehicle details from the database
            vehicle = await _dataFacade.VehicleDataProvider.GetAsync(vehicle);

            // Validate that the vehicle exists
            if (vehicle == null)
            {
                throw new GOServerException($"unknow vehicle id {vehicleId}");
            }

            // Load the vehicle's IoT module
            var module = await vehicle.LoadModuleAsync();
            if (module != null)
            {
                // Command to disable maintenance mode (0x00): Normal operation, no lockout
                await _deviceMessageHandler.SendCloudToDeviceMessageAsync(module.IoTDevice, "MAINT=0");
            }

            // Return the vehicle data object in the response
            return new ComponentResponse<VehicleDataObject>(vehicle);
        }

        public async Task<ComponentResponse<bool>> BroadcastMessageAsync(BroadcastMessageDataObject broadcastMessage, Guid[] vehicleIds, Dictionary<string, object> parameters = null)
        {
            if (broadcastMessage == null)
            {
                throw new ArgumentNullException(nameof(broadcastMessage));
            }

            var charsToReplace = new HashSet<char> { '[', '^', '+', ',', ']' };
            broadcastMessage.Message = broadcastMessage.Message?.Trim();
            if (!string.IsNullOrEmpty(broadcastMessage.Message))
            {
                // Replace special chars with spaces and normalize multiple spaces into single spaces
                broadcastMessage.Message = new string(broadcastMessage.Message.Select(c => charsToReplace.Contains(c) ? ' ' : c).ToArray());
                while (broadcastMessage.Message.Contains("  "))
                {
                    broadcastMessage.Message = broadcastMessage.Message.Replace("  ", " ");
                }
            }

            if (broadcastMessage.Timeout <= 0)
            {
                broadcastMessage.Timeout = 60;
            }

            if (string.IsNullOrWhiteSpace(broadcastMessage.Message))
            {
                throw new ArgumentException("Message cannot be null or empty", nameof(broadcastMessage.Message));
            }

            if (vehicleIds == null)
            {
                throw new ArgumentNullException(nameof(vehicleIds));
            }

            if (!vehicleIds.Any())
            {
                throw new ArgumentException("vehicleIds cannot be empty", nameof(vehicleIds));
            }

            foreach (var vehicleId in vehicleIds)
            {
                // Create a new vehicle data object and set its ID
                var vehicle = _serviceProvider.GetService<VehicleDataObject>();
                vehicle.Id = vehicleId;

                // Retrieve the vehicle details from the database
                vehicle = await _dataFacade.VehicleDataProvider.GetAsync(vehicle);

                // Validate that the vehicle exists
                if (vehicle == null)
                {
                    throw new GOServerException($"unknow vehicle id {vehicleId}");
                }

                // Load the vehicle's IoT module
                var module = await vehicle.LoadModuleAsync();
                if (module != null)
                {
                    var broadcastMessageHistory = _serviceProvider.GetRequiredService<BroadcastMessageHistoryDataObject>();
                    broadcastMessageHistory.Message = broadcastMessage.Message;
                    broadcastMessageHistory.Type = broadcastMessage.ResponseOptions;
                    broadcastMessageHistory.SentTime = DateTime.UtcNow;
                    broadcastMessageHistory.VehicleId = vehicleId;
                    broadcastMessageHistory = await _dataFacade.BroadcastMessageHistoryDataProvider.SaveAsync(broadcastMessageHistory);

                    var message = $"MSG={broadcastMessageHistory.MessageId},{broadcastMessage.Message},{(int)broadcastMessage.Priority},{(int)broadcastMessage.ResponseOptions},{broadcastMessage.Timeout}";

                    try
                    {
                        await _deviceMessageHandler.SendCloudToDeviceMessageAsync(module.IoTDevice, message);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"Failed to send broadcast message to vehicle {vehicleId}. Device: {module.IoTDevice}, Message: {message}");
                    }
                }
            }

            return new ComponentResponse<bool>(true);
        }

        public async Task<ComponentResponse<bool>> SoftDeleteAsync(Guid vehicleId, Dictionary<string, object> parameters = null)
        {
            var vehicle = _serviceProvider.GetService<VehicleDataObject>();
            vehicle.Id = vehicleId;

            vehicle = await _dataFacade.VehicleDataProvider.GetAsync(vehicle);

            if (vehicle == null)
            {
                throw new GOServerException($"unknow vehicle id {vehicleId}");
            }

            var oldModuleId = vehicle.ModuleId1;

            // Create a new dummy module
            var dummyModule = _serviceProvider.GetRequiredService<ModuleDataObject>();
            var cleanSerialNo = new string(vehicle.SerialNo.Where(c => char.IsLetterOrDigit(c)).ToArray());
            dummyModule.IoTDevice = $"Deleted_{cleanSerialNo}_{DateTime.UtcNow:yyyyMMddHHmmss}".Replace(" ", "_").Trim();
            dummyModule.Status = (ModuleStatusEnum)2; // Assigned to vehicle
            dummyModule.ModuleType = (ModuleTypeEnum)0; // Mk3
            dummyModule.SwapDate = DateTime.UtcNow;
            await _dataFacade.ModuleDataProvider.SaveAsync(dummyModule);

            // Swap the module using the existing API
            await _moduleUtilities.SwapModuleForVehicleAsync(vehicleId, dummyModule.Id, "Vehicle soft deleted", null);

            vehicle = await _dataFacade.VehicleDataProvider.GetAsync(vehicle);

            // Get the old module and change its status to spare
            var oldModule = _serviceProvider.GetService<ModuleDataObject>();
            oldModule.Id = oldModuleId;
            oldModule = await _dataFacade.ModuleDataProvider.GetAsync(oldModule);
            if (oldModule != null)
            {
                oldModule.Status = ModuleStatusEnum.Spare;
                await _dataFacade.ModuleDataProvider.SaveAsync(oldModule);
            }

            vehicle.DeletedAtUtc = DateTime.UtcNow;
            await _dataFacade.VehicleDataProvider.SaveAsync(vehicle);

            return new ComponentResponse<bool>(true);
        }

        public async Task<ComponentResponse<bool>> ResetUnitMemoryAsync(Guid vehicleId, Dictionary<string, object> parameters = null)
        {
            // Create a new vehicle data object and set its ID
            var vehicle = _serviceProvider.GetService<VehicleDataObject>();
            vehicle.Id = vehicleId;

            // Retrieve the vehicle details from the database
            vehicle = await _dataFacade.VehicleDataProvider.GetAsync(vehicle);

            // Validate that the vehicle exists
            if (vehicle == null)
            {
                throw new GOServerException($"unknow vehicle id {vehicleId}");
            }

            // Load the vehicle's IoT module
            var module = await vehicle.LoadModuleAsync();
            if (module != null)
            {
                // Send the CLRMEM command to the vehicle's IoT device
                await _deviceMessageHandler.SendCloudToDeviceMessageAsync(module.IoTDevice, "CLRMEM");
            }

            // Return success response
            return new ComponentResponse<bool>(true);
        }

        public async Task<ComponentResponse<bool>> UploadLogoAsync(Guid[] vehicleIds, UploadLogoRequestDataObject uploadLogoRequest, Dictionary<string, object> parameters = null)
        {
            if (uploadLogoRequest == null)
            {
                throw new ArgumentNullException(nameof(uploadLogoRequest));
            }

            if (vehicleIds == null || !vehicleIds.Any())
            {
                throw new ArgumentException("vehicleIds cannot be null or empty", nameof(vehicleIds));
            }

            if (string.IsNullOrWhiteSpace(uploadLogoRequest.Logo))
            {
                throw new ArgumentException("Logo data cannot be null or empty", nameof(uploadLogoRequest.Logo));
            }

            if (string.IsNullOrWhiteSpace(uploadLogoRequest.LogoInternalName))
            {
                throw new ArgumentException("Logo internal name cannot be null or empty", nameof(uploadLogoRequest.LogoInternalName));
            }

            // Validate that the file is a PNG
            var fileExtension = Path.GetExtension(uploadLogoRequest.LogoInternalName)?.ToLowerInvariant();
            if (fileExtension != ".png")
            {
                throw new ArgumentException("Only PNG files are allowed for logo uploads", nameof(uploadLogoRequest.LogoInternalName));
            }

            try
            {
                // Create the full file path for the existing local file
                var filePath = Path.Combine(_webHostEnvironment.WebRootPath, "files", uploadLogoRequest.LogoInternalName);

                // Check if the local file exists
                if (!_fileService.FileExists(filePath))
                {
                    throw new GOServerException($"Local file not found at {filePath}");
                }

                // Upload the local file to blob storage
                var blobName = uploadLogoRequest.LogoInternalName;
                var containerName = _configuration["AzureStorage:ImagesContainerName"];

                // Use the factory to create the blob client
                var blobClient = _storageClientFactory.CreateBlobClient(containerName, $"{blobName}");

                // Upload the local file to blob storage
                using (var fileStream = _fileService.OpenRead(filePath))
                {
                    await blobClient.UploadAsync(fileStream, overwrite: true);
                }

                var logoUrl = blobClient.Uri.ToString();
                _logger.LogInformation($"Logo uploaded successfully from {filePath} to blob storage at {logoUrl}");

                // Sync the logo URL to each vehicle in parallel
                foreach (var vehicleId in vehicleIds)
                {
                    // Get the vehicle and its module
                    var vehicle = _serviceProvider.GetService<VehicleDataObject>();
                    vehicle.Id = vehicleId;
                    vehicle = await _dataFacade.VehicleDataProvider.GetAsync(vehicle);

                    if (vehicle == null)
                    {
                        _logger.LogWarning($"Vehicle with ID {vehicleId} not found, skipping logo sync");
                        continue;
                    }

                    var module = await vehicle.LoadModuleAsync();
                    if (module?.IoTDevice == null)
                    {
                        _logger.LogWarning($"Vehicle {vehicleId} has no IoT module, skipping logo sync");
                        continue;
                    }

                    // Sync the logo URL to the vehicle's device twin
                    await _deviceTwinHandler.SyncLogoAsync(module.IoTDevice, logoUrl);
                }

                // Clean up the local file after successful upload and sync
                try
                {
                    if (_fileService.FileExists(filePath))
                    {
                        _fileService.DeleteFile(filePath);
                        _logger.LogInformation($"Local file cleaned up: {filePath}");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning($"Failed to clean up local file {filePath}: {ex.Message}");
                    // Don't throw here as the main operation was successful
                }

                return new ComponentResponse<bool>(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to upload logo: {ex.Message}");
                throw new GOServerException($"Failed to upload logo: {ex.Message}");
            }
        }

        public async Task<ComponentResponse<bool>> UpdateFirmwareAsync(Guid[] vehicleIds, UpdateFirmwareRequestDataObject updateFirmwareRequest, Dictionary<string, object> parameters = null)
        {
            if (vehicleIds == null || !vehicleIds.Any())
            {
                throw new ArgumentException("vehicleIds cannot be null or empty", nameof(vehicleIds));
            }

            if (updateFirmwareRequest == null || updateFirmwareRequest.FirmwareId == Guid.Empty)
            {
                throw new ArgumentException("firmwareId cannot be empty");
            }

            var firmwareId = updateFirmwareRequest.FirmwareId;

            // Get the firmware data object to validate it exists
            var firmware = _serviceProvider.GetRequiredService<FirmwareDataObject>();
            firmware.Id = updateFirmwareRequest.FirmwareId;
            firmware = await _dataFacade.FirmwareDataProvider.GetAsync(firmware);

            if (firmware == null)
            {
                throw new GOServerException($"Firmware with ID {firmwareId} not found");
            }

            var deviceIds = new List<string>();

            foreach (var vehicleId in vehicleIds)
            {
                // Create a new vehicle data object and set its ID
                var vehicle = _serviceProvider.GetService<VehicleDataObject>();
                vehicle.Id = vehicleId;

                // Retrieve the vehicle details from the database
                vehicle = await _dataFacade.VehicleDataProvider.GetAsync(vehicle);

                // Validate that the vehicle exists
                if (vehicle == null)
                {
                    throw new GOServerException($"Unknown vehicle id {vehicleId}");
                }

                // Load the vehicle's IoT module
                var module = await vehicle.LoadModuleAsync();

                // Save the firmware ID to the vehicle
                vehicle.FirmwareId = firmwareId;
                await _dataFacade.VehicleDataProvider.SaveAsync(vehicle);

                // Add the device ID to the list for firmware update
                if (!string.IsNullOrEmpty(module.IoTDevice))
                {
                    deviceIds.Add(module.IoTDevice);
                }
            }

            // Send firmware update to all devices if any were found
            if (deviceIds.Any())
            {
                foreach (var deviceId in deviceIds)
                {
                    await _deviceTwinHandler.UpdateFirmware(deviceId, firmware.Version);
                }
            }

            return new ComponentResponse<bool>(true);
        }

        public Task<ComponentResponse<DataObjectCollection<VehicleDataObject>>> GetAvailableVehiclesForNewSlamcoreDeviceAsync(Guid customerId, Dictionary<string, object> parameters = null)
        {
            throw new NotImplementedException();
        }
    }
}
