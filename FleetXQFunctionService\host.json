{"version": "2.0", "logging": {"applicationInsights": {"samplingSettings": {"isEnabled": true, "excludedTypes": "Request"}}}, "extensionBundle": {"id": "Microsoft.Azure.Functions.ExtensionBundle", "version": "[4.*, 5.0.0)"}, "functionTimeout": "00:09:00", "extensions": {"serviceBus": {"prefetchCount": 32, "maxConcurrentCalls": 8, "maxDeliveryCount": 5, "autoRenewTimeout": "00:10:00", "enableCrossEntityTransactions": false, "maxAutoRenewDuration": "00:10:00", "maxConcurrentSessions": 4, "maxMessageBatchSize": 1000, "sessionIdleTimeout": "00:02:00"}}}