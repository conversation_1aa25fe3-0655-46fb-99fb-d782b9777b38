using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text.Json;
using System.Text.Json.Nodes;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using FleetXQFunctionService.Messages;

namespace FleetXQFunctionService.Services
{
    public class VehicleAccessCreationService : IVehicleAccessCreationService
    {
        private readonly ILogger<VehicleAccessCreationService> _logger;
        private readonly IConfiguration _configuration;
        private readonly HttpClient _httpClient;

        public VehicleAccessCreationService(
            ILogger<VehicleAccessCreationService> logger,
            IConfiguration configuration)
        {
            _logger = logger;
            _configuration = configuration;
            _httpClient = new HttpClient
            {
                Timeout = TimeSpan.FromMinutes(20)
            };

            // Configure HTTP client for better concurrent performance
            _httpClient.DefaultRequestHeaders.ConnectionClose = false;
            _httpClient.DefaultRequestHeaders.Connection.Add("keep-alive");
        }

        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                var baseUrl = _configuration["FleetXQApiBaseUrl"];
                if (string.IsNullOrEmpty(baseUrl))
                {
                    _logger.LogWarning("FleetXQApiBaseUrl not configured");
                    return false;
                }

                _logger.LogInformation("Testing FleetXQ API connection to {BaseUrl}", baseUrl);

                // Try to get a CSRF token to verify API is accessible
                var csrfResponse = await _httpClient.GetAsync($"{baseUrl}/dataset/api/goservices/csrf-token");
                if (csrfResponse.IsSuccessStatusCode)
                {
                    _logger.LogInformation("FleetXQ API connection successful");
                    return true;
                }
                else
                {
                    _logger.LogWarning("FleetXQ API connection failed with status: {StatusCode}", csrfResponse.StatusCode);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "FleetXQ API connection test failed: {Message}", ex.Message);
                return false;
            }
        }

        public async Task<bool> ProcessUserAccessUpdateAsync(UserAccessUpdateMessage message)
        {
            try
            {
                _logger.LogInformation("Processing user access update for person {PersonId}", message.PersonId);

                var baseUrl = _configuration["FleetXQApiBaseUrl"];
                if (string.IsNullOrEmpty(baseUrl))
                {
                    _logger.LogError("FleetXQApiBaseUrl not configured");
                    return false;
                }

                // Authenticate with FleetXQ API
                var (csrfToken, applicationToken, userToken) = await AuthenticateAsync(baseUrl);
                if (string.IsNullOrEmpty(userToken))
                {
                    _logger.LogError("Failed to authenticate with FleetXQ API");
                    return false;
                }

                // Call the FleetXQ user access management API
                var result = await ManageUserAccessAsync(baseUrl, csrfToken, applicationToken, userToken, message);

                if (result)
                {
                    _logger.LogInformation("Successfully processed user access update for person {PersonId}", message.PersonId);
                }
                else
                {
                    _logger.LogError("Failed to process user access update for person {PersonId}", message.PersonId);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing user access update for person {PersonId}: {Message}",
                    message.PersonId, ex.Message);
                return false;
            }
        }

        public async Task<bool> ProcessVehicleAccessCreationAsync(VehicleAccessCreationMessage message)
        {
            try
            {
                var operationType = message.IsDepartmentChange ? "department change" :
                                   message.IsNewVehicle ? "new vehicle" : "vehicle access update";
                _logger.LogInformation("Processing vehicle access {OperationType} for vehicle {VehicleId}", operationType, message.VehicleId);

                var baseUrl = _configuration["FleetXQApiBaseUrl"];
                if (string.IsNullOrEmpty(baseUrl))
                {
                    _logger.LogError("FleetXQApiBaseUrl not configured");
                    return false;
                }

                // Authenticate with FleetXQ API
                var (csrfToken, applicationToken, userToken) = await AuthenticateAsync(baseUrl);
                if (string.IsNullOrEmpty(userToken))
                {
                    _logger.LogError("Failed to authenticate with FleetXQ API");
                    return false;
                }

                // Call the FleetXQ vehicle access creation API
                var result = await CreateVehicleAccessAsync(baseUrl, csrfToken, applicationToken, userToken, message);

                if (result)
                {
                    _logger.LogInformation("Successfully processed vehicle access {OperationType} for vehicle {VehicleId}", operationType, message.VehicleId);
                }
                else
                {
                    _logger.LogError("Failed to process vehicle access {OperationType} for vehicle {VehicleId}", operationType, message.VehicleId);
                }

                return result;
            }
            catch (Exception ex)
            {
                var operationType = message.IsDepartmentChange ? "department change" :
                                   message.IsNewVehicle ? "new vehicle" : "vehicle access update";
                _logger.LogError(ex, "Error processing vehicle access {OperationType} for vehicle {VehicleId}: {Message}",
                    operationType, message.VehicleId, ex.Message);
                return false;
            }
        }

        private async Task<(string csrfToken, string applicationToken, string userToken)> AuthenticateAsync(string baseUrl)
        {
            try
            {
                // Get CSRF token
                var csrfResponse = await _httpClient.GetAsync($"{baseUrl}/dataset/api/goservices/csrf-token");
                csrfResponse.EnsureSuccessStatusCode();
                var csrfContent = await csrfResponse.Content.ReadAsStringAsync();
                var csrfJson = JsonSerializer.Deserialize<JsonObject>(csrfContent);
                var csrfToken = csrfJson?["csrfToken"]?.GetValue<string>();

                if (string.IsNullOrEmpty(csrfToken))
                {
                    _logger.LogError("Failed to retrieve CSRF token from response");
                    return (null, null, null);
                }

                // Authenticate
                var loginUrl = $"{baseUrl}/dataset/api/gosecurityprovider/authenticate";
                var credentials = new Dictionary<string, string>
                {
                    {"username", _configuration["FleetXQUsername"] ?? "Admin"},
                    {"password", _configuration["FleetXQPassword"] ?? "Admin"}
                };

                var content = new MultipartFormDataContent();
                foreach (var kvp in credentials)
                {
                    content.Add(new StringContent(kvp.Value), kvp.Key);
                }

                _httpClient.DefaultRequestHeaders.Remove("x-csrf-token");
                _httpClient.DefaultRequestHeaders.Add("x-csrf-token", csrfToken);
                var response = await _httpClient.PostAsync(loginUrl, content);
                response.EnsureSuccessStatusCode();

                var responseContent = await response.Content.ReadAsStringAsync();
                var jsonResponse = JsonSerializer.Deserialize<JsonObject>(responseContent);

                var tokensObject = jsonResponse?["ObjectsDataSet"]?["GOSecurityTokensObjectsDataSet"]?["GOSecurityTokensObjects"]?["1"];
                var applicationToken = tokensObject?["ApplicationToken"]?.GetValue<string>();
                var userToken = tokensObject?["AuthenticationToken"]?.GetValue<string>();

                if (string.IsNullOrEmpty(applicationToken) || string.IsNullOrEmpty(userToken))
                {
                    _logger.LogError("Failed to retrieve authentication tokens from response");
                    return (null, null, null);
                }

                _logger.LogInformation("Successfully authenticated with FleetXQ API");
                return (csrfToken, applicationToken, userToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to authenticate with FleetXQ API: {Message}", ex.Message);
                return (null, null, null);
            }
        }

        private async Task<bool> CreateVehicleAccessAsync(string baseUrl, string csrfToken, string applicationToken, string userToken, VehicleAccessCreationMessage message)
        {
            try
            {
                var operationType = message.IsDepartmentChange ? "department change" :
                                   message.IsNewVehicle ? "new vehicle" : "vehicle access update";
                _logger.LogInformation("Creating vehicle access for {OperationType} - vehicle {VehicleId}", operationType, message.VehicleId);

                // Build API URL with authentication tokens as query parameters (matching curl format)
                var apiUrl = $"{baseUrl}/dataset/api/vehicleaccesscreation/createvehicleaccess?_user_token={userToken}&_application_token={applicationToken}";

                // Prepare the request data using available properties - matching the curl format
                var requestData = new Dictionary<string, object>
                {
                    ["VehicleId"] = message.VehicleId,
                    ["CustomerId"] = message.CustomerId,
                    ["ModelId"] = message.ModelId,
                    ["DepartmentId"] = message.DepartmentId,
                    ["SiteId"] = message.SiteId,
                    ["IsNewVehicle"] = message.IsNewVehicle,
                    ["IoTDevice"] = message.IoTDevice ?? "",
                    ["CreatedAt"] = message.CreatedAt,
                    ["RetryCount"] = message.RetryCount,
                    ["MaxRetries"] = message.MaxRetries,
                    ["IsDepartmentChange"] = message.IsDepartmentChange,
                    ["OldDepartmentId"] = message.OldDepartmentId,
                    ["OldSiteId"] = message.OldSiteId,
                    ["PermissionLevels"] = message.PermissionLevels
                };

                var json = JsonSerializer.Serialize(requestData);

                // Use multipart/form-data with Message field containing JSON (matching curl format)
                var content = new MultipartFormDataContent();
                content.Add(new StringContent(json), "Message");

                // Set CSRF token header only
                _httpClient.DefaultRequestHeaders.Remove("x-csrf-token");
                _httpClient.DefaultRequestHeaders.Add("x-csrf-token", csrfToken);

                _logger.LogInformation("Calling FleetXQ API: {ApiUrl}", apiUrl.Substring(0, apiUrl.IndexOf('?'))); // Log URL without tokens for security

                var response = await _httpClient.PostAsync(apiUrl, content);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();

                    // Check if the response indicates success or failure
                    // "Vehicle not found" or similar error messages should be treated as failures
                    if (IsSuccessfulResponse(responseContent))
                    {
                        // Log specific message for "No site accesses found" case
                        if (responseContent.Contains("No site accesses found", StringComparison.OrdinalIgnoreCase))
                        {
                            _logger.LogInformation("Vehicle access creation completed for vehicle {VehicleId} - no site accesses found (valid business scenario). Response: {Response}",
                                message.VehicleId, responseContent);
                        }
                        else
                        {
                            _logger.LogInformation("Vehicle access creation successful for vehicle {VehicleId}. Response: {Response}",
                                message.VehicleId, responseContent);
                        }
                        return true;
                    }
                    else
                    {
                        // Log specific details for "Vehicle not found" case
                        if (responseContent.Contains("Vehicle not found", StringComparison.OrdinalIgnoreCase))
                        {
                            _logger.LogWarning("Vehicle {VehicleId} not found - may need to wait for vehicle creation/sync. Retry {RetryCount}/{MaxRetries}",
                                message.VehicleId, message.RetryCount, message.MaxRetries);
                        }
                        else
                        {
                            _logger.LogWarning("Vehicle access creation failed for vehicle {VehicleId}. Response indicates failure: {Response}",
                                message.VehicleId, responseContent);
                        }
                        return false;
                    }
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError("Vehicle access creation failed for vehicle {VehicleId}. Status: {StatusCode}, Response: {Response}",
                        message.VehicleId, response.StatusCode, errorContent);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating vehicle access for vehicle {VehicleId}: {Message}",
                    message.VehicleId, ex.Message);
                return false;
            }
        }

        private async Task<bool> ManageUserAccessAsync(string baseUrl, string csrfToken, string applicationToken, string userToken, UserAccessUpdateMessage message)
        {
            try
            {
                _logger.LogInformation("Managing user access for person {PersonId}", message.PersonId);

                // Build API URL with authentication tokens as query parameters
                var apiUrl = $"{baseUrl}/dataset/api/vehicleaccesscreation/manageuseraccess?_user_token={userToken}&_application_token={applicationToken}";

                // Serialize the entire message as JSON
                var json = JsonSerializer.Serialize(message);

                // Use multipart/form-data with Message field containing JSON
                var content = new MultipartFormDataContent();
                content.Add(new StringContent(json), "Message");

                // Set CSRF token header
                _httpClient.DefaultRequestHeaders.Remove("x-csrf-token");
                _httpClient.DefaultRequestHeaders.Add("x-csrf-token", csrfToken);

                _logger.LogInformation("Calling FleetXQ ManageUserAccess API for person {PersonId}", message.PersonId);

                var response = await _httpClient.PostAsync(apiUrl, content);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    _logger.LogInformation("User access management response for person {PersonId}: {Response}",
                        message.PersonId, responseContent);

                    if (IsSuccessfulResponse(responseContent))
                    {
                        _logger.LogInformation("Successfully managed user access for person {PersonId}", message.PersonId);
                        return true;
                    }
                    else
                    {
                        _logger.LogWarning("User access management failed for person {PersonId}. Response indicates failure: {Response}",
                            message.PersonId, responseContent);
                        return false;
                    }
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError("User access management failed for person {PersonId}. Status: {StatusCode}, Response: {Response}",
                        message.PersonId, response.StatusCode, errorContent);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error managing user access for person {PersonId}: {Message}",
                    message.PersonId, ex.Message);
                return false;
            }
        }

        private bool IsSuccessfulResponse(string responseContent)
        {
            if (string.IsNullOrWhiteSpace(responseContent))
            {
                return false;
            }

            var response = responseContent.Trim().Trim('"');

            // Check for specific business cases that should be treated as successful completion
            // "No site accesses found" is a valid business scenario, not an error
            if (response.Contains("No site accesses found", StringComparison.OrdinalIgnoreCase))
            {
                return true;
            }

            // "An item with the same key has already been added" indicates duplicate user access
            // This is a successful scenario since the user access already exists (desired end state)
            if (response.Contains("An item with the same key has already been added", StringComparison.OrdinalIgnoreCase))
            {
                return true;
            }

            // First try to parse as JSON to check for structured success/error response
            try
            {
                var jsonResponse = JsonSerializer.Deserialize<JsonElement>(responseContent);

                // Check for common success/error fields
                if (jsonResponse.TryGetProperty("success", out var successProp))
                {
                    return successProp.GetBoolean();
                }

                if (jsonResponse.TryGetProperty("error", out var errorProp))
                {
                    return false;
                }

                if (jsonResponse.TryGetProperty("status", out var statusProp))
                {
                    var status = statusProp.GetString()?.ToLower();
                    return status == "success" || status == "ok" || status == "completed";
                }
            }
            catch
            {
                // If JSON parsing fails, fall back to string analysis
            }

            // Check for known error messages that indicate failure
            var errorIndicators = new[]
            {
                "Vehicle not found",
                "Vehicle does not exist",
                "error",
                "failed",
                "exception",
                "not found",
                "invalid",
                "unauthorized",
                "forbidden"
            };

            foreach (var indicator in errorIndicators)
            {
                if (response.Contains(indicator, StringComparison.OrdinalIgnoreCase))
                {
                    return false;
                }
            }

            // Check for success indicators
            var successIndicators = new[]
            {
                "success",
                "created",
                "completed",
                "processed"
            };

            foreach (var indicator in successIndicators)
            {
                if (response.Contains(indicator, StringComparison.OrdinalIgnoreCase))
                {
                    return true;
                }
            }

            // If we can't determine success/failure clearly, treat as failure to be safe
            // This will trigger retry logic which is better than silently failing
            return false;
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
}