﻿(function () {

    FleetXQ.Web.Controllers.VORReportPageControllerCustom = function (controller) {
        var self = this;
        this.controller = controller;

        this.getDefaultConfiguration = function () {
            var configuration = {};
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            var AllowedSiteIds = self.controller.applicationController.viewModel.security.currentUserClaims().AllowedSiteIds;
            // Remove the curly braces and spaces, then split the string into an array
            var array = AllowedSiteIds.replace(/[{} ]/g, '').split(',');
            // Get the first element of the array, if empty null
            var siteId = array[0] || null;
    
        
            var parameterCount = 0;
        
            if (customerId != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += 'CustomerId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + customerId + '" }';
            }
        
            if (siteId != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += 'SiteId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + siteId + '" }';
            }
            if (configuration.filterParameters) {
                configuration.filterParameters = '[' + configuration.filterParameters + ']';
            }
            return configuration;
        };

        this.getConfiguration = function () {
            var configuration = {};
            configuration.filterPredicate = '';
            configuration.filterParameters = [];
            var currentData = self.controller.VORReportFilterFormViewModel.CurrentObject().Data;

            var parameterIndex = 0; // Start indexing after the initial three parameters.

            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            if (currentData.CustomerId() == null && customerId != null) {
                return self.getDefaultConfiguration();
            }

            if (currentData.CustomerId() != null) {
                configuration.filterPredicate += `CustomerId == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Guid", "IsNullable": false, "Value": currentData.CustomerId() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.SiteId() != null) {
                configuration.filterPredicate += `SiteId == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Guid", "IsNullable": false, "Value": currentData.SiteId() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.DepartmentId() != null) {
                configuration.filterPredicate += `DepartmentId == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Guid", "IsNullable": false, "Value": currentData.DepartmentId() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.StartDate() != null) {
                // Add StartDate Year, Month, and Day predicates and parameters.
                configuration.filterPredicate += ` && StartDate == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.DateTime", "IsNullable": false, "Value": currentData.StartDate() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.EndDate() != null) {
                // Add StartDate Year, Month, and Day predicates and parameters.
                configuration.filterPredicate += ` && EndDate == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.DateTime", "IsNullable": false, "Value": currentData.EndDate() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            // Convert configuration.filterParameters array to a JSON string.
            configuration.filterParameters = JSON.stringify(configuration.filterParameters);

            return configuration;
        };
        this.addMultiSearchFilter = function (configuration) {
            var updatedConfiguration = configuration;
            var currentData = self.controller.VORReportFilterFormViewModel.CurrentObject().Data;
            if (currentData.MultiSearch()) {
                if (updatedConfiguration) {
                    var filterParameters = JSON.parse(updatedConfiguration.filterParameters);
                    updatedConfiguration.filterPredicate = updatedConfiguration.filterPredicate ? 
                        updatedConfiguration.filterPredicate + ' && MultiSearch == @' + filterParameters.length :
                        'MultiSearch == @' + filterParameters.length;
                    filterParameters.push({ "TypeName": "System.String", "IsNullable": true, "Value": currentData.MultiSearch() });
                    updatedConfiguration.filterParameters = JSON.stringify(filterParameters);
                    return updatedConfiguration;
                } else {
                    // Create new configuration if none exists
                    return {
                        filterPredicate: 'MultiSearch == @0',
                        filterParameters: JSON.stringify([{ 
                            "TypeName": "System.String", 
                            "IsNullable": true, 
                            "Value": currentData.MultiSearch() 
                        }])
                    };
                }
            }
            return updatedConfiguration;
        };

        this.loadPageData = function () {
            var VORSessionsConfig = this.getConfiguration();
            // add the filter for multi search to the configuration
            VORSessionsConfig = this.addMultiSearchFilter(VORSessionsConfig);

            self.controller.VORReportCombinedViewFormViewModel.AllVORSessionsPerVehicleStoreProcedureItemsGridViewModel.exportFilterPredicate = VORSessionsConfig.filterPredicate;
            self.controller.VORReportCombinedViewFormViewModel.AllVORSessionsPerVehicleStoreProcedureItemsGridViewModel.exportFilterParameters = VORSessionsConfig.filterParameters;
            self.controller.VORReportCombinedViewFormViewModel.AllVORSessionsPerVehicleStoreProcedureItemsGridViewModel.LoadAllVORSessionsPerVehicleStoreProcedureObjectCollection(VORSessionsConfig);

            var VORStatusConfig = this.getConfiguration();
            // add the filter for multi search to the configuration
            VORStatusConfig = this.addMultiSearchFilter(VORStatusConfig);

            self.controller.VORReportCombinedViewFormViewModel.AllVORStatusStoreProcedureItemsGridViewModel.exportFilterPredicate = VORStatusConfig.filterPredicate;
            self.controller.VORReportCombinedViewFormViewModel.AllVORStatusStoreProcedureItemsGridViewModel.exportFilterParameters = VORStatusConfig.filterParameters;

            // Subscribe to the CollectionLoaded event to trigger sort
            var subscription = self.controller.VORReportCombinedViewFormViewModel.AllVORStatusStoreProcedureItemsGridViewModel.Events.CollectionLoaded.subscribe(function() {
                self.controller.VORReportCombinedViewFormViewModel.AllVORStatusStoreProcedureItemsGridViewModel.sortingOptions({
                    columnName: "TimezoneAdjustedStartDateTime",
                    order: "desc"
                });
                subscription.dispose(); // Unsubscribe after first load
            });

            self.controller.VORReportCombinedViewFormViewModel.AllVORStatusStoreProcedureItemsGridViewModel.LoadAllVORStatusStoreProcedureObjectCollection(VORStatusConfig);
        };

        this.loadInitialGridData = function () {
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            if (customerId != null) {
                this.loadPageData();
                return;
            }
            if (!GO.Filter.hasUrlFilter(self.controller.VORReportCombinedViewFormViewModel.AllVORSessionsPerVehicleStoreProcedureItemsGridViewModel.FILTER_NAME, self.controller.VORReportCombinedViewFormViewModel.AllVORSessionsPerVehicleStoreProcedureItemsGridViewModel)) {
                self.controller.VORReportCombinedViewFormViewModel.AllVORSessionsPerVehicleStoreProcedureItemsGridViewModel.LoadAllVORSessionsPerVehicleStoreProcedureObjectCollection();
			}
            if (!GO.Filter.hasUrlFilter(self.controller.VORReportCombinedViewFormViewModel.AllVORStatusStoreProcedureItemsGridViewModel.FILTER_NAME, self.controller.VORReportCombinedViewFormViewModel.AllVORStatusStoreProcedureItemsGridViewModel)) {
				self.controller.VORReportCombinedViewFormViewModel.AllVORStatusStoreProcedureItemsGridViewModel.LoadAllVORStatusStoreProcedureObjectCollection();
			}
        }


        this.initialize = function () {
            // to avoid to have the message asking to confirm changing page and lose changes (caused at the moment by the dashboard filter that is a view in edit mode)
            self.controller.IsInEditMode = function () {
                return false;
            }

            if (!sessionStorage.getItem('hasReloaded')) {
                // Set the flag before reloading
                sessionStorage.setItem('hasReloaded', 'true');
                
                // Force a reload after a brief delay to ensure hash is set
                window.location.reload();
            } else {
                // Clear the flag for next time
                sessionStorage.removeItem('hasReloaded');
            }

            // We create here the filterData function that will be called by the custom client component FleetXQ.Web.Model.Components.DashboardFilter

            self.controller.VORReportFilterFormViewModel.filterData = function () {
                // Check if user is DealerAdmin
                var userRole = self.controller.applicationController.viewModel.security.currentUserClaims().role;
                if (userRole === 'DealerAdmin') {
                    // For DealerAdmin, verify that a customer is selected
                    var customerId = self.controller.VORReportFilterFormViewModel.CurrentObject().Data.CustomerId();
                    if (!customerId) {
                        self.controller.VORReportFilterFormViewModel.ShowError('Please select a customer');
                        return;
                    }
                }
                
                self.loadPageData();

                // save filter data to the GeneralProductivityViewFormViewModel so that it can be used by the "Show Sessions" popup.
                var currentData = self.controller.VORReportFilterFormViewModel.CurrentObject().Data;

                if (currentData.StartDate()) {
                    self.controller.VORReportCombinedViewFormViewModel.AllVORSessionsPerVehicleStoreProcedureItemsGridViewModel.StartDate = currentData.StartDate();
                }
                if (currentData.EndDate()) {
                    self.controller.VORReportCombinedViewFormViewModel.AllVORSessionsPerVehicleStoreProcedureItemsGridViewModel.EndDate = currentData.EndDate();
                }
                if (currentData.CustomerId()) {
                    self.controller.VORReportCombinedViewFormViewModel.AllVORSessionsPerVehicleStoreProcedureItemsGridViewModel.CustomerId = currentData.CustomerId();
                }
                if (currentData.SiteId()) {
                    self.controller.VORReportCombinedViewFormViewModel.AllVORSessionsPerVehicleStoreProcedureItemsGridViewModel.SiteId = currentData.SiteId();
                }
                if (currentData.DepartmentId()) {
                    self.controller.VORReportCombinedViewFormViewModel.AllVORSessionsPerVehicleStoreProcedureItemsGridViewModel.DepartmentId = currentData.DepartmentId();
                }
            };




            // self.loadInitialGridData();
        };
    };

})();