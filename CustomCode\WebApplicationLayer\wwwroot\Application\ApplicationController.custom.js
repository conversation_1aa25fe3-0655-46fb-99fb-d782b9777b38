(function () {
    FleetXQ.Web.Application.ControllerCustom = function (baseController) {
        var self = this;
        this.baseController = baseController;

        // Custom navigation method for Person
        this.customNavigateToPersonDetail = function (personId) {
            if (personId) {
                var path = `/UserManagement/UserDetail/${personId}`;
                window.location.hash = `#!${path}`;
            } else {
                console.error('Invalid person ID for navigation');
            }
        };

        // Custom navigation method for Vehicle
        this.customNavigateToVehicleDetail = function (vehicleId) {
            if (vehicleId) {
                var path = `/Vehicle/${vehicleId}`;
                window.location.hash = `#!${path}`;
            } else {
                console.error('Invalid vehicle ID for navigation');
            }
        };

        // You can add more custom methods here
    };

    // Extend the existing controller with both methods
    var customController = new FleetXQ.Web.Application.ControllerCustom();
    FleetXQ.Web.Application.Controller.prototype.customNavigateToPersonDetail = customController.customNavigateToPersonDetail;
    FleetXQ.Web.Application.Controller.prototype.customNavigateToVehicleDetail = customController.customNavigateToVehicleDetail;
}());