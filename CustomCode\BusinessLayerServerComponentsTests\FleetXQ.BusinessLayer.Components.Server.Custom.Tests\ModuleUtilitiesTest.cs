﻿using FleetXQ.BusinessLayer.Components.Server;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.ServiceLayer;
using FleetXQ.Tests.Common;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.ORMSupportClasses;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using NSubstitute;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FleetXQ.Data.DataProvidersExtensions.Custom;
using VDS.RDF;
using FleetXQ.Data.DataObjects.Custom;
using DocumentFormat.OpenXml.Bibliography;


namespace FleetXQ.BusinessLayer.Components.Server.Custom.Tests
{
    [TestFixture]
    public class ModuleUtilitiesTest : TestBase
    {
        private IModuleUtilities _moduleUtilities;
        private IDataFacade _dataFacade;
        private readonly string _testDatabaseName = $"ModuleUtilitiesTest-{Guid.NewGuid()}";

        protected override void AddServiceRegistrations(ServiceCollection services)
        {
        }

        [OneTimeSetUp]
        public async Task OneTimeSetUpAsync()
        {
            _dataFacade = _serviceProvider.GetRequiredService<IDataFacade>();
            _configuration = _serviceProvider.GetRequiredService<IConfiguration>();
            _moduleUtilities = _serviceProvider.GetRequiredService<IModuleUtilities>();

            CreateTestDatabase(_testDatabaseName);
            await CreateTestDataAsync();
        }

        [OneTimeTearDown]
        public void OneTimeTearDown()
        {
            DeleteTestDatabase(_testDatabaseName);
        }

        [Test]
        public async Task GetAvailableModulesAsync_ReturnsCorrectModules()
        {
            // Arrange
            // Assuming the test setup has already created modules and vehicles
            // and some modules are not assigned to any vehicle.

            // Act
            var response = await _moduleUtilities.GetAvailableModulesAsync(Guid.Empty);
            var availableModules = response.Result.ToList();

            // Assert
            Assert.That(availableModules, Is.Not.Null, "No available modules returned.");
            Assert.That(availableModules.Any(), Is.True, "Expected at least one available module.");
            foreach (var module in availableModules)
            {
                Assert.That(module.Vehicle, Is.Null, "Available module should not be assigned to any vehicle.");
            }
        }

        [Test]
        public async Task GetAvailableModulesAsync_WithDealerId_ReturnsOnlyModulesForDealer()
        {
            // Arrange
            var dealer = (await _dataFacade.DealerDataProvider.GetCollectionAsync(null)).FirstOrDefault();
            Assert.That(dealer, Is.Not.Null, "Test setup error: No dealer found.");

            // Act
            var response = await _moduleUtilities.GetAvailableModulesAsync(dealer.Id);
            var availableModules = response.Result.ToList();

            // Assert
            Assert.That(availableModules, Is.Not.Null, "No available modules returned.");
            Assert.That(availableModules.Any(), Is.True, "Expected at least one available module for dealer.");
            foreach (var module in availableModules)
            {
                Assert.That(module.DealerId, Is.EqualTo(dealer.Id), "Available module should belong to the specified dealer.");
                Assert.That(module.Status == ModuleStatusEnum.Spare || module.Status == null, "Available module should have Spare status or null status.");
            }
        }

        [Test]
        public async Task GetAvailableModulesAsync_OnlyReturnsSpareOrNullStatusModules()
        {
            // Arrange
            // Create a module with non-Spare status to ensure it's filtered out
            var dealer = (await _dataFacade.DealerDataProvider.GetCollectionAsync(null)).FirstOrDefault();
            Assert.That(dealer, Is.Not.Null, "Test setup error: No dealer found.");

            var nonSpareModule = _serviceProvider.GetRequiredService<ModuleDataObject>();
            nonSpareModule.Id = Guid.NewGuid();
            nonSpareModule.Status = ModuleStatusEnum.Assigned;
            nonSpareModule.DealerId = dealer.Id;
            nonSpareModule.CCID = "TEST-CCID-NONSPARE";
            nonSpareModule.IoTDevice = "TEST-IOT-NONSPARE";
            nonSpareModule = await _dataFacade.ModuleDataProvider.SaveAsync(nonSpareModule);

            // Act
            var response = await _moduleUtilities.GetAvailableModulesAsync(dealer.Id);
            var availableModules = response.Result.ToList();

            // Assert
            Assert.That(availableModules, Is.Not.Null, "No available modules returned.");
            foreach (var module in availableModules)
            {
                Assert.That(module.Status == ModuleStatusEnum.Spare || module.Status == null, 
                    $"Module {module.Id} should have Spare status or null status, but has {module.Status}");
            }

            // Verify the non-Spare module is not in the results
            var nonSpareModuleInResults = availableModules.Any(m => m.Id == nonSpareModule.Id);
            Assert.That(nonSpareModuleInResults, Is.False, "Non-Spare module should not be in available modules list.");
        }

        [Test]
        public async Task GetAvailableModulesAsync_ExcludesModulesAssignedToVehicles()
        {
            // Arrange
            // Get a vehicle that has a module assigned
            var vehicleWithModule = (await _dataFacade.VehicleDataProvider.GetCollectionAsync(null, "ModuleId1 != null")).FirstOrDefault();
            Assert.That(vehicleWithModule, Is.Not.Null, "Test setup error: No vehicle with assigned module found.");

            // Act
            var response = await _moduleUtilities.GetAvailableModulesAsync(Guid.Empty);
            var availableModules = response.Result.ToList();

            // Assert
            Assert.That(availableModules, Is.Not.Null, "No available modules returned.");
            
            // Verify the module assigned to the vehicle is not in the available modules
            var assignedModuleInResults = availableModules.Any(m => m.Id == vehicleWithModule.ModuleId1);
            Assert.That(assignedModuleInResults, Is.False, "Module assigned to vehicle should not be in available modules list.");
        }

        [Test]
        public async Task GetAvailableModulesAsync_WithEmptyDealerId_ReturnsAllAvailableModules()
        {
            // Arrange
            // Get all modules that are not assigned to vehicles
            var allModules = await _dataFacade.ModuleDataProvider.GetCollectionAsync(null);
            var vehiclesWithModules = await _dataFacade.VehicleDataProvider.GetCollectionAsync(null, "ModuleId1 != null");
            var assignedModuleIds = vehiclesWithModules.Select(v => v.ModuleId1).Distinct().ToArray();
            
            var expectedAvailableModules = allModules
                .Where(m => !assignedModuleIds.Contains(m.Id))
                .Where(m => m.Status == ModuleStatusEnum.Spare || m.Status == null)
                .ToList();

            // Act
            var response = await _moduleUtilities.GetAvailableModulesAsync(Guid.Empty);
            var availableModules = response.Result.ToList();

            // Assert
            Assert.That(availableModules, Is.Not.Null, "No available modules returned.");
            Assert.That(availableModules.Count, Is.EqualTo(expectedAvailableModules.Count), 
                $"Expected {expectedAvailableModules.Count} available modules, but got {availableModules.Count}");
        }

        [Test]
        public async Task GetAvailableModulesAsync_WithNonExistentDealerId_ReturnsEmptyList()
        {
            // Arrange
            var nonExistentDealerId = Guid.NewGuid();

            // Act
            var response = await _moduleUtilities.GetAvailableModulesAsync(nonExistentDealerId);
            var availableModules = response.Result.ToList();

            // Assert
            Assert.That(availableModules, Is.Not.Null, "Response should not be null.");
            Assert.That(availableModules.Count, Is.EqualTo(0), "Should return empty list for non-existent dealer.");
        }

        [Test]
        public async Task GetAvailableModulesAsync_ReturnsCorrectModuleProperties()
        {
            // Arrange
            var dealer = (await _dataFacade.DealerDataProvider.GetCollectionAsync(null)).FirstOrDefault();
            Assert.That(dealer, Is.Not.Null, "Test setup error: No dealer found.");

            // Act
            var response = await _moduleUtilities.GetAvailableModulesAsync(dealer.Id);
            var availableModules = response.Result.ToList();

            // Assert
            Assert.That(availableModules, Is.Not.Null, "No available modules returned.");
            Assert.That(availableModules.Any(), Is.True, "Expected at least one available module.");
            foreach (var module in availableModules)
            {
                Assert.That(module.Id, Is.Not.EqualTo(Guid.Empty), "Module ID should not be empty.");
                Assert.That(module.IoTDevice, Is.Not.Null, "Module IoTDevice should not be null.");
                Assert.That(module.CCID, Is.Not.Null, "Module CCID should not be null.");
            }
        }

        // New tests for performance optimization methods

        [Test]
        public async Task GetAvailableModulesWithPaginationAsync_ReturnsCorrectPageSize()
        {
            // Arrange
            var dealer = (await _dataFacade.DealerDataProvider.GetCollectionAsync(null)).FirstOrDefault();
            Assert.That(dealer, Is.Not.Null, "Test setup error: No dealer found.");

            // Act
            var response = await _moduleUtilities.GetAvailableModulesWithPaginationAsync(dealer.Id, 1, 5);
            var availableModules = response.Result.ToList();

            // Assert
            Assert.That(availableModules, Is.Not.Null, "No available modules returned.");
            Assert.That(availableModules.Count, Is.LessThanOrEqualTo(5), "Page size should not exceed 5.");
        }

        [Test]
        public async Task GetAvailableModulesWithPaginationAsync_WithSearchTerm_FiltersCorrectly()
        {
            // Arrange
            var dealer = (await _dataFacade.DealerDataProvider.GetCollectionAsync(null)).FirstOrDefault();
            Assert.That(dealer, Is.Not.Null, "Test setup error: No dealer found.");

            // Create a test module with specific IoTDevice
            var testModule = _serviceProvider.GetRequiredService<ModuleDataObject>();
            testModule.Id = Guid.NewGuid();
            testModule.Status = ModuleStatusEnum.Spare;
            testModule.DealerId = dealer.Id;
            testModule.CCID = "TEST-SEARCH-CCID";
            testModule.IoTDevice = "TEST-SEARCH-DEVICE";
            testModule = await _dataFacade.ModuleDataProvider.SaveAsync(testModule);

            // Act
            var response = await _moduleUtilities.GetAvailableModulesWithPaginationAsync(dealer.Id, 1, 50, "TEST-SEARCH");
            var availableModules = response.Result.ToList();

            // Assert
            Assert.That(availableModules, Is.Not.Null, "No available modules returned.");
            var foundModule = availableModules.FirstOrDefault(m => m.Id == testModule.Id);
            Assert.That(foundModule, Is.Not.Null, "Test module should be found in search results.");
        }

        [Test]
        public async Task GetVehicleModuleRelationshipsAsync_ReturnsCorrectRelationships()
        {
            // Arrange
            var vehiclesWithModules = await _dataFacade.VehicleDataProvider.GetCollectionAsync(null, "ModuleId1 != null");
            Assert.That(vehiclesWithModules.Any(), Is.True, "Test setup error: No vehicles with modules found.");

            // Act
            var relationships = await _moduleUtilities.GetVehicleModuleRelationshipsAsync();

            // Assert
            Assert.That(relationships, Is.Not.Null, "Relationships should not be null.");
            Assert.That(relationships.Count, Is.GreaterThan(0), "Should have at least one relationship.");

            // Verify each relationship
            foreach (var relationship in relationships)
            {
                Assert.That(relationship.Key, Is.Not.EqualTo(Guid.Empty), "Vehicle ID should not be empty.");
                Assert.That(relationship.Value, Is.Not.EqualTo(Guid.Empty), "Module ID should not be empty.");
            }
        }

        [Test]
        public async Task GetVehicleModuleRelationshipsAsync_WithDealerId_FiltersCorrectly()
        {
            // Arrange
            var dealer = (await _dataFacade.DealerDataProvider.GetCollectionAsync(null)).FirstOrDefault();
            Assert.That(dealer, Is.Not.Null, "Test setup error: No dealer found.");

            // Act
            var relationships = await _moduleUtilities.GetVehicleModuleRelationshipsAsync(dealer.Id);

            // Assert
            Assert.That(relationships, Is.Not.Null, "Relationships should not be null.");
            
            // Verify all relationships belong to the specified dealer
            foreach (var relationship in relationships)
            {
                var vehicle = await _dataFacade.VehicleDataProvider.GetCollectionAsync(null, "Id == @0", new object[] { relationship.Key });
                var customer = await vehicle.FirstOrDefault()?.LoadCustomerAsync();
                Assert.That(customer?.DealerId, Is.EqualTo(dealer.Id), "All relationships should belong to the specified dealer.");
            }
        }

        [Test]
        public void InvalidateAvailableModulesCache_DoesNotThrowException()
        {
            // Arrange & Act & Assert
            Assert.DoesNotThrow(() => _moduleUtilities.InvalidateAvailableModulesCache(), 
                "Cache invalidation should not throw an exception.");
        }

        [Test]
        public void InvalidateAvailableModulesCache_WithDealerId_DoesNotThrowException()
        {
            // Arrange
            var dealer = (await _dataFacade.DealerDataProvider.GetCollectionAsync(null)).FirstOrDefault();
            Assert.That(dealer, Is.Not.Null, "Test setup error: No dealer found.");

            // Act & Assert
            Assert.DoesNotThrow(() => _moduleUtilities.InvalidateAvailableModulesCache(dealer.Id), 
                "Cache invalidation with dealer ID should not throw an exception.");
        }

        [Test]
        public async Task GetAvailableModulesAsync_CachePerformance_ImprovesOnSubsequentCalls()
        {
            // Arrange
            var dealer = (await _dataFacade.DealerDataProvider.GetCollectionAsync(null)).FirstOrDefault();
            Assert.That(dealer, Is.Not.Null, "Test setup error: No dealer found.");

            // Act - First call (cache miss)
            var firstCallStart = DateTime.UtcNow;
            var firstResponse = await _moduleUtilities.GetAvailableModulesAsync(dealer.Id);
            var firstCallDuration = DateTime.UtcNow - firstCallStart;

            // Second call (cache hit)
            var secondCallStart = DateTime.UtcNow;
            var secondResponse = await _moduleUtilities.GetAvailableModulesAsync(dealer.Id);
            var secondCallDuration = DateTime.UtcNow - secondCallStart;

            // Assert
            Assert.That(firstResponse.Result.Count, Is.EqualTo(secondResponse.Result.Count), 
                "Both calls should return the same number of modules.");
            Assert.That(secondCallDuration, Is.LessThan(firstCallDuration), 
                "Second call should be faster due to caching.");
        }

        [Test]
        public async Task GetAvailableModulesAsync_OptimizedQuery_ReturnsSameResultsAsOriginal()
        {
            // Arrange
            var dealer = (await _dataFacade.DealerDataProvider.GetCollectionAsync(null)).FirstOrDefault();
            Assert.That(dealer, Is.Not.Null, "Test setup error: No dealer found.");

            // Get results using the original approach (for comparison)
            var allModules = await _dataFacade.ModuleDataProvider.GetCollectionAsync(null);
            var vehiclesWithModules = await _dataFacade.VehicleDataProvider.GetCollectionAsync(null, "ModuleId1 != null");
            var usedModuleIds = vehiclesWithModules.Where(v => v.ModuleId1 != Guid.Empty)
                                                  .Select(v => v.ModuleId1)
                                                  .Distinct()
                                                  .ToHashSet();

            var expectedModules = allModules
                .Where(m => (m.Status == ModuleStatusEnum.Spare || m.Status == null) && !usedModuleIds.Contains(m.Id))
                .Where(m => dealer.Id == Guid.Empty || m.DealerId == dealer.Id)
                .ToList();

            // Act - Get results using optimized approach
            var response = await _moduleUtilities.GetAvailableModulesAsync(dealer.Id);
            var actualModules = response.Result.ToList();

            // Assert
            Assert.That(actualModules.Count, Is.EqualTo(expectedModules.Count), 
                $"Expected {expectedModules.Count} modules, but got {actualModules.Count}");

            var actualModuleIds = actualModules.Select(m => m.Id).ToHashSet();
            var expectedModuleIds = expectedModules.Select(m => m.Id).ToHashSet();
            
            Assert.That(actualModuleIds.SetEquals(expectedModuleIds), Is.True, 
                "Both approaches should return the same modules.");
        }

        private async Task CreateTestDataAsync()
        {
            // Create test dealer
            var dealer = _serviceProvider.GetRequiredService<DealerDataObject>();
            dealer.Id = Guid.NewGuid();
            dealer.Name = "Test Dealer";
            dealer = await _dataFacade.DealerDataProvider.SaveAsync(dealer);

            // Create test customer
            var customer = _serviceProvider.GetRequiredService<CustomerDataObject>();
            customer.Id = Guid.NewGuid();
            customer.CompanyName = "Test Customer";
            customer.DealerId = dealer.Id;
            customer = await _dataFacade.CustomerDataProvider.SaveAsync(customer);

            // Create test site
            var site = _serviceProvider.GetRequiredService<SiteDataObject>();
            site.Id = Guid.NewGuid();
            site.Name = "Test Site";
            site.CustomerId = customer.Id;
            site = await _dataFacade.SiteDataProvider.SaveAsync(site);

            // Create test department
            var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
            department.Id = Guid.NewGuid();
            department.Name = "Test Department";
            department.SiteId = site.Id;
            department = await _dataFacade.DepartmentDataProvider.SaveAsync(department);

            // Create test model
            var model = _serviceProvider.GetRequiredService<ModelDataObject>();
            model.Id = Guid.NewGuid();
            model.Name = "Test Model";
            model.DealerId = dealer.Id;
            model = await _dataFacade.ModelDataProvider.SaveAsync(model);

            // Create test modules
            for (int i = 1; i <= 10; i++)
            {
                var module = _serviceProvider.GetRequiredService<ModuleDataObject>();
                module.Id = Guid.NewGuid();
                module.Status = ModuleStatusEnum.Spare;
                module.DealerId = dealer.Id;
                module.CCID = $"TEST-CCID-{i:D3}";
                module.IoTDevice = $"TEST-DEVICE-{i:D3}";
                module = await _dataFacade.ModuleDataProvider.SaveAsync(module);
            }

            // Create test vehicles (some with modules, some without)
            for (int i = 1; i <= 5; i++)
            {
                var vehicle = _serviceProvider.GetRequiredService<VehicleDataObject>();
                vehicle.Id = Guid.NewGuid();
                vehicle.SerialNo = $"TEST-SERIAL-{i:D3}";
                vehicle.HireNo = $"TEST-HIRE-{i:D3}";
                vehicle.CustomerId = customer.Id;
                vehicle.SiteId = site.Id;
                vehicle.DepartmentId = department.Id;
                vehicle.ModelId = model.Id;
                vehicle.OnHire = false;
                vehicle.ModuleIsConnected = false;
                vehicle.ImpactLockout = false;
                vehicle.TimeoutEnabled = false;
                vehicle.IsCanbus = false;

                // Assign modules to some vehicles
                if (i <= 3)
                {
                    var module = await _dataFacade.ModuleDataProvider.GetCollectionAsync(null, "CCID == @0", new object[] { $"TEST-CCID-{i:D3}" });
                    vehicle.ModuleId1 = module.FirstOrDefault()?.Id ?? Guid.Empty;
                }

                vehicle = await _dataFacade.VehicleDataProvider.SaveAsync(vehicle);
            }
        }
    }
}
