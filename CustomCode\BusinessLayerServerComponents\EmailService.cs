﻿using DocumentFormat.OpenXml.Wordprocessing;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.BusinessLayer.Tasks;
using FleetXQ.Data.DataObjects;
using FleetXQ.Data.DataObjects.Custom;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using GenerativeObjects.Practices.LayerSupportClasses.ServiceLayer;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Primitives;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Mail;
using System.Threading.Tasks;

namespace FleetXQ.BusinessLayer.Components.Server
{
    /// <summary>
	/// EmailService Component
	///  
	/// </summary>
    public partial class EmailService : BaseServerComponent, IEmailService
    {

        static bool mailSent = false;
        private readonly FleetXQ.BusinessLayer.Tasks.GOTaskRunner _goTaskRunner;
        private readonly IServicePath _servicePath;
        public EmailService(IServiceProvider serviceProvider, IConfiguration configuration, IDataFacade dataFacade, IServicePath servicePath, FleetXQ.BusinessLayer.Tasks.GOTaskRunner goTaskRunner) : base(serviceProvider, configuration, dataFacade)
        {
            _goTaskRunner = goTaskRunner;
            _servicePath = servicePath;
        }

        /// <summary>
        /// SendEmail Method
        /// </summary>
        /// <param name="EmailDetails"></param>
        /// <returns></returns>
        public async System.Threading.Tasks.Task<ComponentResponse<bool>> SendEmailAsync(System.String EmailDetails, Dictionary<string, object> parameters = null)
        {
            string contactName = "FleetIQ360";
            string australiaPhone = "+61 2 9723 5244";
            string usaPhone = "+1 ***********";

            var emailDetails = JsonConvert.DeserializeObject<EmailDetail>(EmailDetails);
            var emailAlertType = emailDetails.Alert;

            var alertDetail = emailDetails.Details;
            // Send email alert
            var alertType = (await _dataFacade.AlertDataProvider.GetCollectionAsync(null, "Name == @0", new object[] { emailAlertType })).SingleOrDefault();
            if (alertType == null)
            {
                return new ComponentResponse<bool>(false);
            }
            var vehicle = (await _dataFacade.VehicleDataProvider.GetCollectionAsync(null, "Id == @0", new object[] { emailDetails.VehicleId })).SingleOrDefault();
            if (vehicle == null)
            {
                return new ComponentResponse<bool>(false);
            }

            var alertSubscriptions = (await vehicle.LoadVehicleAlertSubscriptionItemsAsync());

            List<GOUserDataObject> userList = new List<GOUserDataObject>();
            foreach (var alertSubscription in alertSubscriptions)
            {
                var myAlertSubscription = await alertSubscription.LoadAlertSubscriptionAsync();
                if (myAlertSubscription.AlertId == alertType.Id && myAlertSubscription.IsActive)
                {
                    var goUser = (await alertSubscription.AlertSubscription.LoadPersonAsync().Result.LoadGOUserAsync());
                    userList.Add(goUser);
                }
            }

            // setup smtp server for AWS
            SmtpClient client = new SmtpClient("email-smtp.us-east-1.amazonaws.com");
            MailAddress from = new MailAddress("<EMAIL>",
               "FleetXQ - Subscription",
            System.Text.Encoding.UTF8);
            foreach (var goUser in userList)
            {

                MailAddress to = new MailAddress(goUser.EmailAddress);
                MailMessage message = new MailMessage(from, to);
                try
                {
                    client.Port = 587;
                    // using AWS SES SMTP credentials
                    client.Credentials = new System.Net.NetworkCredential(_configuration["AWS_SES_USERNAME"], _configuration["AWS_SES_PASSWORD"]);
                    client.EnableSsl = true;

                    message.Body = alertType.Paragraph1 + Environment.NewLine + Environment.NewLine;
                    message.Body += "Vehicle: " + vehicle.HireNo + Environment.NewLine;
                    message.Body += "Time of occurence: " + emailDetails.TimeStamp + Environment.NewLine;

                    //Details will change base on the alert type
                    if (alertType.Name == "Failed Critical Question Lockout Alert")
                    {
                        message.Body += "Name: " + emailDetails.DriverName + Environment.NewLine;
                        message.Body += "Question: " + alertDetail.CriticalQuestion + Environment.NewLine;
                        message.Body += "Expected Answer: " + alertDetail.ExpectedAnswer + Environment.NewLine;
                        message.Body += "Driver Response: " + alertDetail.Response + Environment.NewLine;
                    }
                    else if (alertType.Name == "Service Alert")
                    {
                        message.Body += "Service Due Date: " + alertDetail.dueDate + Environment.NewLine;
                        message.Body += "Current Service Hours: " + alertDetail.currentServiceHours + Environment.NewLine;
                        message.Body += "Next Service Hours: " + alertDetail.nextServiceHours + Environment.NewLine;
                    }
                    message.Body += Environment.NewLine;
                    message.Body += alertType.Paragraph2 + Environment.NewLine;
                    message.Body += Environment.NewLine + Environment.NewLine;
                    message.Body += "This is an automatically generated email. Please do not reply to this email." + Environment.NewLine + Environment.NewLine;
                    message.Body += "NOTE: This message and attachments are confidential, may contain legally privileged information, and are intended solely for the named addressee. If you receive this e-mail in error, please contact the sender and delete this message. Any unauthorized use of the contents of this email is expressly prohibited." + Environment.NewLine;
                    message.Body += Environment.NewLine + Environment.NewLine;

                    message.Body += $"Regards,{Environment.NewLine}" +
                      $"{contactName}{Environment.NewLine}" +
                      $"AU: {australiaPhone}{Environment.NewLine}" +
                      $"USA: {usaPhone}";

                    message.BodyEncoding = System.Text.Encoding.UTF8;
                    message.Subject = alertType.Subject + " - " + vehicle.HireNo;
                    message.SubjectEncoding = System.Text.Encoding.UTF8;
                    await client.SendMailAsync(message);

                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex.ToString());
                    return new ComponentResponse<bool>(false);
                }
                finally
                {
                    message.Dispose();
                }
            }

            client.Dispose();

            return new ComponentResponse<bool>(default(System.Boolean));
        }

        public async System.Threading.Tasks.Task<ComponentResponse<bool>> SendReportEmailsAsync(Dictionary<string, object> parameters = null)
        {
            return new ComponentResponse<bool>(await SendReportEmails());
        }

        public async System.Threading.Tasks.Task<ComponentResponse<bool>> SendReportEmailAsync(string emailAddress, int reportType, System.String filterPredicate, System.String filterParameters, Dictionary<string, object> parameters = null)
        {
            var reportTypes = await _dataFacade.ReportTypeDataProvider.GetCollectionAsync(null);
            var reportTypeObject = reportTypes.FirstOrDefault(x => (int)x.ReportType == reportType);

            if (reportTypeObject == null)
            {
                throw new Exception($"Report type with value: {reportType} not found");
            }

            var taskName = reportTypeObject.ReportType switch
            {
                ReportTypesEnum.ImpactReport => "ImpactReportExportComponent.Export",
                ReportTypesEnum.GeneralProductivityReport => "GeneralProductivityExportComponent.Export",
                ReportTypesEnum.ProficiencyReport => "ProficiencyExportComponent.Export",
                ReportTypesEnum.MachineUnlockReport => "MachineUnlockExportComponent.Export",
                ReportTypesEnum.CurrentStatusReport => "CurrentStatusExportComponent.Export",
                ReportTypesEnum.PreOpCheckReport => "PreOpCheckExportComponent.Export",
                ReportTypesEnum.ServiceCheckReport => "ServiceCheckExportComponent.Export",
                ReportTypesEnum.DriverAccessAbuseReport => "DriverAccessAbuseReportComponent.Export",
                ReportTypesEnum.LicenseExpiryReport => "LicenseExpiryReportComponent.Export",
                ReportTypesEnum.SynchronizationStatusReport => "SynchronizationStatusReportComponent.Export",
                ReportTypesEnum.VORReport => "VORReportComponent.Export",
                ReportTypesEnum.VehicleCalibrationReport => "VehicleCalibrationReportComponent.Export",
                ReportTypesEnum.BroadcastMessageReport => "BroadcastMessageHistoryExportComponent.Export",
                ReportTypesEnum.PedestrianDetectionReport => "PedestrianDetectionReportExportComponent.Export",
                _ => string.Empty
            };

            var taskComponents = reportTypeObject.ReportType switch
            {
                ReportTypesEnum.CurrentStatusReport => new List<object>
                {
                    _serviceProvider.GetRequiredService<IVehicleCurrentStatusReportExportComponent>(),
                    _serviceProvider.GetRequiredService<IDriverCurrentStatusReportExportComponent>()
                },
                ReportTypesEnum.ImpactReport => new List<object> { _serviceProvider.GetRequiredService<IImpactReportExportComponent>() },
                ReportTypesEnum.GeneralProductivityReport => new List<object> { _serviceProvider.GetRequiredService<IGeneralProductivityReportExportComponent>() },
                ReportTypesEnum.ProficiencyReport => new List<object> { _serviceProvider.GetRequiredService<IProficiencyReportExportComponent>() },
                ReportTypesEnum.MachineUnlockReport => new List<object> { _serviceProvider.GetRequiredService<IMachineUnlockReportExportComponent>() },
                ReportTypesEnum.PreOpCheckReport => new List<object> { _serviceProvider.GetRequiredService<IPreOpChecklistReportExportComponent>() },
                ReportTypesEnum.ServiceCheckReport => new List<object> { _serviceProvider.GetRequiredService<IServiceCheckReportExportComponent>() },
                ReportTypesEnum.DriverAccessAbuseReport => new List<object> { _serviceProvider.GetRequiredService<IDriverAccessAbuseReportExportComponent>() },
                ReportTypesEnum.LicenseExpiryReport => new List<object> { _serviceProvider.GetRequiredService<ILicenseExpiryReportExportComponent>() },
                ReportTypesEnum.SynchronizationStatusReport => new List<object> { _serviceProvider.GetRequiredService<ISynchronizationStatusReportExportComponent>() },
                ReportTypesEnum.VORReport => new List<object> { _serviceProvider.GetRequiredService<IVORReportExportComponent>() },
                ReportTypesEnum.VehicleCalibrationReport => new List<object> { _serviceProvider.GetRequiredService<IVehicleCalibrationReportExportComponent>() },
                ReportTypesEnum.BroadcastMessageReport => new List<object> { _serviceProvider.GetRequiredService<IBroadcastMessageHistoryExportComponent>() },
                ReportTypesEnum.PedestrianDetectionReport => new List<object> { _serviceProvider.GetRequiredService<IPedestrianDetectionReportExportComponent>() },
                _ => new List<object>()
            };

            var attachmentFiles = new List<AttachmentFile>();

            foreach (var component in taskComponents)
            {
                var task = _serviceProvider.GetRequiredService<FleetXQ.BusinessLayer.Tasks.Task<ExportJobStatusDataObject>>();
                task.TaskObject = _goTaskRunner.Init<ExportJobStatusDataObject>(
                    taskName,
                    new GOTaskRunner.GOTaskArgs<ExportJobStatusDataObject> { }
                );

                string modifiedFilterPredicate = filterPredicate;

                // Handle filter predicate replacements for CurrentStatusReport
                if (reportTypeObject.ReportType == ReportTypesEnum.CurrentStatusReport)
                {
                    if (component is IVehicleCurrentStatusReportExportComponent)
                    {
                        modifiedFilterPredicate = modifiedFilterPredicate
                            .Replace("Driver.Person.CustomerId", "Vehicle.Department.Site.CustomerId")
                            .Replace("Driver.Person.SiteId", "Vehicle.Department.SiteId")
                            .Replace("Driver.Person.DepartmentId", "Vehicle.DepartmentId");
                    }
                    else if (component is IDriverCurrentStatusReportExportComponent)
                    {
                        modifiedFilterPredicate = modifiedFilterPredicate
                            .Replace("Vehicle.Department.Site.CustomerId", "Driver.Person.CustomerId")
                            .Replace("Vehicle.Department.SiteId", "Driver.Person.SiteId")
                            .Replace("Vehicle.DepartmentId", "Driver.Person.DepartmentId");
                    }
                }

                dynamic exportComponent = component;
                await exportComponent.ExportAsync(task, modifiedFilterPredicate, filterParameters, null);

                if (task.DataObject.TaskStatus == GOTaskStatusEnum.Complete)
                {
                    string storageContainer = _configuration["StorageContainer"] == StringValues.Empty ? "files" : _configuration["StorageContainer"];
                    var fileName = task.DataObject.ExportedFileInternalName.Substring(task.DataObject.ExportedFileInternalName.IndexOf("/") + 1);
                    var fullPath = Path.Combine(_servicePath.WebRootPath(), storageContainer, "exports", fileName);

                    attachmentFiles.Add(new AttachmentFile
                    {
                        FileLocation = fullPath,
                        FileName = task.DataObject.ExportedFile
                    });
                }
            }

            if (attachmentFiles.Any())
            {
                await SendEmail(new List<string> { emailAddress }, reportTypeObject.Description, attachmentFiles);
            }

            return new ComponentResponse<bool>(true);
        }


        private async System.Threading.Tasks.Task<bool> SendReportEmails()
        {
            var reportSubscriptions = await _dataFacade.ReportSubscriptionDataProvider.GetCollectionAsync(null);
            if (reportSubscriptions == null || !reportSubscriptions.Any())
            {
                return false;
            }

            foreach (var reportSub in reportSubscriptions)
            {
                try
                {
                    if (reportSub.StartDate < DateTime.UtcNow && (reportSub.NextRuntime == null || reportSub.NextRuntime < DateTime.UtcNow))
                    {
                        var emailList = new List<string>();

                        // Determine email recipients based on subscription configuration
                        // Case 1: Direct user association through GOUserId or PersonId
                        if (reportSub.GOUserId != null || reportSub.PersonId != null)
                        {
                            GOUserDataObject goUser = null;

                            // First attempt: Try to get user directly from GOUserId
                            if (reportSub.GOUserId != null)
                            {
                                goUser = await reportSub.LoadGOUserAsync();
                            }

                            // Second attempt: If no direct user found, try to get user through PersonId
                            if (goUser == null && reportSub.PersonId != null)
                            {
                                var person = await reportSub.LoadPersonAsync();

                                if (person != null)
                                {
                                    goUser = await person.LoadGOUserAsync();
                                }
                            }

                            // Add user's email if a valid email address exists
                            if (!string.IsNullOrEmpty(goUser?.EmailAddress))
                            {
                                emailList.Add(goUser.EmailAddress);
                            }
                        }
                        // Case 2: Email group association
                        else
                        {
                            // Try to get emails from email group
                            var emailGroup = await reportSub.LoadEmailGroupsAsync();
                            if (emailGroup == null) continue;

                            // Get all person items associated with the email group
                            var emailGroupsToPersonItems = await emailGroup.LoadEmailGroupsToPersonItemsAsync();
                            if (emailGroupsToPersonItems?.Any() == true)
                            {
                                // Process each person in the email group
                                foreach (var emailGroupsToPerson in emailGroupsToPersonItems)
                                {
                                    var person = await emailGroupsToPerson.LoadPersonAsync();
                                    if (person == null) continue;

                                    // Get associated user and add their email if valid
                                    var goUser = await person.LoadGOUserAsync();
                                    if (!string.IsNullOrEmpty(goUser?.EmailAddress))
                                    {
                                        emailList.Add(goUser.EmailAddress);
                                    }
                                }
                            }
                        }

                        // Skip this subscription if no valid email recipients were found
                        if (emailList.Count == 0) continue;

                        var reportType = await reportSub.LoadReportTypeAsync();

                        var taskName = reportType.ReportType switch
                        {
                            ReportTypesEnum.ImpactReport => "ImpactReportExportComponent.Export",
                            ReportTypesEnum.GeneralProductivityReport => "GeneralProductivityExportComponent.Export",
                            ReportTypesEnum.ProficiencyReport => "ProficiencyExportComponent.Export",
                            ReportTypesEnum.MachineUnlockReport => "MachineUnlockExportComponent.Export",
                            ReportTypesEnum.CurrentStatusReport => "CurrentStatusExportComponent.Export",
                            ReportTypesEnum.PreOpCheckReport => "PreOpCheckExportComponent.Export",
                            ReportTypesEnum.ServiceCheckReport => "ServiceCheckExportComponent.Export",
                            ReportTypesEnum.DriverAccessAbuseReport => "DriverAccessAbuseReportComponent.Export",
                            ReportTypesEnum.LicenseExpiryReport => "LicenseExpiryReportComponent.Export",
                            ReportTypesEnum.SynchronizationStatusReport => "SynchronizationStatusReportComponent.Export",
                            ReportTypesEnum.VORReport => "VORReportComponent.Export",
                            ReportTypesEnum.VehicleCalibrationReport => "VehicleCalibrationReportComponent.Export",
                            ReportTypesEnum.BroadcastMessageReport => "BroadcastMessageHistoryExportComponent.Export",
                            ReportTypesEnum.PedestrianDetectionReport => "PedestrianDetectionReportExportComponent.Export",
                            _ => string.Empty
                        };

                        var taskComponents = reportType.ReportType switch
                        {
                            ReportTypesEnum.CurrentStatusReport => new List<object>
                            {
                                _serviceProvider.GetRequiredService<IVehicleCurrentStatusReportExportComponent>(),
                                _serviceProvider.GetRequiredService<IDriverCurrentStatusReportExportComponent>()
                            },
                            ReportTypesEnum.ImpactReport => new List<object> { _serviceProvider.GetRequiredService<IImpactReportExportComponent>() },
                            ReportTypesEnum.GeneralProductivityReport => new List<object> { _serviceProvider.GetRequiredService<IGeneralProductivityReportExportComponent>() },
                            ReportTypesEnum.ProficiencyReport => new List<object> { _serviceProvider.GetRequiredService<IProficiencyReportExportComponent>() },
                            ReportTypesEnum.MachineUnlockReport => new List<object> { _serviceProvider.GetRequiredService<IMachineUnlockReportExportComponent>() },
                            ReportTypesEnum.PreOpCheckReport => new List<object> { _serviceProvider.GetRequiredService<IPreOpChecklistReportExportComponent>() },
                            ReportTypesEnum.ServiceCheckReport => new List<object> { _serviceProvider.GetRequiredService<IServiceCheckReportExportComponent>() },
                            ReportTypesEnum.DriverAccessAbuseReport => new List<object> { _serviceProvider.GetRequiredService<IDriverAccessAbuseReportExportComponent>() },
                            ReportTypesEnum.LicenseExpiryReport => new List<object> { _serviceProvider.GetRequiredService<ILicenseExpiryReportExportComponent>() },
                            ReportTypesEnum.SynchronizationStatusReport => new List<object> { _serviceProvider.GetRequiredService<ISynchronizationStatusReportExportComponent>() },
                            ReportTypesEnum.VORReport => new List<object> { _serviceProvider.GetRequiredService<IVORReportExportComponent>() },
                            ReportTypesEnum.VehicleCalibrationReport => new List<object> { _serviceProvider.GetRequiredService<IVehicleCalibrationReportExportComponent>() },
                            ReportTypesEnum.BroadcastMessageReport => new List<object> { _serviceProvider.GetRequiredService<IBroadcastMessageHistoryExportComponent>() },
                            ReportTypesEnum.PedestrianDetectionReport => new List<object> { _serviceProvider.GetRequiredService<IPedestrianDetectionReportExportComponent>() },
                            _ => new List<object>()
                        };

                        var filterParts = new List<string>();
                        var parameters = new List<object>();
                        var paramIndex = 0;

                        if (reportSub.CustomerId.HasValue)
                        {
                            filterParts.Add($"CustomerId == @{paramIndex}");
                            parameters.Add(new { TypeName = "System.Guid", IsNullable = false, Value = reportSub.CustomerId.Value.ToString() });
                            paramIndex++;
                        }

                        if (reportSub.SiteId.HasValue)
                        {
                            filterParts.Add($"SiteId == @{paramIndex}");
                            parameters.Add(new { TypeName = "System.Guid", IsNullable = false, Value = reportSub.SiteId.Value.ToString() });
                            paramIndex++;
                        }

                        if (reportSub.DepartmentId.HasValue)
                        {
                            filterParts.Add($"DepartmentId == @{paramIndex}");
                            parameters.Add(new { TypeName = "System.Guid", IsNullable = false, Value = reportSub.DepartmentId.Value.ToString() });
                            paramIndex++;
                        }

                        // Skip time filters for CurrentStatusReport and ServiceCheckReport
                        if (reportType.ReportType != ReportTypesEnum.CurrentStatusReport &&
                            reportType.ReportType != ReportTypesEnum.ServiceCheckReport)
                        {
                            if (reportSub.ReportStartTime.HasValue)
                            {
                                filterParts.Add($"StartDate == @{paramIndex}");
                                parameters.Add(new { TypeName = "System.DateTime", IsNullable = false, Value = reportSub.ReportStartTime.Value.ToString("o") });
                                paramIndex++;
                            }

                            if (reportSub.ReportEndTime.HasValue)
                            {
                                filterParts.Add($"EndDate == @{paramIndex}");
                                parameters.Add(new { TypeName = "System.DateTime", IsNullable = false, Value = reportSub.ReportEndTime.Value.ToString("o") });
                                paramIndex++;
                            }
                        }

                        var filterPredicate = filterParts.Any() ? string.Join(" && ", filterParts) : string.Empty;
                        var filterParameters = JsonConvert.SerializeObject(parameters);

                        var attachmentFiles = new List<AttachmentFile>();
                        foreach (var component in taskComponents)
                        {
                            var task = _serviceProvider.GetRequiredService<FleetXQ.BusinessLayer.Tasks.Task<ExportJobStatusDataObject>>();
                            task.TaskObject = _goTaskRunner.Init<ExportJobStatusDataObject>(
                                taskName,
                                new GOTaskRunner.GOTaskArgs<ExportJobStatusDataObject> { }
                            );

                            string modifiedFilterPredicate = filterPredicate;

                            // Handle filter predicate replacements for CurrentStatusReport
                            if (reportType.ReportType == ReportTypesEnum.CurrentStatusReport)
                            {
                                if (component is IVehicleCurrentStatusReportExportComponent)
                                {
                                    modifiedFilterPredicate = modifiedFilterPredicate
                                        .Replace("CustomerId", "Vehicle.Department.Site.CustomerId")
                                        .Replace("SiteId", "Vehicle.Department.SiteId")
                                        .Replace("DepartmentId", "Vehicle.DepartmentId");
                                }
                                else if (component is IDriverCurrentStatusReportExportComponent)
                                {
                                    modifiedFilterPredicate = modifiedFilterPredicate
                                        .Replace("CustomerId", "Driver.Person.CustomerId")
                                        .Replace("SiteId", "Driver.Person.SiteId")
                                        .Replace("DepartmentId", "Driver.Person.DepartmentId");
                                }
                            }

                            dynamic exportComponent = component;
                            await exportComponent.ExportAsync(task, modifiedFilterPredicate, filterParameters, null);

                            if (task.DataObject.TaskStatus == GOTaskStatusEnum.Complete)
                            {
                                string storageContainer = _configuration["StorageContainer"] == StringValues.Empty ? "files" : _configuration["StorageContainer"];
                                var fileName = task.DataObject.ExportedFileInternalName.Substring(task.DataObject.ExportedFileInternalName.IndexOf("/") + 1);
                                var fullPath = Path.Combine(_servicePath.WebRootPath(), storageContainer, "exports", fileName);

                                attachmentFiles.Add(new AttachmentFile
                                {
                                    FileLocation = fullPath,
                                    FileName = task.DataObject.ExportedFile
                                });
                            }
                        }

                        if (attachmentFiles.Any())
                        {
                            await SendEmail(emailList, reportSub.Subject, attachmentFiles);
                        }

                        var nextRunDateTime = reportSub.Frequency switch
                        {
                            FrequencyEnum.Daily => DateTime.UtcNow.AddDays(1),
                            FrequencyEnum.Weekly => DateTime.UtcNow.AddDays(7),
                            FrequencyEnum.Monthly => DateTime.UtcNow.AddMonths(1),
                            _ => DateTime.UtcNow.AddDays(1)
                        };

                        reportSub.NextRuntime = nextRunDateTime;
                        await _dataFacade.ReportSubscriptionDataProvider.SaveAsync(reportSub);
                    }
                }
                catch (Exception ex)
                {
                    //TODO: Add logging
                }
            }

            return true;
        }


        private async Task SendEmail(IEnumerable<string> emailList, string subject, IEnumerable<AttachmentFile> attachments)
        {
            SmtpClient client = new SmtpClient("email-smtp.us-east-1.amazonaws.com");
            MailAddress from = new MailAddress("<EMAIL>",
               "FleetXQ - Subscription",
            System.Text.Encoding.UTF8);
            foreach (var email in emailList)
            {
                MailAddress to = new MailAddress(email);
                MailMessage message = new MailMessage(from, to);
                try
                {
                    client.Port = 587;
                    // using AWS SES SMTP credentials
                    client.Credentials = new System.Net.NetworkCredential(_configuration["AWS_SES_USERNAME"], _configuration["AWS_SES_PASSWORD"]);
                    client.EnableSsl = true;

                    message.Body = "This is an automatically generated email. Please do not reply to this email. \r\n\r\nThe attachment is the requested Report. \r\n\r\nNOTE: This message and attachments are confidential, may contain legally privileged information, and are intended solely for the named addressee. If you receive this e-mail in error, please contact the sender and delete this message. Any unauthorized use of the contents of this email is expressly prohibited.";

                    message.Subject = subject;
                    message.SubjectEncoding = System.Text.Encoding.UTF8;

                    foreach (var attachmentFile in attachments)
                    {
                        if (!string.IsNullOrEmpty(attachmentFile.FileLocation) && File.Exists(attachmentFile.FileLocation))
                        {
                            Stream stream = File.OpenRead(attachmentFile.FileLocation);
                            var attachment = new Attachment(stream, attachmentFile.FileName);

                            message.Attachments.Add(attachment);
                        }
                    }

                    await client.SendMailAsync(message);

                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex.ToString());
                }
                finally
                {
                    message.Dispose();
                }
            }

            client.Dispose();
        }
    }

    public class AttachmentFile
    {
        public string FileLocation { get; set; }

        public string FileName { get; set; }
    }
}
