﻿(function () {
    //
    FleetXQ.Web.ViewModels.PersonVehicleAccessFormViewModelCustom = function (viewmodel) {
        var self = this;
        this.viewmodel = viewmodel;

        this.IsModifyCommandVisible = function () {
            return (self.viewmodel.StatusData.DisplayMode() == 'view' && !self.viewmodel.StatusData.IsEmpty() && self.viewmodel.DataStore && self.viewmodel.DataStore.CheckAuthorizationForEntityAndMethod('save'))
                && (ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.HAS_USERS_ACCESS) &&
                    ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.CAN_EDIT_VEHICLE_ACCESS));
        }

        this.IsSaveCommandVisible = ko.pureComputed(function () {
            return false;
        });

        // Method to manually refresh access calculations
        this.RefreshCalculations = function () {
            self.RefreshAccessCalculations();
        };



        // Method to perform the actual save operation
        this.PerformSave = function () {
            console.log("[NORMAL FORM] PROCEEDING with PerformSave - Setting permissionLevel = 3");

            var configuration = {};
            configuration.caller = self.viewmodel;
            configuration.contextId = self.viewmodel.contextId;
            configuration.successHandler = self.onNewSaveSuccess;
            configuration.errorHandler = self.onNewSaveError;
            configuration.personId = self.viewmodel.PersonObject().Data.Id();
            // CRITICAL: Always pass permissionLevel = 3 for normal driver access (bulk operations)
            configuration.permissionLevel = 3;

            // Get site accesses
            var siteAccesses = self.viewmodel.PersonToSiteVehicleNormalAccessViewItemsListViewModel.viewModelCollection()
                .map(function (item) { return item.CurrentObject(); })
                .filter(function (access) { return access.Data.HasAccess(); });
            configuration.personToSiteAccesses = self.getObjectsForSiteAccess(siteAccesses);

            // Get department accesses
            var departmentAccesses = self.viewmodel.PersonToDepartmentVehicleNormalAccessViewItemsListViewModel.viewModelCollection()
                .map(function (item) { return item.CurrentObject(); })
                .filter(function (access) { return access.Data.HasAccess(); });
            configuration.personToDepartmentAccesses = self.getObjectsForDepartmentAccess(departmentAccesses);

            // Get model accesses
            var modelAccesses = self.viewmodel.PersonToModelVehicleNormalAccessViewItemsListViewModel.viewModelCollection()
                .map(function (item) { return item.CurrentObject(); })
                .filter(function (access) { return access.Data.HasAccess(); });
            configuration.personToModelAccesses = self.getObjectsForModelAccess(modelAccesses);

            // Get vehicle accesses
            var vehicleAccesses = self.viewmodel.PersonToPerVehicleNormalAccessViewItemsListViewModel.viewModelCollection()
                .map(function (item) { return item.CurrentObject(); })
                .filter(function (access) { return access.Data.HasAccess(); });
            configuration.personToVehicleAccesses = self.getObjectsForVehicleAccess(vehicleAccesses);

            console.log("[NORMAL FORM] CALLING UpdateAccessesForPerson NOW with PermissionLevel:", configuration.permissionLevel);
            self.viewmodel.setIsBusy(true);
            self.viewmodel.controller.applicationController.getProxyForComponent("VehicleAccessUtilities").UpdateAccessesForPerson(configuration);
        };

        this.initialize = function () {
            /// override visibility of select all and deselect all buttons
            self.viewmodel.Commands.IsSelectAllCommandVisible = ko.pureComputed(function () {
                return self.viewmodel.StatusData.DisplayMode() == 'edit';
            });

            self.viewmodel.Commands.IsDeselectAllCommandVisible = ko.pureComputed(function () {
                return self.viewmodel.StatusData.DisplayMode() == 'edit';
            });

            // Override NewSave button visibility
            self.viewmodel.Commands.IsNewSaveCommandVisible = ko.pureComputed(function () {
                return self.viewmodel.StatusData.DisplayMode() == 'edit';
            });

            // Subscribe to tab changes
            self.viewmodel.StatusData.CurrentTabIndex.subscribe(function (newTabIndex) {
                if (self.viewmodel.StatusData.DisplayMode() == 'edit') {
                    self.RefreshAccessCalculations();
                }
            });

            // Override NewSave function
            self.viewmodel.NewSave = function () {
                // Check which tab we're on to determine what needs to be recalculated
                var currentTab = self.viewmodel.StatusData.CurrentTabIndex();

                if (currentTab == 1) { // Sites tab
                    // For sites, we need to recalculate departments, models, and vehicles
                    self.isSaving = true;
                    self.GetAccessesForDepartments();
                } else if (currentTab == 2) { // Departments tab
                    // For departments, we need to recalculate models and vehicles only
                    self.isSaving = true;
                    self.GetAccessesForModelsAsync();
                } else if (currentTab == 3) { // Models tab
                    // For models, we need to recalculate vehicles only
                    self.isSaving = true;
                    self.GetAccessesForVehiclesAsync();
                } else { // Vehicles tab or other
                    // No recalculation needed, just save
                    self.PerformSave();
                }
            };
        };

        this.getObjectsToSave = function (accesses) {
            // check if changes where made
            var hasChanges = false;
            var objectToSave = undefined;

            for (var i = 0; i < accesses.length; i++) {
                if (accesses[i].Data.IsDirty()) {
                    hasChanges = true;
                    break;
                }
            }

            if (hasChanges) {
                var dataset = new FleetXQ.Web.Model.DataSets.ObjectsDataSet();
                var objectToSave;

                for (var i = 0; i < accesses.length; i++) {
                    if (!accesses[i].Data.IsDirty()) {
                        continue;
                    }

                    objectToSave = accesses[i].Clone();
                    dataset.AddObject(objectToSave);
                }
            }

            return objectToSave;
        };

        this.getObjectsForSiteAccess = function (accesses) {
            var dataset = new FleetXQ.Web.Model.DataSets.ObjectsDataSet();

            if (accesses.length === 0) {
                var dummyObject = new FleetXQ.Web.Model.DataObjects.PersonToSiteVehicleNormalAccessViewObject();
                dummyObject.Data.IsDirty(true);
                dummyObject.Data.HasAccess(false);
                dummyObject.contextIds.push(self.viewmodel.contextId);
                dataset.AddObject(dummyObject);
                return dummyObject;
            }

            var objectToSave = undefined;
            for (var i = 0; i < accesses.length; i++) {
                objectToSave = accesses[i].Clone();
                objectToSave.Data.IsDirty(true);
                objectToSave.contextIds.push(self.viewmodel.contextId);
                dataset.AddObject(objectToSave);
            }

            return objectToSave;
        };

        this.getObjectsForDepartmentAccess = function (accesses) {
            var dataset = new FleetXQ.Web.Model.DataSets.ObjectsDataSet();

            if (accesses.length === 0) {
                var dummyObject = new FleetXQ.Web.Model.DataObjects.PersonToDepartmentVehicleNormalAccessViewObject();
                dummyObject.Data.IsDirty(true);
                dummyObject.Data.HasAccess(false);
                dummyObject.contextIds.push(self.viewmodel.contextId);
                dataset.AddObject(dummyObject);
                return dummyObject;
            }

            var objectToSave = undefined;
            for (var i = 0; i < accesses.length; i++) {
                objectToSave = accesses[i].Clone();
                objectToSave.Data.IsDirty(true);
                objectToSave.contextIds.push(self.viewmodel.contextId);
                dataset.AddObject(objectToSave);
            }

            return objectToSave;
        };

        this.getObjectsForModelAccess = function (accesses) {
            var dataset = new FleetXQ.Web.Model.DataSets.ObjectsDataSet();

            if (accesses.length === 0) {
                var dummyObject = new FleetXQ.Web.Model.DataObjects.PersonToModelVehicleNormalAccessViewObject();
                dummyObject.Data.IsDirty(true);
                dummyObject.Data.HasAccess(false);
                dummyObject.contextIds.push(self.viewmodel.contextId);
                dataset.AddObject(dummyObject);
                return dummyObject;
            }

            var objectToSave = undefined;
            for (var i = 0; i < accesses.length; i++) {
                objectToSave = accesses[i].Clone();
                objectToSave.Data.IsDirty(true);
                objectToSave.contextIds.push(self.viewmodel.contextId);
                dataset.AddObject(objectToSave);
            }

            return objectToSave;
        };

        this.getObjectsForVehicleAccess = function (accesses) {
            var dataset = new FleetXQ.Web.Model.DataSets.ObjectsDataSet();

            if (accesses.length === 0) {
                var dummyObject = new FleetXQ.Web.Model.DataObjects.PersonToPerVehicleNormalAccessViewObject();
                dummyObject.Data.IsDirty(true);
                dummyObject.Data.HasAccess(false);
                dummyObject.contextIds.push(self.viewmodel.contextId);
                dataset.AddObject(dummyObject);
                return dummyObject;
            }

            var objectToSave = undefined;
            for (var i = 0; i < accesses.length; i++) {
                objectToSave = accesses[i].Clone();
                objectToSave.Data.IsDirty(true);
                objectToSave.contextIds.push(self.viewmodel.contextId);
                dataset.AddObject(objectToSave);
            }

            return objectToSave;
        };

        this.RefreshAccessCalculations = function () {
            // Create a promise for the async operations
            var asyncPromise = new Promise(function (resolve) {
                self.resolveAsyncOperations = resolve;
            });

            // Start the async chain to get correct calculations
            self.GetAccessesForDepartments();

            // Wait for async operations to complete
            asyncPromise.then(function () {
                // Calculations complete, no further action needed
            });
        };

        this.GetAccessesForDepartments = function () {
            var configuration = {};
            configuration.caller = self.viewmodel;
            configuration.contextId = self.viewmodel.contextId;
            configuration.successHandler = self.onGetAccessesForDepartmentsSuccess;
            configuration.errorHandler = self.viewmodel.ShowError;
            configuration.personId = self.viewmodel.PersonObject().Data.Id();
            configuration.permissionLevel = 3;

            // Store current department selection state before making the API call
            var currentDepartmentSelections = {};
            var currentCollection = self.viewmodel.PersonToDepartmentVehicleNormalAccessViewItemsListViewModel.viewModelCollection();
            currentCollection.forEach(function (item) {
                var key = item.CurrentObject().Data.DepartmentId();
                currentDepartmentSelections[key] = item.CurrentObject().Data.HasAccess();
            });
            // Store the selections in a property accessible to the success callback
            self.currentDepartmentSelections = currentDepartmentSelections;

            var siteAccesses = self.viewmodel.PersonToSiteVehicleNormalAccessViewItemsListViewModel.viewModelCollection()
                .map(function (item) { return item.CurrentObject(); })
                .filter(function (access) { return access.Data.HasAccess(); });

            var objectToSave = self.getObjectsForSiteAccess(siteAccesses);

            if (objectToSave) {
                configuration.contextId = self.viewmodel.contextId;
                configuration.personToSiteAccesses = objectToSave;
                self.viewmodel.setIsBusy(true);
                self.viewmodel.controller.applicationController.getProxyForComponent("VehicleAccessUtilities").GetAccessesForDepartments(configuration);
            } else {
                self.viewmodel.setIsBusy(false);
                return false;
            }
        };

        this.GetAccessesForModelsAsync = function () {
            var configuration = {};
            configuration.caller = self.viewmodel;
            configuration.contextId = self.viewmodel.contextId;
            configuration.successHandler = self.onGetAccessesForModelsAsyncSuccess;
            configuration.errorHandler = self.viewmodel.ShowError;
            configuration.personId = self.viewmodel.PersonObject().Data.Id();
            configuration.permissionLevel = 3;

            // Store current model selection state before making the API call
            var currentModelSelections = {};
            var currentCollection = self.viewmodel.PersonToModelVehicleNormalAccessViewItemsListViewModel.viewModelCollection();
            currentCollection.forEach(function (item) {
                var key = item.CurrentObject().Data.ModelId() + '_' + item.CurrentObject().Data.DepartmentId();
                currentModelSelections[key] = item.CurrentObject().Data.HasAccess();
            });
            // Store the selections in a property accessible to the success callback
            self.currentModelSelections = currentModelSelections;

            var departmentAccesses = self.viewmodel.PersonToDepartmentVehicleNormalAccessViewItemsListViewModel.viewModelCollection()
                .map(function (item) { return item.CurrentObject(); })
                .filter(function (access) { return access.Data.HasAccess(); });

            var objectToSave = self.getObjectsForDepartmentAccess(departmentAccesses);

            if (objectToSave) {
                configuration.contextId = self.viewmodel.contextId;
                configuration.personToDepartmentAccesses = objectToSave;
                self.viewmodel.setIsBusy(true);
                self.viewmodel.controller.applicationController.getProxyForComponent("VehicleAccessUtilities").GetAccessesForModels(configuration);
            } else {
                self.viewmodel.setIsBusy(false);
                return false;
            }
        };

        this.GetAccessesForVehiclesAsync = function () {
            var configuration = {};
            configuration.caller = self.viewmodel;
            configuration.contextId = self.viewmodel.contextId;
            configuration.successHandler = self.onGetAccessesForVehiclesAsyncSuccess;
            configuration.errorHandler = self.viewmodel.ShowError;
            configuration.personId = self.viewmodel.PersonObject().Data.Id();
            configuration.permissionLevel = 3;

            // Store current vehicle selection state before making the API call
            var currentVehicleSelections = {};
            var currentCollection = self.viewmodel.PersonToPerVehicleNormalAccessViewItemsListViewModel.viewModelCollection();
            currentCollection.forEach(function (item) {
                var key = item.CurrentObject().Data.VehicleId();
                currentVehicleSelections[key] = item.CurrentObject().Data.HasAccess();
            });
            // Store the selections in a property accessible to the success callback
            self.currentVehicleSelections = currentVehicleSelections;

            var departmentAccesses = self.viewmodel.PersonToDepartmentVehicleNormalAccessViewItemsListViewModel.viewModelCollection()
                .map(function (item) { return item.CurrentObject(); })
                .filter(function (access) { return access.Data.HasAccess(); });

            var modelAccesses = self.viewmodel.PersonToModelVehicleNormalAccessViewItemsListViewModel.viewModelCollection()
                .map(function (item) { return item.CurrentObject(); })
                .filter(function (access) { return access.Data.HasAccess(); });

            var modelObjectToSave = self.getObjectsForModelAccess(modelAccesses);
            var deptObjectToSave = self.getObjectsForDepartmentAccess(departmentAccesses);

            if (deptObjectToSave && modelObjectToSave) {
                configuration.contextId = self.viewmodel.contextId;
                configuration.personToDepartmentAccesses = deptObjectToSave;
                configuration.personToModelAccesses = modelObjectToSave;
                self.viewmodel.setIsBusy(true);
                self.viewmodel.controller.applicationController.getProxyForComponent("VehicleAccessUtilities").GetAccessesForVehicles(configuration);
            } else {
                self.viewmodel.setIsBusy(false);
                return false;
            }
        };

        this.onGetAccessesForModelsAsyncSuccess = function (data) {
            if (data) {
                // Get the stored selection state
                var currentModelSelections = self.currentModelSelections || {};
                var currentCollection = self.viewmodel.PersonToModelVehicleNormalAccessViewItemsListViewModel.viewModelCollection();
                var dataCollection = self.viewmodel.PersonToModelVehicleNormalAccessViewItemsListViewModel.PersonToModelVehicleNormalAccessViewObjectCollection();

                // Ensure data collection is initialized
                if (!dataCollection) {
                    self.viewmodel.PersonToModelVehicleNormalAccessViewItemsListViewModel.PersonToModelVehicleNormalAccessViewObjectCollection(new GO.observableArray());
                }

                // Create a set of keys from the new data for efficient lookup
                var newDataKeys = new Set();
                data.forEach(function (newItem) {
                    var key = newItem.Data.ModelId() + '_' + newItem.Data.DepartmentId();
                    newDataKeys.add(key);
                });

                // Remove viewmodels that are no longer in the data
                for (var i = currentCollection.length - 1; i >= 0; i--) {
                    var vm = currentCollection[i];
                    // Add null checks to prevent errors
                    if (vm && vm.CurrentObject && vm.CurrentObject().Data) {
                        var vmKey = vm.CurrentObject().Data.ModelId() + '_' + vm.CurrentObject().Data.DepartmentId();
                        if (!newDataKeys.has(vmKey)) {
                            // Use the framework's removeItem method with error handling
                            try {
                                self.viewmodel.PersonToModelVehicleNormalAccessViewItemsListViewModel.removeItem(vm);
                            } catch (error) {
                                // Fallback to manual removal
                                vm.release();
                                currentCollection.splice(i, 1);
                            }
                        }
                    } else {
                        // Remove invalid viewmodels
                        currentCollection.splice(i, 1);
                    }
                }

                // Process new data and preserve existing selections
                data.forEach(function (newItem) {
                    var key = newItem.Data.ModelId() + '_' + newItem.Data.DepartmentId();

                    // Find the corresponding viewmodel object in the current collection
                    var existingViewModel = currentCollection.find(function (vm) {
                        return vm && vm.CurrentObject && vm.CurrentObject().Data &&
                            vm.CurrentObject().Data.ModelId() === newItem.Data.ModelId() &&
                            vm.CurrentObject().Data.DepartmentId() === newItem.Data.DepartmentId();
                    });

                    if (existingViewModel) {
                        // Update the existing viewmodel's HasAccess property
                        if (currentModelSelections.hasOwnProperty(key)) {
                            existingViewModel.CurrentObject().Data.HasAccess(currentModelSelections[key]);
                        } else {
                            existingViewModel.CurrentObject().Data.HasAccess(newItem.Data.HasAccess());
                        }

                        // Force UI update by triggering the observable
                        existingViewModel.CurrentObject().Data.HasAccess.valueHasMutated();
                    } else {
                        // Ensure the data object is properly set up
                        newItem.ObjectsDataSet = self.viewmodel.controller.ObjectsDataSet;

                        // Add to the data collection first
                        var dataCollection = self.viewmodel.PersonToModelVehicleNormalAccessViewItemsListViewModel.PersonToModelVehicleNormalAccessViewObjectCollection();
                        if (dataCollection) {
                            dataCollection.push(newItem);
                        }

                        // Add to the collection using the proper method - addViewModel expects a data object
                        self.viewmodel.PersonToModelVehicleNormalAccessViewItemsListViewModel.addViewModel(newItem);

                        // Find the newly added viewmodel and update its HasAccess property
                        var newViewModel = currentCollection.find(function (vm) {
                            return vm && vm.CurrentObject && vm.CurrentObject().Data &&
                                vm.CurrentObject().Data.ModelId() === newItem.Data.ModelId() &&
                                vm.CurrentObject().Data.DepartmentId() === newItem.Data.DepartmentId();
                        });

                        if (newViewModel) {
                            // Set HasAccess based on stored selections or new data
                            if (currentModelSelections.hasOwnProperty(key)) {
                                newViewModel.CurrentObject().Data.HasAccess(currentModelSelections[key]);
                                newViewModel.CurrentObject().Data.HasAccess.valueHasMutated();
                            }
                        }
                    }
                });

                // Force collection change notification to update UI
                self.viewmodel.PersonToModelVehicleNormalAccessViewItemsListViewModel.viewModelCollection.valueHasMutated();

                // Also force data collection notification - check if it's a GO.observableArray or ko.observable
                if (dataCollection) {
                    if (typeof dataCollection.valueHasMutated === 'function') {
                        dataCollection.valueHasMutated();
                    } else if (typeof dataCollection.notifySubscribers === 'function') {
                        dataCollection.notifySubscribers();
                    } else if (typeof dataCollection.changedHandlerOn !== 'undefined') {
                        // This might be a GO.observableArray - trigger change notification
                        dataCollection.changedHandlerOn = false;
                        dataCollection.changedHandlerOn = true;
                    }
                }

                // After updating models, get vehicle accesses
                self.GetAccessesForVehiclesAsync();
            } else {
                self.viewmodel.setIsBusy(false);
                // Only call PerformSave if we're not in the middle of a chain
                if (self.isSaving && !self.isInChain) {
                    self.isSaving = false;
                    self.PerformSave();
                }
            }
        };

        this.onGetAccessesForVehiclesAsyncSuccess = function (data) {
            if (data) {
                // Get the stored selection state
                var currentVehicleSelections = self.currentVehicleSelections || {};
                var currentCollection = self.viewmodel.PersonToPerVehicleNormalAccessViewItemsListViewModel.viewModelCollection();
                var dataCollection = self.viewmodel.PersonToPerVehicleNormalAccessViewItemsListViewModel.PersonToPerVehicleNormalAccessViewObjectCollection();

                // Ensure data collection is initialized
                if (!dataCollection) {
                    self.viewmodel.PersonToPerVehicleNormalAccessViewItemsListViewModel.PersonToPerVehicleNormalAccessViewObjectCollection(new GO.observableArray());
                }

                // Create a set of keys from the new data for efficient lookup
                var newDataKeys = new Set();
                data.forEach(function (newItem) {
                    var key = newItem.Data.VehicleId();
                    newDataKeys.add(key);
                });

                // Remove viewmodels that are no longer in the data
                for (var i = currentCollection.length - 1; i >= 0; i--) {
                    var vm = currentCollection[i];
                    // Add null checks to prevent errors
                    if (vm && vm.CurrentObject && vm.CurrentObject().Data) {
                        var vmKey = vm.CurrentObject().Data.VehicleId();
                        if (!newDataKeys.has(vmKey)) {
                            // Use the framework's removeItem method with error handling
                            try {
                                self.viewmodel.PersonToPerVehicleNormalAccessViewItemsListViewModel.removeItem(vm);
                            } catch (error) {
                                // Fallback to manual removal
                                vm.release();
                                currentCollection.splice(i, 1);
                            }
                        }
                    } else {
                        // Remove invalid viewmodels
                        currentCollection.splice(i, 1);
                    }
                }

                // Process new data and preserve existing selections
                data.forEach(function (newItem) {
                    var key = newItem.Data.VehicleId();

                    // Find the corresponding viewmodel object in the current collection
                    var existingViewModel = currentCollection.find(function (vm) {
                        return vm && vm.CurrentObject && vm.CurrentObject().Data &&
                            vm.CurrentObject().Data.VehicleId() === newItem.Data.VehicleId();
                    });

                    if (existingViewModel) {
                        // Update the existing viewmodel's HasAccess property
                        if (currentVehicleSelections.hasOwnProperty(key)) {
                            existingViewModel.CurrentObject().Data.HasAccess(currentVehicleSelections[key]);
                        } else {
                            existingViewModel.CurrentObject().Data.HasAccess(newItem.Data.HasAccess());
                        }

                        // Force UI update by triggering the observable
                        existingViewModel.CurrentObject().Data.HasAccess.valueHasMutated();
                    } else {
                        // Ensure the data object is properly set up
                        newItem.ObjectsDataSet = self.viewmodel.controller.ObjectsDataSet;

                        // Add to the data collection first
                        var dataCollection = self.viewmodel.PersonToPerVehicleNormalAccessViewItemsListViewModel.PersonToPerVehicleNormalAccessViewObjectCollection();
                        if (dataCollection) {
                            dataCollection.push(newItem);
                        }

                        // Add to the collection using the proper method - addViewModel expects a data object
                        self.viewmodel.PersonToPerVehicleNormalAccessViewItemsListViewModel.addViewModel(newItem);

                        // Find the newly added viewmodel and update its HasAccess property
                        var newViewModel = currentCollection.find(function (vm) {
                            return vm && vm.CurrentObject && vm.CurrentObject().Data &&
                                vm.CurrentObject().Data.VehicleId() === newItem.Data.VehicleId();
                        });

                        if (newViewModel) {
                            // Set HasAccess based on stored selections or new data
                            if (currentVehicleSelections.hasOwnProperty(key)) {
                                newViewModel.CurrentObject().Data.HasAccess(currentVehicleSelections[key]);
                                newViewModel.CurrentObject().Data.HasAccess.valueHasMutated();
                            }
                        }
                    }
                });

                // Force collection change notification to update UI
                self.viewmodel.PersonToPerVehicleNormalAccessViewItemsListViewModel.viewModelCollection.valueHasMutated();

                // Also force data collection notification - check if it's a GO.observableArray or ko.observable
                if (dataCollection) {
                    if (typeof dataCollection.valueHasMutated === 'function') {
                        dataCollection.valueHasMutated();
                    } else if (typeof dataCollection.notifySubscribers === 'function') {
                        dataCollection.notifySubscribers();
                    } else if (typeof dataCollection.changedHandlerOn !== 'undefined') {
                        // This might be a GO.observableArray - trigger change notification
                        dataCollection.changedHandlerOn = false;
                        dataCollection.changedHandlerOn = true;
                    }
                }
            }
            self.viewmodel.setIsBusy(false);

            // Check if we're in a save operation - this is the end of the chain
            if (self.isSaving) {
                self.isSaving = false;
                self.isInChain = false;
                self.PerformSave();
            } else {
                // Resolve the promise when all async operations are complete
                if (self.resolveAsyncOperations) {
                    self.resolveAsyncOperations();
                }
            }
        };

        this.onGetAccessesForDepartmentsSuccess = function (data) {
            if (data) {
                // Get the stored selection state
                var currentDepartmentSelections = self.currentDepartmentSelections || {};
                var currentCollection = self.viewmodel.PersonToDepartmentVehicleNormalAccessViewItemsListViewModel.viewModelCollection();
                var dataCollection = self.viewmodel.PersonToDepartmentVehicleNormalAccessViewItemsListViewModel.PersonToDepartmentVehicleNormalAccessViewObjectCollection();

                // Ensure data collection is initialized
                if (!dataCollection) {
                    self.viewmodel.PersonToDepartmentVehicleNormalAccessViewItemsListViewModel.PersonToDepartmentVehicleNormalAccessViewObjectCollection(new GO.observableArray());
                }

                // Create a set of keys from the new data for efficient lookup
                var newDataKeys = new Set();
                data.forEach(function (newItem) {
                    var key = newItem.Data.DepartmentId();
                    newDataKeys.add(key);
                });

                // Remove viewmodels that are no longer in the data
                for (var i = currentCollection.length - 1; i >= 0; i--) {
                    var vm = currentCollection[i];
                    // Add null checks to prevent errors
                    if (vm && vm.CurrentObject && vm.CurrentObject().Data) {
                        var vmKey = vm.CurrentObject().Data.DepartmentId();
                        if (!newDataKeys.has(vmKey)) {
                            // Use the framework's removeItem method with error handling
                            try {
                                self.viewmodel.PersonToDepartmentVehicleNormalAccessViewItemsListViewModel.removeItem(vm);
                            } catch (error) {
                                // Fallback to manual removal
                                vm.release();
                                currentCollection.splice(i, 1);
                            }
                        }
                    } else {
                        // Remove invalid viewmodels
                        currentCollection.splice(i, 1);
                    }
                }

                // Process new data and preserve existing selections
                data.forEach(function (newItem) {
                    var key = newItem.Data.DepartmentId();

                    // Find the corresponding viewmodel object in the current collection
                    var existingViewModel = currentCollection.find(function (vm) {
                        return vm && vm.CurrentObject && vm.CurrentObject().Data &&
                            vm.CurrentObject().Data.DepartmentId() === newItem.Data.DepartmentId();
                    });

                    if (existingViewModel) {
                        // Update the existing viewmodel's HasAccess property
                        if (currentDepartmentSelections.hasOwnProperty(key)) {
                            existingViewModel.CurrentObject().Data.HasAccess(currentDepartmentSelections[key]);
                        } else {
                            existingViewModel.CurrentObject().Data.HasAccess(newItem.Data.HasAccess());
                        }

                        // Force UI update by triggering the observable
                        existingViewModel.CurrentObject().Data.HasAccess.valueHasMutated();
                    } else {
                        // Ensure the data object is properly set up
                        newItem.ObjectsDataSet = self.viewmodel.controller.ObjectsDataSet;

                        // Add to the data collection first
                        var dataCollection = self.viewmodel.PersonToDepartmentVehicleNormalAccessViewItemsListViewModel.PersonToDepartmentVehicleNormalAccessViewObjectCollection();
                        if (dataCollection) {
                            dataCollection.push(newItem);
                        }

                        // Add to the collection using the proper method - addViewModel expects a data object
                        self.viewmodel.PersonToDepartmentVehicleNormalAccessViewItemsListViewModel.addViewModel(newItem);

                        // Find the newly added viewmodel and update its HasAccess property
                        var newViewModel = currentCollection.find(function (vm) {
                            return vm && vm.CurrentObject && vm.CurrentObject().Data &&
                                vm.CurrentObject().Data.DepartmentId() === newItem.Data.DepartmentId();
                        });

                        if (newViewModel) {
                            // Set HasAccess based on stored selections or new data
                            if (currentDepartmentSelections.hasOwnProperty(key)) {
                                newViewModel.CurrentObject().Data.HasAccess(currentDepartmentSelections[key]);
                                newViewModel.CurrentObject().Data.HasAccess.valueHasMutated();
                            }
                        }
                    }
                });

                // Force collection change notification to update UI
                self.viewmodel.PersonToDepartmentVehicleNormalAccessViewItemsListViewModel.viewModelCollection.valueHasMutated();

                // Also force data collection notification - check if it's a GO.observableArray or ko.observable
                if (dataCollection) {
                    if (typeof dataCollection.valueHasMutated === 'function') {
                        dataCollection.valueHasMutated();
                    } else if (typeof dataCollection.notifySubscribers === 'function') {
                        dataCollection.notifySubscribers();
                    } else if (typeof dataCollection.changedHandlerOn !== 'undefined') {
                        // This might be a GO.observableArray - trigger change notification
                        dataCollection.changedHandlerOn = false;
                        dataCollection.changedHandlerOn = true;
                    }
                }

                // After updating departments, get model accesses
                self.GetAccessesForModelsAsync();
            } else {
                self.viewmodel.setIsBusy(false);
                // Only call PerformSave if we're not in the middle of a chain
                if (self.isSaving && !self.isInChain) {
                    self.isSaving = false;
                    self.PerformSave();
                }
            }
        };

        this.onBeforeSave = function () {
            // SMART GUARD: Check if supervisor form should take priority
            var canUnlockVehicle = self.viewmodel.CurrentObject().Data.CanUnlockVehicle();
            var normalDriverAccess = self.viewmodel.CurrentObject().Data.NormalDriverAccess();
            var vorActivateDeactivate = self.viewmodel.CurrentObject().Data.VORActivateDeactivate();
            var supervisorShouldHandle = canUnlockVehicle || normalDriverAccess || vorActivateDeactivate;

            // Check normal form conditions
            var hasDriver = self.viewmodel.CurrentObject().getDriver();
            var hasCard = hasDriver && self.viewmodel.CurrentObject().getDriver().Data.Card();
            var isActiveDriver = self.viewmodel.CurrentObject().Data.IsActiveDriver();
            var isOnDemand = self.viewmodel.CurrentObject().Data.OnDemand();
            var shouldShowNormal = hasDriver && hasCard && isActiveDriver && !isOnDemand;

            console.log("[NORMAL FORM] onBeforeSave called - Supervisor should handle:", supervisorShouldHandle, "Normal should show:", shouldShowNormal);
            console.log("[NORMAL FORM] Current tab index:", self.viewmodel.StatusData.CurrentTabIndex());

            if (supervisorShouldHandle) {
                console.log("[NORMAL FORM] GUARD: Supervisor form should handle this, skipping");
                return true; // Let supervisor form handle it
            }

            if (!shouldShowNormal) {
                console.log("[NORMAL FORM] GUARD: Normal form conditions not met, skipping");
                return true;
            }

            console.log("[NORMAL FORM] Setting permissionLevel = 3");

            self.viewmodel.setIsBusy(true);

            var configuration = {};
            configuration.personId = self.viewmodel.CurrentObject().Data.Id();
            configuration.successHandler = self.onAccessChangedSuccess;
            configuration.errorHandler = self.onAccessChangedError;
            // CRITICAL: Always pass permissionLevel = 3 for normal driver access (individual operations)
            configuration.PermissionLevel = 3;

            if (self.viewmodel.StatusData.CurrentTabIndex() == 1) { // Sites
                var accesses = self.viewmodel.PersonObject().getPersonToSiteVehicleNormalAccessViewItems();

                var objectToSave = self.getObjectsToSave(accesses);

                if (objectToSave) {
                    configuration.contextId = objectToSave.contextIds[0];
                    // here we pass an object, and not the collection. The collection is saved because in same dataset
                    configuration.updatedPersonToSiteAccesses = objectToSave;
                    self.vehicleAccessUtilitiesProxy.UpdateVehicleSiteAccessesForPerson(configuration);
                }
                else {
                    self.viewmodel.EndEdit();
                    return false;
                }
            }
            else if (self.viewmodel.StatusData.CurrentTabIndex() == 2) { // Departments
                var accesses = self.viewmodel.PersonObject().getPersonToDepartmentVehicleNormalAccessViewItems();

                var objectToSave = self.getObjectsToSave(accesses);

                if (objectToSave) {
                    configuration.contextId = objectToSave.contextIds[0];
                    // here we pass an object, and not the collection. The collection is saved because in same dataset
                    configuration.updatedPersonToDepartmentAccesses = objectToSave;
                    self.vehicleAccessUtilitiesProxy.UpdateVehicleDepartmentAccessesForPerson(configuration);
                }
                else {
                    self.viewmodel.EndEdit();
                    return false;
                }
            }
            else if (self.viewmodel.StatusData.CurrentTabIndex() == 3) { // Models
                var accesses = self.viewmodel.PersonObject().getPersonToModelVehicleNormalAccessViewItems();

                var objectToSave = self.getObjectsToSave(accesses);

                if (objectToSave) {
                    configuration.contextId = objectToSave.contextIds[0];
                    // here we pass an object, and not the collection. The collection is saved because in same dataset
                    configuration.updateModelAccesses = objectToSave;
                    self.vehicleAccessUtilitiesProxy.UpdateVehicleModelAccessesForPerson(configuration);
                }
                else {
                    self.viewmodel.EndEdit();
                    return false;
                }
            }
            else if (self.viewmodel.StatusData.CurrentTabIndex() == 4) {  // Vehicles
                var accesses = self.viewmodel.PersonObject().getPersonToPerVehicleNormalAccessViewItems();

                var objectToSave = self.getObjectsToSave(accesses);

                if (objectToSave) {
                    configuration.contextId = objectToSave.contextIds[0];
                    // here we pass an object, and not the collection. The collection is saved because in same dataset
                    configuration.updatePerVehicleAccesses = objectToSave;
                    self.vehicleAccessUtilitiesProxy.UpdateVehiclePerVehicleAccessesForPerson(configuration);
                }
                else {
                    self.viewmodel.EndEdit();
                    return false;
                }
            }
        }

        this.onAccessChangedSuccess = function () {
            // reload all vehicle access data
            var configuration = {};
            configuration.filterPredicate = 'PersonId == @0';
            configuration.filterParameters = '[{ "TypeName" : "System.Guid", "Value" : "' + self.viewmodel.CurrentObject().Data.Id() + '" }]';
            configuration.include = 'Site';
            self.viewmodel.PersonToSiteVehicleNormalAccessViewItemsListViewModel.LoadPersonToSiteVehicleNormalAccessViewObjectCollection(configuration);

            configuration = {};
            configuration.filterPredicate = 'PersonId == @0';
            configuration.filterParameters = '[{ "TypeName" : "System.Guid", "Value" : "' + self.viewmodel.CurrentObject().Data.Id() + '" }]';
            configuration.include = 'Department';
            self.viewmodel.PersonToDepartmentVehicleNormalAccessViewItemsListViewModel.LoadPersonToDepartmentVehicleNormalAccessViewObjectCollection(configuration);

            configuration = {};
            configuration.filterPredicate = 'PersonId == @0';
            configuration.filterParameters = '[{ "TypeName" : "System.Guid", "Value" : "' + self.viewmodel.CurrentObject().Data.Id() + '" }]';
            configuration.include = 'Model';
            self.viewmodel.PersonToModelVehicleNormalAccessViewItemsListViewModel.LoadPersonToModelVehicleNormalAccessViewObjectCollection(configuration);

            configuration = {};
            configuration.filterPredicate = 'PersonId == @0';
            configuration.filterParameters = '[{ "TypeName" : "System.Guid", "Value" : "' + self.viewmodel.CurrentObject().Data.Id() + '" }]';
            configuration.include = 'Vehicle';

            self.viewmodel.PersonToPerVehicleNormalAccessViewItemsListViewModel.LoadPersonToPerVehicleNormalAccessViewObjectCollection(configuration);

            self.viewmodel.EndEdit();
            self.viewmodel.StatusData.IsBusy(false);
        };

        this.onAccessChangedError = function () {
            self.viewmodel.ShowError("Failed to change vehicle access", "Error");
            self.viewmodel.StatusData.IsBusy(false);
        };

        this.onNewSaveSuccess = function (data) {
            self.viewmodel.setIsBusy(false);
            self.viewmodel.EndEdit();

            // Reload all data from database after save
            // var configuration = {};
            // configuration.filterPredicate = 'PersonId == @0';
            // configuration.filterParameters = '[{ "TypeName" : "System.Guid", "Value" : "' + self.viewmodel.CurrentObject().Data.Id() + '" }]';
            // configuration.include = 'Site';
            // self.viewmodel.PersonToSiteVehicleNormalAccessViewItemsListViewModel.LoadPersonToSiteVehicleNormalAccessViewObjectCollection(configuration);

            // configuration = {};
            // configuration.filterPredicate = 'PersonId == @0';
            // configuration.filterParameters = '[{ "TypeName" : "System.Guid", "Value" : "' + self.viewmodel.CurrentObject().Data.Id() + '" }]';
            // configuration.include = 'Department';
            // self.viewmodel.PersonToDepartmentVehicleNormalAccessViewItemsListViewModel.LoadPersonToDepartmentVehicleNormalAccessViewObjectCollection(configuration);

            // configuration = {};
            // configuration.filterPredicate = 'PersonId == @0';
            // configuration.filterParameters = '[{ "TypeName" : "System.Guid", "Value" : "' + self.viewmodel.CurrentObject().Data.Id() + '" }]';
            // configuration.include = 'Model';
            // self.viewmodel.PersonToModelVehicleNormalAccessViewItemsListViewModel.LoadPersonToModelVehicleNormalAccessViewObjectCollection(configuration);

            // configuration = {};
            // configuration.filterPredicate = 'PersonId == @0';
            // configuration.filterParameters = '[{ "TypeName" : "System.Guid", "Value" : "' + self.viewmodel.CurrentObject().Data.Id() + '" }]';
            // configuration.include = 'Vehicle';
            // self.viewmodel.PersonToPerVehicleNormalAccessViewItemsListViewModel.LoadPersonToPerVehicleNormalAccessViewObjectCollection(configuration);
        };

        this.onNewSaveError = function (error) {
            self.viewmodel.setIsBusy(false);
            self.viewmodel.ShowError("Failed to save vehicle access", "Error");
        };
    }
}());