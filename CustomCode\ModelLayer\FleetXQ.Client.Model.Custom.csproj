﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net7.0</TargetFramework>
    <OutputType>Library</OutputType>
    <GeneratePackageOnBuild>True</GeneratePackageOnBuild>
  </PropertyGroup>
  <PropertyGroup>
    <VersionPrefix>*********</VersionPrefix>
    <VersionSuffix></VersionSuffix>
    <Authors>Generative Objects</Authors>
    <Description>FleetXQ Custom SDK</Description>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="GenerativeObjects.Practices.LayerSupportClasses" Version="2.0.3" />

    <PackageReference Include="GenerativeObjects.Services" Version="2.0.3" />
    <PackageReference Include="Microsoft.CSharp" Version="4.7.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.2" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\GeneratedCode\DataLayer\FleetXQ.Data.DataObjects.csproj" />
  </ItemGroup>
</Project>