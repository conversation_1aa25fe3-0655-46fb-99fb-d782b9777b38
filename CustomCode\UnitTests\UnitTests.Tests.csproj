<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net7.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.5.0" />
    <PackageReference Include="NUnit" Version="3.13.3" />
    <PackageReference Include="NUnit3TestAdapter" Version="4.4.2" />
    <PackageReference Include="NUnit.Analyzers" Version="3.6.1" />
    <PackageReference Include="coverlet.collector" Version="3.2.0" />
    <PackageReference Include="NSubstitute" Version="5.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="7.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\BusinessLayerServerComponents\FleetXQ.BusinessLayer.Components.Server.Custom.csproj" />
    <ProjectReference Include="..\DataLayer\FleetXQ.Data.DataObjects.Custom.csproj" />
    <ProjectReference Include="..\DataLayerDataProviderExtensions\FleetXQ.Data.DataProvidersExtensions.Custom.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="FleetXQ.Data.DataObjects">
      <HintPath>..\..\GeneratedCode\DataLayer\bin\Debug\net7.0\FleetXQ.Data.DataObjects.dll</HintPath>
    </Reference>
    <Reference Include="FleetXQ.BusinessLayer.ORMSupportClasses">
      <HintPath>..\..\GeneratedCode\BusinessLayerORMSupportClasses\bin\Debug\net7.0\FleetXQ.BusinessLayer.ORMSupportClasses.dll</HintPath>
    </Reference>
    <Reference Include="GenerativeObjects.Practices.ORMSupportClasses">
      <HintPath>..\..\GeneratedCode\BusinessLayerORMSupportClasses\bin\Debug\net7.0\GenerativeObjects.Practices.ORMSupportClasses.dll</HintPath>
    </Reference>
    <Reference Include="GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components">
      <HintPath>..\..\GeneratedCode\BusinessLayerORMSupportClasses\bin\Debug\net7.0\GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components.dll</HintPath>
    </Reference>
  </ItemGroup>

</Project> 