﻿using FleetXQ.BusinessLayer.Components.Server.Custom;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer.Extensions;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Threading.Tasks;

namespace FleetXQ.Data.DataProvidersExtensions.Custom
{
    public class VehicleLockoutDataProviderExtension : IDataProviderExtension<VehicleLockoutDataObject>
    {
        private readonly IDataFacade _dataFacade;
        private readonly IServiceProvider _serviceProvider;
        public VehicleLockoutDataProviderExtension(IDataFacade dataFacade, IServiceProvider serviceProvider)
        {

            _dataFacade = dataFacade;
            _serviceProvider = serviceProvider;

        }
        public void Init(IDataProviderExtensionProvider dataProvider)
        {
            dataProvider.OnAfterSaveDataSet += DataProvider_OnAfterSaveDataSetAsync;
        }
        private async Task DataProvider_OnAfterSaveDataSetAsync(OnAfterSaveDataSetEventArgs e)
        {
            // return if update
            if (!e.EntityBeforeSave.IsNew)
            {
                return;
            }

            var vehicleLockout = e.EntityRefetched as VehicleLockoutDataObject;
            var reason = vehicleLockout.Reason;
            
            // if reason = 253, then RealImpact value should be 2, else RealImpact value should be 3
            if (reason != (LockoutReasonEnum)253)
            {
                vehicleLockout.RealImpact = (ImpactLockoutConfirmationEnum)3;
            }

            await _dataFacade.VehicleLockoutDataProvider.SaveAsync(vehicleLockout);

            return;

        }
    }
}
