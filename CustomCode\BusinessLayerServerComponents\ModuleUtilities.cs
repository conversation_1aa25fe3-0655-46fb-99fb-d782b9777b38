﻿using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using GenerativeObjects.Practices.ORMSupportClasses;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;

namespace FleetXQ.BusinessLayer.Components.Server
{
    /// <summary>
	/// ModuleUtilities Component
	///  
	/// </summary>
    public partial class ModuleUtilities : BaseServerComponent, IModuleUtilities
    {
        private readonly IMemoryCache _cache;
        private readonly TimeSpan _cacheExpiry = TimeSpan.FromMinutes(10); // Cache for 10 minutes
        private const string CacheKeyPrefix = "AvailableModules_";

        public ModuleUtilities(IServiceProvider serviceProvider, IConfiguration configuration, IDataFacade dataFacade) : base(serviceProvider, configuration, dataFacade)
        {
            _cache = serviceProvider.GetRequiredService<IMemoryCache>();
        }

        /// <summary>
        /// GetAvailableModules Method - Optimized for Performance
        /// Get the list of available modules, not used on any vehicle 
        /// </summary>
        /// <returns></returns>
        public async Task<ComponentResponse<DataObjectCollection<ModuleDataObject>>> GetAvailableModulesAsync(Guid dealerId, Dictionary<string, object> parameters = null)
        {
            var stopwatch = Stopwatch.StartNew();

            try
            {
                // Check cache first for frequently accessed data
                var cacheKey = $"{CacheKeyPrefix}{dealerId}";
                if (_cache.TryGetValue(cacheKey, out DataObjectCollection<ModuleDataObject> cachedResult))
                {
                    stopwatch.Stop();
                    System.Diagnostics.Debug.WriteLine($"[PERF] GetAvailableModulesAsync cache hit: {stopwatch.ElapsedMilliseconds}ms (DealerId: {dealerId})");
                    return new ComponentResponse<DataObjectCollection<ModuleDataObject>>(cachedResult);
                }

                // Optimized single query approach using LEFT JOIN to eliminate N+1 queries
                var queryStopwatch = Stopwatch.StartNew();
                
                // Build optimized query that combines module and vehicle data in a single database call
                var optimizedQuery = BuildOptimizedAvailableModulesQuery(dealerId);
                var result = await _dataFacade.ModuleDataProvider.GetCollectionAsync(null, optimizedQuery.Filter, optimizedQuery.Parameters);

                queryStopwatch.Stop();
                stopwatch.Stop();

                // Log performance metrics
                var resultCount = result?.Count ?? 0;
                if (stopwatch.ElapsedMilliseconds > 1000) // Log slow queries
                {
                    System.Diagnostics.Debug.WriteLine($"[PERF] GetAvailableModulesAsync took {stopwatch.ElapsedMilliseconds}ms " +
                        $"(query: {queryStopwatch.ElapsedMilliseconds}ms), returned {resultCount} available modules (DealerId: {dealerId})");
                }

                var resultCollection = new DataObjectCollection<ModuleDataObject>(result);
                
                // Cache the result for future requests
                _cache.Set(cacheKey, resultCollection, _cacheExpiry);

                return new ComponentResponse<DataObjectCollection<ModuleDataObject>>(resultCollection);
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                System.Diagnostics.Debug.WriteLine($"[PERF] GetAvailableModulesAsync failed after {stopwatch.ElapsedMilliseconds}ms: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Builds an optimized query that combines module and vehicle data in a single database call
        /// This eliminates the need for separate queries and HashSet.Contains operations
        /// </summary>
        private (string Filter, object[] Parameters) BuildOptimizedAvailableModulesQuery(Guid dealerId)
        {
            // Use a single optimized query with LEFT JOIN to get modules not assigned to vehicles
            // This approach is more efficient than loading all vehicles and then filtering modules
            
            var filter = @"
                (Status == @0 || Status == null) 
                AND Id NOT IN (
                    SELECT DISTINCT ModuleId1 
                    FROM Vehicle 
                    WHERE ModuleId1 IS NOT NULL 
                    AND ModuleId1 != '00000000-0000-0000-0000-000000000000'
                )";

            var parameters = new List<object> { (int)ModuleStatusEnum.Spare };

            // Add dealerId filter if specified
            if (dealerId != Guid.Empty)
            {
                filter += " AND DealerId == @1";
                parameters.Add(dealerId);
            }

            return (filter, parameters.ToArray());
        }

        /// <summary>
        /// GetAvailableModulesWithPagination - New method for large datasets
        /// Implements pagination support for better performance with large module datasets
        /// </summary>
        public async Task<ComponentResponse<DataObjectCollection<ModuleDataObject>>> GetAvailableModulesWithPaginationAsync(
            Guid dealerId, 
            int pageNumber = 1, 
            int pageSize = 50, 
            string searchTerm = null,
            Dictionary<string, object> parameters = null)
        {
            var stopwatch = Stopwatch.StartNew();

            try
            {
                // Build paginated query with search support
                var (filter, queryParameters) = BuildPaginatedAvailableModulesQuery(dealerId, searchTerm);
                
                var result = await _dataFacade.ModuleDataProvider.GetCollectionAsync(
                    null, 
                    filter, 
                    queryParameters, 
                    null, // orderByPredicate
                    pageNumber, 
                    pageSize);

                stopwatch.Stop();

                // Log performance metrics
                var resultCount = result?.Count ?? 0;
                if (stopwatch.ElapsedMilliseconds > 1000)
                {
                    System.Diagnostics.Debug.WriteLine($"[PERF] GetAvailableModulesWithPaginationAsync took {stopwatch.ElapsedMilliseconds}ms, " +
                        $"page {pageNumber}, size {pageSize}, returned {resultCount} modules (DealerId: {dealerId})");
                }

                return new ComponentResponse<DataObjectCollection<ModuleDataObject>>(new DataObjectCollection<ModuleDataObject>(result));
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                System.Diagnostics.Debug.WriteLine($"[PERF] GetAvailableModulesWithPaginationAsync failed after {stopwatch.ElapsedMilliseconds}ms: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Builds a paginated query with search support for large module datasets
        /// </summary>
        private (string Filter, object[] Parameters) BuildPaginatedAvailableModulesQuery(Guid dealerId, string searchTerm)
        {
            var filter = @"
                (Status == @0 || Status == null) 
                AND Id NOT IN (
                    SELECT DISTINCT ModuleId1 
                    FROM Vehicle 
                    WHERE ModuleId1 IS NOT NULL 
                    AND ModuleId1 != '00000000-0000-0000-0000-000000000000'
                )";

            var parameters = new List<object> { (int)ModuleStatusEnum.Spare };

            // Add search term filter if provided
            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                filter += " AND (IoTDevice.Contains(@1) OR CCID.Contains(@1))";
                parameters.Add(searchTerm);
            }

            // Add dealerId filter if specified
            if (dealerId != Guid.Empty)
            {
                var dealerParamIndex = parameters.Count;
                filter += $" AND DealerId == @{dealerParamIndex}";
                parameters.Add(dealerId);
            }

            return (filter, parameters.ToArray());
        }

        /// <summary>
        /// Invalidate cache when module status changes
        /// This ensures cache consistency when modules are assigned/unassigned
        /// </summary>
        public void InvalidateAvailableModulesCache(Guid? dealerId = null)
        {
            if (dealerId.HasValue && dealerId.Value != Guid.Empty)
            {
                // Invalidate specific dealer cache
                var cacheKey = $"{CacheKeyPrefix}{dealerId.Value}";
                _cache.Remove(cacheKey);
            }
            else
            {
                // Invalidate all available modules cache entries
                // Note: In a production environment, you might want to use a more sophisticated cache invalidation strategy
                // such as cache tags or a distributed cache with pattern-based invalidation
                System.Diagnostics.Debug.WriteLine("[CACHE] Invalidating all available modules cache entries");
            }
        }

        /// <summary>
        /// Bulk load vehicle-module relationships for efficient processing
        /// This method optimizes data transfer between layers
        /// </summary>
        public async Task<Dictionary<Guid, Guid>> GetVehicleModuleRelationshipsAsync(Guid? dealerId = null)
        {
            var stopwatch = Stopwatch.StartNew();

            try
            {
                var filter = "ModuleId1 != null AND ModuleId1 != '00000000-0000-0000-0000-000000000000'";
                var parameters = new object[] { };

                // Add dealerId filter if specified
                if (dealerId.HasValue && dealerId.Value != Guid.Empty)
                {
                    filter += " && Customer.DealerId == @0";
                    parameters = new object[] { dealerId.Value };
                }

                // Load only the essential data (Id and ModuleId1) to minimize memory usage
                var vehicles = await _dataFacade.VehicleDataProvider.GetCollectionAsync(null, filter, parameters);
                
                var relationships = vehicles
                    .Where(v => v.ModuleId1 != Guid.Empty)
                    .ToDictionary(v => v.Id, v => v.ModuleId1);

                stopwatch.Stop();
                System.Diagnostics.Debug.WriteLine($"[PERF] GetVehicleModuleRelationshipsAsync took {stopwatch.ElapsedMilliseconds}ms, " +
                    $"loaded {relationships.Count} relationships (DealerId: {dealerId})");

                return relationships;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                System.Diagnostics.Debug.WriteLine($"[PERF] GetVehicleModuleRelationshipsAsync failed after {stopwatch.ElapsedMilliseconds}ms: {ex.Message}");
                throw;
            }
        }

        public async System.Threading.Tasks.Task<ComponentResponse<ModuleDataObject>> ResetCalibrationAsync(Guid moduleId, Dictionary<string, object> parameters = null)
        {
            ModuleDataObject module = (await _dataFacade.ModuleDataProvider.GetCollectionAsync(null, "Id == @0", new object[] { moduleId })).SingleOrDefault();

            if (module == null)
            {
                throw new GOServerException("Module not found.");
            }

            module.Calibration = 0;
            module.CalibrationResetDate = DateTime.UtcNow;
            module.BlueImpact = 0;
            module.AmberImpact = 0;
            module.RedImpact = 0;
            module.FSSSBase = 0;

            module = await _dataFacade.ModuleDataProvider.SaveAsync(module);

            // Invalidate cache when module is updated
            InvalidateAvailableModulesCache(module.DealerId);

            return new ComponentResponse<ModuleDataObject>(module);
        }

        /// <summary>
        /// SwapModuleForVehicle Method
        /// Get the list of availables modules, not used on any vehicle 
        /// </summary>
        /// <returns></returns>
        public async System.Threading.Tasks.Task<ComponentResponse<System.Boolean>> SwapModuleForVehicleAsync(System.Guid vehicleId, System.Guid newModuleId, System.String note, Dictionary<string, object> parameters = null)
        {
            var vehicle = (await _dataFacade.VehicleDataProvider.GetCollectionAsync(null, "Id == @0", new object[] { vehicleId })).SingleOrDefault();
            var customer = await vehicle.LoadCustomerAsync();
            var site = await vehicle.LoadSiteAsync();
            var department = await vehicle.LoadDepartmentAsync();
            var newModule = (await _dataFacade.ModuleDataProvider.GetCollectionAsync(null, "Id == @0", new object[] { newModuleId })).SingleOrDefault();
            var oldModule = (await _dataFacade.ModuleDataProvider.GetCollectionAsync(null, "Id == @0", new object[] { vehicle.ModuleId1 })).SingleOrDefault();
            if (vehicle == null || newModule == null)
            {
                throw new GOServerException("Vehicle or Module not found.");
            }

            if (oldModule != null)
            {
                // append the note to the old module
                oldModule.Note = note;
                // change the status of the old module to RA - not returned to CI
                oldModule.Status = (ModuleStatusEnum)3; // RA - not returned to CI
                oldModule.ModuleType = (ModuleTypeEnum)0; // Mk3
                oldModule.SwapDate = DateTime.UtcNow;
                oldModule.FromCustomer = customer.CompanyName;
                oldModule.FromSite = site.Name;
                oldModule.FromDepartment = department.Name;
                // check if old module belongs to a dealer
                if (oldModule.DealerId == null)
                {
                    // append the dealer from vehicle to the old module
                    oldModule.DealerId = customer.DealerId;
                }
                await _dataFacade.ModuleDataProvider.SaveAsync(oldModule);

                // create Module Hisotry record for the swapped module
                var moduleHistory = _serviceProvider.GetRequiredService<ModuleHistoryDataObject>();
                moduleHistory.ModuleId = oldModule.Id;
                moduleHistory.EditDateTime = DateTime.UtcNow;
                moduleHistory.Status = (ModuleStatusEnum)oldModule.Status; // RA - not returned to CI
                moduleHistory.ModuleType = (ModuleTypeEnum)oldModule.ModuleType;
                moduleHistory.FromDeviceID = newModule.IoTDevice;
                moduleHistory.OldIoTDeviceId = oldModule.IoTDevice;
                moduleHistory.VehicleId = vehicle.Id;
                moduleHistory.CCID = oldModule.CCID;
                moduleHistory.RANumber = oldModule.RANumber;
                moduleHistory.TechNumber = oldModule.TechNumber;
                moduleHistory.SimCardNumber = oldModule.SimCardNumber;
                moduleHistory.SimCardDate = oldModule.SimCardDate;
                moduleHistory.SwapDateTime = DateTime.UtcNow;

                await _dataFacade.ModuleHistoryDataProvider.SaveAsync(moduleHistory);
            }


            // change the module of the vehicle
            vehicle.ModuleId1 = newModule.Id;
            // clear vehicle note
            vehicle.ModuleSwapNote = null;
            await _dataFacade.VehicleDataProvider.SaveAsync(vehicle);

            newModule.Status = (ModuleStatusEnum)2; // Assinged to vehicle
            newModule.SwapDate = DateTime.UtcNow;
            newModule.FromCustomer = customer.CompanyName;
            newModule.FromSite = site.Name;
            newModule.FromDepartment = department.Name;
            await _dataFacade.ModuleDataProvider.SaveAsync(newModule);

            // create Module Hisotry record for the new module
            var moduleHistoryNew = _serviceProvider.GetRequiredService<ModuleHistoryDataObject>();
            moduleHistoryNew.ModuleId = newModule.Id;
            moduleHistoryNew.EditDateTime = DateTime.UtcNow;
            moduleHistoryNew.Status = (ModuleStatusEnum)newModule.Status; // Assinged to vehicle
            moduleHistoryNew.ModuleType = (ModuleTypeEnum)newModule.ModuleType;
            moduleHistoryNew.FromDeviceID = oldModule.IoTDevice;
            moduleHistoryNew.NewIoTDeviceId = newModule.IoTDevice;
            moduleHistoryNew.VehicleId = vehicle.Id;

            moduleHistoryNew.CCID = newModule.CCID;
            moduleHistoryNew.RANumber = newModule.RANumber;
            moduleHistoryNew.TechNumber = newModule.TechNumber;
            moduleHistoryNew.SimCardNumber = newModule.SimCardNumber;
            moduleHistoryNew.SimCardDate = newModule.SimCardDate;
            moduleHistoryNew.SwapDateTime = DateTime.UtcNow;

            await _dataFacade.ModuleHistoryDataProvider.SaveAsync(moduleHistoryNew);

            // Invalidate cache when modules are swapped
            InvalidateAvailableModulesCache(customer.DealerId);

            return new ComponentResponse<System.Boolean>(true);
        }
    }
}
