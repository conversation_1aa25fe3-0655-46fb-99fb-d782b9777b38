﻿-- ----------------------------------------------------------------------------------------------------------------
-- Schema 'dbo'
-- ----------------------------------------------------------------------------------------------------------------
-- -------[ Tables ]-----------------------------------------------------------------------------------------------
CREATE TABLE [dbo].[AccessGroup] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Name] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[AccessRules] [nvarchar] (2500) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Description] [nvarchar] (500) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[CanDeleteCustomerEmailList] [bit] NOT NULL, 
	[CanEditUserReportSubscription] [bit] NOT NULL, 
	[CanViewPreopChecklistReport] [bit] NOT NULL, 
	[CanExportCurrentStatusReport] [bit] NOT NULL, 
	[CanCreateUser] [bit] NOT NULL, 
	[CanEditVehicleOtherSettingFullLockout] [bit] NOT NULL, 
	[CanViewUserSupervisorAccess] [bit] NOT NULL, 
	[CanViewProficiencyReport] [bit] NOT NULL, 
	[CanDeleteCustomerAccessGroups] [bit] NOT NULL, 
	[CanViewAccessGroups] [bit] NOT NULL, 
	[CanExportProficiencyReport] [bit] NOT NULL, 
	[CanEditCustomerFirmware] [bit] NOT NULL, 
	[CanDeleteUserAlert] [bit] NOT NULL, 
	[CanDeleteUserReportSubscription] [bit] NOT NULL, 
	[CanCreateCustomerSite] [bit] NOT NULL, 
	[CanEditVehicleOtherSettingVorStatus] [bit] NOT NULL, 
	[CanEditCustomerAccessGroups] [bit] NOT NULL, 
	[CanExportPreopChecklistReport] [bit] NOT NULL, 
	[CanViewMachineUnlockReport] [bit] NOT NULL, 
	[CanViewVehicleSynchronization] [bit] NOT NULL, 
	[CanCreateVehicleChecklist] [bit] NOT NULL, 
	[CanDeleteCustomerEmailGroup] [bit] NOT NULL, 
	[CanDeleteUser] [bit] NOT NULL, 
	[CanEditVehicle] [bit] NOT NULL, 
	[CanViewVehicleImpactSetting] [bit] NOT NULL, 
	[CanEditRAModuleSwap] [bit] NOT NULL, 
	[HasUsersAccess] [bit] NOT NULL, 
	[CanViewUsers] [bit] NOT NULL, 
	[CanEditVehicleChecklist] [bit] NOT NULL, 
	[CanViewGeneralProductivityReport] [bit] NOT NULL, 
	[CanEditUserWebsiteAccess] [bit] NOT NULL, 
	[CanViewDashboard] [bit] NOT NULL, 
	[CanCreateCustomerEmailGroup] [bit] NOT NULL, 
	[CanCreateUserLicense] [bit] NOT NULL, 
	[CanViewVehicleService] [bit] NOT NULL, 
	[CanViewServiceCheckReport] [bit] NOT NULL, 
	[CanEditVehicleImpactSetting] [bit] NOT NULL, 
	[CanEditCustomerSite] [bit] NOT NULL, 
	[CanCreateCustomerAccessGroups] [bit] NOT NULL, 
	[CanViewVehicleChecklistSetting] [bit] NOT NULL, 
	[CanViewVehicleAccess] [bit] NOT NULL, 
	[CanViewVehicle] [bit] NOT NULL, 
	[CanViewVehicleOtherSettingVorStatus] [bit] NOT NULL, 
	[CanCreateUserCard] [bit] NOT NULL, 
	[CanViewCustomer] [bit] NOT NULL, 
	[CanViewImpactReport] [bit] NOT NULL, 
	[CanViewCustomerSite] [bit] NOT NULL, 
	[CanExportServiceCheckReport] [bit] NOT NULL, 
	[CanCreateUserWebsiteAccess] [bit] NOT NULL, 
	[CanViewCurrentStatusReport] [bit] NOT NULL, 
	[CanCreateVehicle] [bit] NOT NULL, 
	[CanViewUserReportSubscription] [bit] NOT NULL, 
	[CanEditUserAlert] [bit] NOT NULL, 
	[CanCreateUserAlert] [bit] NOT NULL, 
	[HasReportsAccess] [bit] NOT NULL, 
	[CanViewVehicleOtherSettingFullLockout] [bit] NOT NULL, 
	[CanViewUserWebsiteAccess] [bit] NOT NULL, 
	[CanViewCustomerDepartment] [bit] NOT NULL, 
	[CanDeleteVehicleService] [bit] NOT NULL, 
	[CanCreateCustomerEmailList] [bit] NOT NULL, 
	[CanExportGeneralProductivityReport] [bit] NOT NULL, 
	[CanEditUser] [bit] NOT NULL, 
	[CanViewCustomerEmailGroup] [bit] NOT NULL, 
	[CanCreateVehicleService] [bit] NOT NULL, 
	[CanViewUserLicense] [bit] NOT NULL, 
	[CanEditCustomer] [bit] NOT NULL, 
	[CanEditCustomerEmailGroup] [bit] NOT NULL, 
	[CanViewUserCard] [bit] NOT NULL, 
	[CanEditVehicleAccess] [bit] NOT NULL, 
	[CanCreateCustomerDepartment] [bit] NOT NULL, 
	[CanViewVehicleChecklist] [bit] NOT NULL, 
	[CanEditCustomerDepartment] [bit] NOT NULL, 
	[CanExportVehicle] [bit] NOT NULL, 
	[HasVehiclesAccess] [bit] NOT NULL, 
	[CanExportMachineUnlockReport] [bit] NOT NULL, 
	[CanEditUserSupervisorAccess] [bit] NOT NULL, 
	[CanEditUserLicense] [bit] NOT NULL, 
	[CanEditUserCard] [bit] NOT NULL, 
	[CanCreateUserReportSubscription] [bit] NOT NULL, 
	[HasCustomersAccess] [bit] NOT NULL, 
	[CanCreateVehicleChecklistSetting] [bit] NOT NULL, 
	[CanViewUserAlert] [bit] NOT NULL, 
	[CanViewCustomerFirmware] [bit] NOT NULL, 
	[CanExportUsers] [bit] NOT NULL, 
	[CanEditVehicleChecklistSetting] [bit] NOT NULL, 
	[CanViewCustomerModel] [bit] NOT NULL, 
	[CanExportImpactReport] [bit] NOT NULL, 
	[CanDeleteVehicleChecklist] [bit] NOT NULL, 
	[CanEditVehicleService] [bit] NOT NULL, 
	[CustomerId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[AccessGroupTemplate] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Description] [nvarchar] (500) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Name] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[CanDeleteUserAlert] [bit] NOT NULL, 
	[CanViewCustomerSite] [bit] NOT NULL, 
	[CanEditUserReportSubscription] [bit] NOT NULL, 
	[CanEditUserWebsiteAccess] [bit] NOT NULL, 
	[CanDeleteCustomerEmailList] [bit] NOT NULL, 
	[CanEditUserSupervisorAccess] [bit] NOT NULL, 
	[CanCreateVehicleChecklistSetting] [bit] NOT NULL, 
	[CanViewVehicleOtherSettingVorStatus] [bit] NOT NULL, 
	[CanDeleteCustomerEmailGroup] [bit] NOT NULL, 
	[CanEditVehicleAccess] [bit] NOT NULL, 
	[CanExportImpactReport] [bit] NOT NULL, 
	[IsDefault] [bit] NOT NULL, 
	[CanExportCurrentStatusReport] [bit] NOT NULL, 
	[CanViewVehicleChecklistSetting] [bit] NOT NULL, 
	[HasReportsAccess] [bit] NOT NULL, 
	[CanCreateUserLicense] [bit] NOT NULL, 
	[CanViewVehicleImpactSetting] [bit] NOT NULL, 
	[CanCreateCustomerEmailGroup] [bit] NOT NULL, 
	[CanEditVehicleService] [bit] NOT NULL, 
	[CanViewVehicleAccess] [bit] NOT NULL, 
	[CanCreateUser] [bit] NOT NULL, 
	[CanCreateUserAlert] [bit] NOT NULL, 
	[CanCreateUserReportSubscription] [bit] NOT NULL, 
	[CanViewCurrentStatusReport] [bit] NOT NULL, 
	[CanViewMachineUnlockReport] [bit] NOT NULL, 
	[CanViewAccessGroups] [bit] NOT NULL, 
	[CanViewVehicleService] [bit] NOT NULL, 
	[CanCreateCustomerAccessGroups] [bit] NOT NULL, 
	[CanViewUserAlert] [bit] NOT NULL, 
	[CanViewUserLicense] [bit] NOT NULL, 
	[CanExportUsers] [bit] NOT NULL, 
	[CanEditCustomerEmailGroup] [bit] NOT NULL, 
	[CanEditUserAlert] [bit] NOT NULL, 
	[CanViewCustomerDepartment] [bit] NOT NULL, 
	[CanEditVehicleImpactSetting] [bit] NOT NULL, 
	[CanExportServiceCheckReport] [bit] NOT NULL, 
	[CanViewUserCard] [bit] NOT NULL, 
	[CanEditUser] [bit] NOT NULL, 
	[CanViewUserReportSubscription] [bit] NOT NULL, 
	[CanViewCustomerEmailGroup] [bit] NOT NULL, 
	[CanViewProficiencyReport] [bit] NOT NULL, 
	[CanCreateCustomerSite] [bit] NOT NULL, 
	[CanEditVehicle] [bit] NOT NULL, 
	[CanViewGeneralProductivityReport] [bit] NOT NULL, 
	[CanViewCustomer] [bit] NOT NULL, 
	[CanViewVehicleSynchronization] [bit] NOT NULL, 
	[CanDeleteVehicleChecklist] [bit] NOT NULL, 
	[CanCreateCustomerDepartment] [bit] NOT NULL, 
	[CanViewCustomerFirmware] [bit] NOT NULL, 
	[CanEditCustomerFirmware] [bit] NOT NULL, 
	[CanEditVehicleChecklistSetting] [bit] NOT NULL, 
	[CanEditVehicleOtherSettingFullLockout] [bit] NOT NULL, 
	[CanExportGeneralProductivityReport] [bit] NOT NULL, 
	[CanEditCustomerDepartment] [bit] NOT NULL, 
	[CanViewCustomerModel] [bit] NOT NULL, 
	[CanViewVehicleOtherSettingFullLockout] [bit] NOT NULL, 
	[CanEditVehicleChecklist] [bit] NOT NULL, 
	[CanCreateUserCard] [bit] NOT NULL, 
	[CanEditVehicleOtherSettingVorStatus] [bit] NOT NULL, 
	[CanExportProficiencyReport] [bit] NOT NULL, 
	[CanExportVehicle] [bit] NOT NULL, 
	[CanEditCustomerAccessGroups] [bit] NOT NULL, 
	[CanViewPreopChecklistReport] [bit] NOT NULL, 
	[CanViewDashboard] [bit] NOT NULL, 
	[CanCreateVehicleChecklist] [bit] NOT NULL, 
	[CanViewVehicle] [bit] NOT NULL, 
	[CanExportMachineUnlockReport] [bit] NOT NULL, 
	[CanViewServiceCheckReport] [bit] NOT NULL, 
	[CanViewImpactReport] [bit] NOT NULL, 
	[HasCustomersAccess] [bit] NOT NULL, 
	[CanEditUserCard] [bit] NOT NULL, 
	[CanCreateVehicleService] [bit] NOT NULL, 
	[CanViewUsers] [bit] NOT NULL, 
	[CanViewUserSupervisorAccess] [bit] NOT NULL, 
	[HasVehiclesAccess] [bit] NOT NULL, 
	[CanViewUserWebsiteAccess] [bit] NOT NULL, 
	[CanExportPreopChecklistReport] [bit] NOT NULL, 
	[HasUsersAccess] [bit] NOT NULL, 
	[CanCreateCustomerEmailList] [bit] NOT NULL, 
	[CanEditUserLicense] [bit] NOT NULL, 
	[CanEditCustomerSite] [bit] NOT NULL, 
	[CanViewVehicleChecklist] [bit] NOT NULL, 
	[CanEditRAModuleSwap] [bit] NOT NULL, 
	[CanCreateUserWebsiteAccess] [bit] NOT NULL, 
	[CanCreateVehicle] [bit] NOT NULL, 
	[CanDeleteUserReportSubscription] [bit] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[AccessGroupToSite] 
(
	[AccessGroupId] [uniqueidentifier] NOT NULL, 
	[SiteId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[Alert] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Paragraph1] [nvarchar] (500) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[Description] [nvarchar] (500) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Subject] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[Paragraph2] [nvarchar] (500) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Name] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[Signature] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[AlertHistory] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Description] [nvarchar] (1000) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[CreatedDateTime] [datetime] NOT NULL, 
	[IsResolved] [bit] NOT NULL, 
	[IsAcknowledged] [bit] NOT NULL, 
	[VehicleId] [uniqueidentifier] NOT NULL, 
	[AlertId] [uniqueidentifier] NOT NULL, 
	[DriverId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[AlertSubscription] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[IsActive] [bit] NOT NULL, 
	[PersonId] [uniqueidentifier] NOT NULL, 
	[GOUserId] [uniqueidentifier] NULL, 
	[AlertId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[BroadcastMessage] 
(
	[Timeout] [int] NOT NULL, 
	[Id] [uniqueidentifier] NOT NULL, 
	[Message] [nvarchar] (300) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[Priority] [int] NOT NULL, 
	[ResponseOptions] [int] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[BroadcastMessageHistory] 
(
	[MessageId] [int] IDENTITY (1,1) NOT NULL, 
	[Message] [nvarchar] (300) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[Response] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[SentTime] [datetime] NOT NULL, 
	[ResponseTime] [datetime] NULL, 
	[DisplayTime] [datetime] NULL, 
	[Type] [int] NOT NULL, 
	[VehicleId] [uniqueidentifier] NOT NULL, 
	[DriverId] [uniqueidentifier] NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[Canrule] 
(
	[VehicleSerial] [smallint] NULL, 
	[Id] [uniqueidentifier] NOT NULL, 
	[CRC] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Name] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[Description] [nvarchar] (500) COLLATE SQL_Latin1_General_CP1_CI_AS NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[CanruleDetails] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Canrules] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[CanruleId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[Card] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[SiteId] [uniqueidentifier] NULL, 
	[Weigand] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[CardNumber] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[FacilityCode] [nvarchar] (3) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Active] [bit] NOT NULL, 
	[Type] [int] NOT NULL, 
	[KeypadReader] [int] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[CardToCardAccess] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[CardDetailsId] [uniqueidentifier] NOT NULL, 
	[PermissionId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[CategoryTemplate] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Description] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[Name] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[Picture] [nvarchar](1000) NULL, 
	[PictureFileSize] [int] NULL, 
	[PictureInternalName] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[ChecklistDetail] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Answer] [bit] NOT NULL, 
	[Failed] [bit] NULL, 
	[ChecklistResultId] [uniqueidentifier] NOT NULL, 
	[PreOperationalChecklistId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[ChecklistResult] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Comment] [nvarchar] (500) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[EndTime] [datetime] NULL, 
	[StartTime] [datetime] NOT NULL, 
	[SessionId1] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[ChecklistSettings] 
(
	[QuestionTimeout] [smallint] NULL, 
	[Id] [uniqueidentifier] NOT NULL, 
	[ShowComment] [bit] NOT NULL, 
	[Randomisation] [bit] NOT NULL, 
	[Type] [int] NOT NULL, 
	[TimeslotTwo] [time] NULL, 
	[TimeslotThree] [time] NULL, 
	[TimeslotFour] [time] NULL, 
	[TimeslotOne] [time] NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[ContactPersonInformation] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Address] [nvarchar] (500) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[PhoneNo] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[LastName] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[FirstName] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[Email] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[Country] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Name] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[Code] [nvarchar] (4) COLLATE SQL_Latin1_General_CP1_CI_AS NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[Customer] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[ConnectionString] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Addess] [nvarchar] (250) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[ContactNumber] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[ContractNumber] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Prefix] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Description] [nvarchar] (500) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Email] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[PreferredLocaleString] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[CompanyName] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[ContractDate] [datetime] NULL, 
	[DeletedAtUtc] [datetime] NULL, 
	[Active] [bit] NOT NULL, 
	[DealerCustomer] [bit] NULL, 
	[PreferredLocale] [int] NULL, 
	[CustomerLogo] [nvarchar](1000) NULL, 
	[CustomerLogoFileSize] [int] NULL, 
	[CustomerLogoInternalName] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[CustomerFeatureSubscriptionId] [uniqueidentifier] NULL, 
	[DealerId] [uniqueidentifier] NOT NULL, 
	[ContactPersonInformationId] [uniqueidentifier] NULL, 
	[CountryId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[CustomerFeatureSubscription] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Description] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Name] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[IsEnabled] [bit] NOT NULL, 
	[IsTagged] [bit] NOT NULL, 
	[HasAdditionalHardwaresAccess] [bit] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[CustomerModel] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Polarity] [int] NOT NULL, 
	[CustomerId] [uniqueidentifier] NOT NULL, 
	[ModelId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[CustomerPreOperationalChecklistTemplate] 
(
	[Order] [smallint] NOT NULL, 
	[Id] [uniqueidentifier] NOT NULL, 
	[Question] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[Critical] [bit] NOT NULL, 
	[Active] [bit] NOT NULL, 
	[ExpectedAnswer] [bit] NOT NULL, 
	[AnswerType] [int] NOT NULL, 
	[CustomerId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[CustomerSSODetail] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[ClientID] [nvarchar] (500) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[TenantID] [nvarchar] (500) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[RedirectURL] [int] NOT NULL, 
	[CustomerId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[Dealer] 
(
	[ContractNumber] [smallint] NULL, 
	[Id] [uniqueidentifier] NOT NULL, 
	[ThemeColor] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Description] [nvarchar] (500) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[SubDomain] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[PortalURL] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Name] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[IsAPIEnabled] [bit] NOT NULL, 
	[Active] [bit] NOT NULL, 
	[LoginLogo] [nvarchar](1000) NULL, 
	[NavbarLogo] [nvarchar](1000) NULL, 
	[NavbarLogoFileSize] [int] NULL, 
	[LoginLogoFileSize] [int] NULL, 
	[NavbarLogoInternalName] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[LoginLogoInternalName] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[DealerConfigurationId] [uniqueidentifier] NULL, 
	[RegionId] [uniqueidentifier] NOT NULL, 
	[DealerFeatureSubscriptionId] [uniqueidentifier] NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[DealerConfiguration] 
(
	[Id] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[DealerDriver] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[DriverType] [int] NOT NULL, 
	[CardId] [uniqueidentifier] NULL, 
	[GOUserId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[DealerFeatureSubscription] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Name] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[Description] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[HasAdditionalHardwaresAccess] [bit] NOT NULL, 
	[IsEnab] [bit] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[Department] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Name] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[DeletedAtUtc] [datetime] NULL, 
	[Active] [bit] NOT NULL, 
	[CustomerId] [uniqueidentifier] NOT NULL, 
	[SiteId] [uniqueidentifier] NOT NULL, 
	[DepartmentHourSettingsId] [uniqueidentifier] NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[DepartmentChecklist] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[IsFrenchEnabled] [bit] NOT NULL, 
	[IsSpanishEnabled] [bit] NOT NULL, 
	[IsThaiEnabled] [bit] NOT NULL, 
	[IsFilipinoEnabled] [bit] NOT NULL, 
	[IsVietnameseEnabled] [bit] NOT NULL, 
	[IsTraditionalChineseEnabled] [bit] NOT NULL, 
	[ModelId] [uniqueidentifier] NULL, 
	[DepartmentId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[DepartmentHourSettings] 
(
	[SaturdayAvailableMinutes] [int] NULL, 
	[TuesdayAvailableHours] [int] NULL, 
	[ThursdayAvailableHours] [int] NULL, 
	[WednesdayAvailableHours] [int] NULL, 
	[MondayAvailableHours] [int] NULL, 
	[SundayAvailableMinutes] [int] NULL, 
	[FridayAvailableHours] [int] NULL, 
	[SaturdayAvailableHours] [int] NULL, 
	[FridayAvailableMinutes] [int] NULL, 
	[TuesdayAvailableMinutes] [int] NULL, 
	[MondayAvailableMinutes] [int] NULL, 
	[WednesdayAvailableMinutes] [int] NULL, 
	[SundayAvailableHours] [int] NULL, 
	[ThursdayAvailableMinutes] [int] NULL, 
	[Id] [uniqueidentifier] NOT NULL, 
	[MondayOpenMinutes] [int] NULL, 
	[SundayBreaktimeMinutes] [int] NULL, 
	[SaturdayOpenHours] [int] NULL, 
	[FridayBreaktimeHours] [int] NULL, 
	[WednesdayOpenHours] [int] NULL, 
	[TuesdayBreaktimeHours] [int] NULL, 
	[TuesdayOpenHours] [int] NULL, 
	[SaturdayBreaktimeHours] [int] NULL, 
	[WednesdayBreaktimeHours] [int] NULL, 
	[MondayBreaktimeMinutes] [int] NULL, 
	[MondayBreaktimeHours] [int] NULL, 
	[ThursdayBreaktimeMinutes] [int] NULL, 
	[FridayOpenMinutes] [int] NULL, 
	[SaturdayBreaktimeMinutes] [int] NULL, 
	[ThursdayOpenMinutes] [int] NULL, 
	[ThursdayOpenHours] [int] NULL, 
	[WednesdayBreaktimeMinutes] [int] NULL, 
	[SaturdayOpenMinutes] [int] NULL, 
	[TuesdayBreaktimeMinutes] [int] NULL, 
	[TuesdayOpenMinutes] [int] NULL, 
	[FridayBreaktimeMinutes] [int] NULL, 
	[SundayOpenMinutes] [int] NULL, 
	[ThursdayBreaktimeHours] [int] NULL, 
	[WednesdayOpenMinutes] [int] NULL, 
	[SundayBreaktimeHours] [int] NULL, 
	[FridayOpenHours] [int] NULL, 
	[SundayOpenHours] [int] NULL, 
	[MondayOpenHours] [int] NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[DepartmentVehicleMasterCardAccess] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[CardId] [uniqueidentifier] NOT NULL, 
	[DepartmentId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[DepartmentVehicleNormalCardAccess] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[CardId] [uniqueidentifier] NOT NULL, 
	[DepartmentId] [uniqueidentifier] NOT NULL, 
	[PermissionId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[Driver] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[LastSessionId] [nvarchar] (200) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[LastSessionDate] [datetime] NULL, 
	[LastSessionDateTzAdjusted] [datetime] NULL, 
	[VehicleAccess] [bit] NULL, 
	[Active] [bit] NOT NULL, 
	[LicenseMode] [int] NOT NULL, 
	[CardDetailsId] [uniqueidentifier] NULL, 
	[LicenceDetailId] [uniqueidentifier] NULL, 
	[CustomerId] [uniqueidentifier] NULL, 
	[DepartmentId] [uniqueidentifier] NULL, 
	[SiteId] [uniqueidentifier] NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[EmailGroups] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Name] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[Description] [nvarchar] (500) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[CustomerId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[EmailGroupsToPerson] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[CustomerToPersonViewId] [uniqueidentifier] NULL, 
	[EmailGroupsId] [uniqueidentifier] NOT NULL, 
	[PersonId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[FeatureSubscriptionTemplate] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Description] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Name] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[Is] [bit] NOT NULL, 
	[HasAdditionalHardwaresAccess] [bit] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[Firmware] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Url] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[Version] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[FloorPlan] 
(
	[Length] [decimal] (2, 2) NULL, 
	[XPosition] [decimal] (2, 2) NULL, 
	[Rotation] [decimal] (2, 2) NULL, 
	[Width] [decimal] (2, 2) NULL, 
	[YPosition] [decimal] (2, 2) NULL, 
	[Scale] [decimal] (2, 2) NULL, 
	[Opacity] [decimal] (2, 2) NULL, 
	[Id] [uniqueidentifier] NOT NULL, 
	[FloorLevel] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[Name] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[File] [nvarchar](1000) NULL, 
	[FileFileSize] [int] NULL, 
	[FileInternalName] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[SiteId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[FloorZones] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[ZoneColor] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[Name] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[FloorPlanId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[GOTask] 
(
	[Progress] [smallint] NOT NULL, 
	[Id] [uniqueidentifier] NOT NULL, 
	[Info] [nvarchar] (1024) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Error] [nvarchar] (1024) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Name] [nvarchar] (256) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[Latest] [datetime] NULL, 
	[End] [datetime] NULL, 
	[Start] [datetime] NOT NULL, 
	[TaskStatus] [int] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[GOUserDepartment] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[GOUserId] [uniqueidentifier] NOT NULL, 
	[DepartmentId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[GoUserToCustomer] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[GOUserId] [uniqueidentifier] NOT NULL, 
	[CustomerId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[GPSHistory] 
(
	[Longitude] [smallint] NULL, 
	[Latitude] [smallint] NULL, 
	[Id] [uniqueidentifier] NOT NULL, 
	[GPSDateTime] [datetime] NOT NULL, 
	[SessionId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[Impact] 
(
	[Threshold] [float] NOT NULL, 
	[Longitude] [float] NULL, 
	[ShockValue] [float] NOT NULL, 
	[Latitude] [float] NULL, 
	[Id] [uniqueidentifier] NOT NULL, 
	[ImpactDateTime] [datetime] NOT NULL, 
	[SessionId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[Inspection] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Notes] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[NextInspectionDate] [datetime] NULL, 
	[InspectionDate] [datetime] NULL, 
	[UploadDocument] [nvarchar](1000) NULL, 
	[UploadDocumentFileSize] [int] NULL, 
	[UploadDocumentInternalName] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[IOFIELD] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Name] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[Measurement] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Description] [nvarchar] (500) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[IOType] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[CANBUS] [bit] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[IoTDeviceMessageCache] 
(
	[VehicleId] [uniqueidentifier] NOT NULL, 
	[Id] [uniqueidentifier] NOT NULL, 
	[Message] [nvarchar] (500) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[EventType] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[LastUpdate] [datetime] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[LicenceDetail] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[LicenseNumber] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[ExpiryDate] [datetime] NOT NULL, 
	[Document] [nvarchar](1000) NULL, 
	[DocumentFileSize] [int] NULL, 
	[DocumentInternalName] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[LicenseByModel] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[ExpiryDate] [datetime] NOT NULL, 
	[ModelImage] [nvarchar](1000) NULL, 
	[ModelImageFileSize] [int] NULL, 
	[ModelImageInternalName] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[DriverId] [uniqueidentifier] NOT NULL, 
	[ModelId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[MessageHistory] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[DeliveredTimestamp] [datetime] NULL, 
	[SentTimestamp] [datetime] NOT NULL, 
	[MessageStatus] [int] NOT NULL, 
	[SyncType] [int] NOT NULL, 
	[GOUserId] [uniqueidentifier] NOT NULL, 
	[VehicleId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[Model] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Description] [nvarchar] (250) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Name] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[Type] [int] NULL, 
	[ModelPicture] [nvarchar](1000) NULL, 
	[ModelPictureFileSize] [int] NULL, 
	[ModelPictureInternalName] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[DealerId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[ModelVehicleMasterCardAccess] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[ModelId] [uniqueidentifier] NOT NULL, 
	[CardId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[ModelVehicleNormalCardAccess] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[PermissionId] [uniqueidentifier] NOT NULL, 
	[CardId] [uniqueidentifier] NOT NULL, 
	[DepartmentId] [uniqueidentifier] NOT NULL, 
	[ModelId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[Module] 
(
	[AmberImpact] [float] NOT NULL, 
	[FSSXMulti] [float] NOT NULL, 
	[SyncVersion] [int] NULL, 
	[FSSSBase] [float] NOT NULL, 
	[Calibration] [int] NULL, 
	[BlueImpact] [float] NULL, 
	[RedImpact] [float] NOT NULL, 
	[Id] [uniqueidentifier] NOT NULL, 
	[TechNumber] [nvarchar] (200) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[DeviceTwin] [nvarchar] (MAX) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[RANumber] [nvarchar] (200) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[FromDepartment] [nvarchar] (200) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[FromSite] [nvarchar] (200) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[IoTDevice] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[OldDeviceID] [nvarchar] (200) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Note] [nvarchar] (200) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[FromSerial] [nvarchar] (200) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[SimCardNumber] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[FromCustomer] [nvarchar] (200) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[CCID] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[LastActivityTime] [datetime] NULL, 
	[CalibrationDate] [datetime] NULL, 
	[LastUpdateTime] [datetime] NULL, 
	[CalibrationResetDate] [datetime] NULL, 
	[CCIDUpdateDateTime] [datetime] NULL, 
	[SimCardDate] [datetime] NULL, 
	[SwapDate] [datetime] NULL, 
	[IsAllocatedToVehicle] [bit] NULL, 
	[Status] [int] NULL, 
	[ModuleType] [int] NULL, 
	[DealerId] [uniqueidentifier] NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[ModuleHistory] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Note] [nvarchar] (200) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[RANumber] [nvarchar] (200) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[TechNumber] [nvarchar] (200) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[CCID] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[FromDeviceID] [nvarchar] (200) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[NewIoTDeviceId] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[SimCardNumber] [nvarchar] (200) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[OldIoTDeviceId] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[SwapDateTime] [datetime] NULL, 
	[SimCardDate] [datetime] NULL, 
	[EditDateTime] [datetime] NOT NULL, 
	[Status] [int] NULL, 
	[ModuleType] [int] NULL, 
	[ModuleId] [uniqueidentifier] NOT NULL, 
	[VehicleId] [uniqueidentifier] NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[NetworkSettings] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[WifiPassword] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[SSID] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[VehicleId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[OnDemandSession] 
(
	[HoursFrom] [int] NOT NULL, 
	[Usage] [bigint] NOT NULL, 
	[Id] [uniqueidentifier] NOT NULL, 
	[EndTime] [datetime] NULL, 
	[StartTime] [datetime] NULL, 
	[SendFlag] [int] NOT NULL, 
	[DriverId] [uniqueidentifier] NOT NULL, 
	[VehicleId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[OnDemandSettings] 
(
	[HourlyRate] [real] NULL, 
	[MaxHourlyRate] [real] NULL, 
	[SessionTime] [int] NULL, 
	[Id] [uniqueidentifier] NOT NULL, 
	[Authorised] [bit] NOT NULL, 
	[OnDemandCommand] [int] NULL, 
	[VehicleId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[PedestrianDetectionHistory] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[AlertDuration] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Date] [datetime] NULL, 
	[TRACK] [bit] NULL, 
	[SEAT] [bit] NULL, 
	[HYDR] [bit] NULL, 
	[VehicleId] [uniqueidentifier] NOT NULL, 
	[DriverId] [uniqueidentifier] NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[Permission] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Description] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[LevelName] [int] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[Person] 
(
	[YearOfStayCount] [smallint] NULL, 
	[MasterMenuOptions] [smallint] NULL, 
	[Id] [uniqueidentifier] NOT NULL, 
	[Notes] [nvarchar] (155) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Phone] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[LastName] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[Email] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[CustomerName] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[FirstName] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[LastMedicalDate] [datetime] NULL, 
	[EntryDate] [datetime] NULL, 
	[ExitDate] [datetime] NULL, 
	[DeletedAtUtc] [datetime] NULL, 
	[HasLicense] [bit] NOT NULL, 
	[OnDemand] [bit] NOT NULL, 
	[VORActivateDeactivate] [bit] NOT NULL, 
	[NormalDriverAccess] [bit] NOT NULL, 
	[MaintenanceMode] [bit] NOT NULL, 
	[CanUnlockVehicle] [bit] NOT NULL, 
	[Supervisor] [bit] NULL, 
	[IsActiveDriver] [bit] NULL, 
	[WebSiteAccess] [bit] NULL, 
	[LicenseActive] [bit] NOT NULL, 
	[IsDriver] [bit] NULL, 
	[VehicleAccess] [bit] NOT NULL, 
	[AccessLevel] [nvarchar](2000) NULL, 
	[Photo] [nvarchar](1000) NULL, 
	[PhotoFileSize] [int] NULL, 
	[PhotoInternalName] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[WebsiteUserId] [uniqueidentifier] NULL, 
	[CustomerId] [uniqueidentifier] NOT NULL, 
	[PersonChecklistLanguageSettingsId] [uniqueidentifier] NULL, 
	[DriverId] [uniqueidentifier] NULL, 
	[GOUserId] [uniqueidentifier] NULL, 
	[AccessGroupId] [uniqueidentifier] NULL, 
	[SiteId] [uniqueidentifier] NOT NULL, 
	[DepartmentId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[PersonAllocation] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[PersonId] [uniqueidentifier] NOT NULL, 
	[SiteId] [uniqueidentifier] NOT NULL, 
	[DepartmentId] [uniqueidentifier] NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[PersonChecklistLanguageSettings] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Language] [int] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[PerVehicleMasterCardAccess] 
(
	[SlotNumber] [smallint] NOT NULL, 
	[Id] [uniqueidentifier] NOT NULL, 
	[SiteVehicleNormalCardAccessId] [uniqueidentifier] NULL, 
	[DepartmentVehicleMasterCardAccessId] [uniqueidentifier] NULL, 
	[ModelVehicleMasterCardAccessId] [uniqueidentifier] NULL, 
	[CardId] [uniqueidentifier] NOT NULL, 
	[VehicleId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[PerVehicleNormalCardAccess] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[PermissionId] [uniqueidentifier] NOT NULL, 
	[VehicleId] [uniqueidentifier] NOT NULL, 
	[CardId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[PreOperationalChecklist] 
(
	[Order] [smallint] NOT NULL, 
	[Id] [uniqueidentifier] NOT NULL, 
	[FrenchQuestion] [nvarchar] (200) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[TraditionalChineseQuestion] [nvarchar] (200) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Question] [nvarchar] (200) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[ThaiQuestion] [nvarchar] (200) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[VietnameseQuestion] [nvarchar] (200) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[SpanishQuestion] [nvarchar] (200) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[FilipinoQuestion] [nvarchar] (200) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Active] [bit] NOT NULL, 
	[ExcludeRandom] [bit] NOT NULL, 
	[ExpectedAnswer] [bit] NOT NULL, 
	[Critical] [bit] NOT NULL, 
	[AnswerType] [int] NOT NULL, 
	[SiteChecklistId] [uniqueidentifier] NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[PSTATDetails] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Usage] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[UTCTime] [datetime] NOT NULL, 
	[SessionId] [uniqueidentifier] NOT NULL, 
	[IOFIELDId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[Region] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Subregion] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Name] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[Active] [bit] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[ReportSubscription] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Subject] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[ReportStartTime] [datetime] NULL, 
	[ReportEndTime] [datetime] NULL, 
	[StartDate] [datetime] NOT NULL, 
	[NextRuntime] [datetime] NULL, 
	[Frequency] [int] NOT NULL, 
	[ReportGenerationTime] [int] NOT NULL, 
	[ReportTypeId] [uniqueidentifier] NOT NULL, 
	[SiteId] [uniqueidentifier] NULL, 
	[PersonId] [uniqueidentifier] NULL, 
	[CustomerId] [uniqueidentifier] NULL, 
	[DepartmentId] [uniqueidentifier] NULL, 
	[GOUserId] [uniqueidentifier] NULL, 
	[EmailGroupsId] [uniqueidentifier] NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[ReportType] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Description] [nvarchar] (500) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Name] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[ReportType] [int] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[ServiceSettings] 
(
	[CurrentMeterReading] [float] NULL, 
	[LastServiceHours] [float] NULL, 
	[NextServiceType] [float] NULL, 
	[CurrentMeterReadingHrs] [float] NULL, 
	[Id] [uniqueidentifier] NOT NULL, 
	[NextServiceDate] [datetime] NULL, 
	[LastServiceDate] [datetime] NULL, 
	[CANBUS] [bit] NOT NULL, 
	[ServiceHoursInterval] [int] NULL, 
	[DateIntervalValue] [int] NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[Session] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[StartTime] [datetime] NOT NULL, 
	[EndTime] [datetime] NULL, 
	[isVOR] [bit] NULL, 
	[DriverId] [uniqueidentifier] NOT NULL, 
	[VehicleId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[SessionDetails] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Usage] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[SessionId] [uniqueidentifier] NOT NULL, 
	[IOFIELDId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[Site] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Name] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[Address] [nvarchar] (500) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[DeletedAtUtc] [datetime] NULL, 
	[UnlockSetting] [bit] NOT NULL, 
	[Active] [bit] NOT NULL, 
	[EnableUnlockReasonType] [int] NULL, 
	[TimezoneId] [uniqueidentifier] NOT NULL, 
	[CustomerId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[SiteVehicleMasterCardAccess] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[SiteId] [uniqueidentifier] NOT NULL, 
	[CardId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[SiteVehicleNormalCardAccess] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[PermissionId] [uniqueidentifier] NOT NULL, 
	[SiteId] [uniqueidentifier] NOT NULL, 
	[CardId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[SlamcoreAPIKey] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[APIKeyDisplay] [nvarchar] (1000) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[APIKey] [nvarchar] (1000) COLLATE SQL_Latin1_General_CP1_CI_AS NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[SlamcoreAwareAuthenticationDetails] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[UsernameDisplay] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Password] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Username] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[PasswordDisplay] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[SlamcoreDevice] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[IPAddress] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[Name] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[SerialNo] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[LastConnectedDateTime] [datetime] NULL, 
	[Status] [int] NOT NULL, 
	[UpdateRate] [int] NOT NULL, 
	[SlamcoreAwareAuthenticationDetailsId] [uniqueidentifier] NULL, 
	[SlamcoreAPIKeyId] [uniqueidentifier] NULL, 
	[CustomerId] [uniqueidentifier] NOT NULL, 
	[VehicleId] [uniqueidentifier] NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[SlamcoreDeviceHistory] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[CreatedDateTime] [datetime] NOT NULL, 
	[EventType] [int] NULL, 
	[VehicleId] [uniqueidentifier] NOT NULL, 
	[SlamcoreDeviceId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[Timezone] 
(
	[UTCOffset] [smallint] NOT NULL, 
	[Id] [uniqueidentifier] NOT NULL, 
	[TimezoneName] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[UpdateFirmwareRequest] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[FirmwareId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[UploadLogoRequest] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Logo] [nvarchar](1000) NOT NULL, 
	[LogoFileSize] [int] NOT NULL, 
	[LogoInternalName] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[Vehicle] 
(
	[IDLETimer] [int] NULL, 
	[Id] [uniqueidentifier] NOT NULL, 
	[ModuleSwapNote] [nvarchar] (300) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[SerialNo] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[LastSessionId] [nvarchar] (200) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Description] [nvarchar] (500) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[HireNo] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[LastSessionDateTzAdjusted] [datetime] NULL, 
	[DehireTime] [datetime] NULL, 
	[DeletedAtUtc] [datetime] NULL, 
	[HireTime] [datetime] NULL, 
	[LastSessionDate] [datetime] NULL, 
	[OnHire] [bit] NOT NULL, 
	[ModuleIsConnected] [bit] NOT NULL, 
	[ImpactLockout] [bit] NOT NULL, 
	[TimeoutEnabled] [bit] NOT NULL, 
	[IsCanbus] [bit] NOT NULL, 
	[VehicleImage] [nvarchar](1000) NULL, 
	[VehicleImageFileSize] [int] NULL, 
	[VehicleImageInternalName] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[FirmwareId] [uniqueidentifier] NULL, 
	[ModelId] [uniqueidentifier] NOT NULL, 
	[DriverId] [uniqueidentifier] NULL, 
	[InspectionId] [uniqueidentifier] NULL, 
	[DepartmentChecklistId] [uniqueidentifier] NULL, 
	[DepartmentId] [uniqueidentifier] NOT NULL, 
	[VehicleOtherSettingsId] [uniqueidentifier] NULL, 
	[PersonId] [uniqueidentifier] NULL, 
	[ServiceSettingsId] [uniqueidentifier] NULL, 
	[ChecklistSettingsId] [uniqueidentifier] NULL, 
	[CanruleId] [uniqueidentifier] NULL, 
	[SiteId] [uniqueidentifier] NOT NULL, 
	[ModuleId1] [uniqueidentifier] NOT NULL, 
	[CustomerId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[VehicleAlertSubscription] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[VehicleId] [uniqueidentifier] NOT NULL, 
	[AlertSubscriptionId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[VehicleBroadcastMessage] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[VehicleId] [uniqueidentifier] NOT NULL, 
	[BroadcastMessageId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[VehicleDiagnostic] 
(
	[RedImpactThreshold] [int] NULL, 
	[DatabaseRedImpactThreshold] [int] NULL, 
	[ShockThreshold] [int] NULL, 
	[SurveyTimeouts] [int] NULL, 
	[SignalStrength] [int] NULL, 
	[Id] [uniqueidentifier] NOT NULL, 
	[HardwareVersion] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[ModemVersion] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[APN] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[ExpansionModuleVersion] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[SeatIdles] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[CCID] [nvarchar] (150) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Timezone] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[CANCRC] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[FirmwareVersion] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[LastPARUpdate] [datetime] NULL, 
	[LastPreopCheck] [datetime] NULL, 
	[KernelBuildDate] [datetime] NULL, 
	[IsSynchronized] [bit] NOT NULL, 
	[VehicleId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[VehicleGPS] 
(
	[Longitude] [decimal] (18, 8) NOT NULL, 
	[Latitude] [decimal] (18, 8) NOT NULL, 
	[Id] [uniqueidentifier] NOT NULL, 
	[GPSDateTime] [datetime] NOT NULL, 
	[SessionId] [uniqueidentifier] NULL, 
	[VehicleId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[VehicleHireDehireHistory] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[VehicleIdWhenSaved] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[SerialNoWhenSaved] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[DeviceIdWhenSaved] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[DehireTime] [datetime] NULL, 
	[HireTime] [datetime] NULL, 
	[DepartmentId] [uniqueidentifier] NOT NULL, 
	[VehicleId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[VehicleHireDehireSynchronizationOptions] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[WillSynchronize] [bit] NOT NULL, 
	[Setting] [int] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[VehicleLockout] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Note] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Comment] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[LockoutTime] [datetime] NOT NULL, 
	[UnlockDateTime] [datetime] NOT NULL, 
	[Reason] [int] NOT NULL, 
	[RealImpact] [int] NULL, 
	[SessionId] [uniqueidentifier] NOT NULL, 
	[DriverId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[VehicleOtherSettings] 
(
	[FullLockoutTimeout] [smallint] NULL, 
	[Id] [uniqueidentifier] NOT NULL, 
	[VORStatus] [bit] NOT NULL, 
	[AmberAlertEnabled] [bit] NOT NULL, 
	[VORStatusConfirmed] [bit] NOT NULL, 
	[FullLockout] [bit] NOT NULL, 
	[DefaultTechnicianAccess] [bit] NOT NULL, 
	[PedestrianSafety] [bit] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[VehicleSessionlessImpact] 
(
	[ShockValue] [float] NOT NULL, 
	[Threshold] [float] NOT NULL, 
	[Id] [uniqueidentifier] NOT NULL, 
	[ImpactDateTime] [datetime] NOT NULL, 
	[VehicleId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[VehicleSlamcoreLocationHistory] 
(
	[YPosition] [decimal] (2, 2) NOT NULL, 
	[WOrientation] [decimal] (2, 2) NOT NULL, 
	[XOrientation] [decimal] (2, 2) NOT NULL, 
	[XPosition] [decimal] (2, 2) NOT NULL, 
	[ZPosition] [decimal] (2, 2) NOT NULL, 
	[YOrientation] [decimal] (2, 2) NOT NULL, 
	[Id] [uniqueidentifier] NOT NULL, 
	[AcquisitionDateTime] [datetime] NOT NULL, 
	[Status] [int] NOT NULL, 
	[EventType] [int] NULL, 
	[ReferenceFrameCategory] [int] NOT NULL, 
	[SlamcoreDeviceId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[VORSettingHistory] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[StartDateTime] [datetime] NOT NULL, 
	[EndDateTime] [datetime] NULL, 
	[Status] [int] NOT NULL, 
	[PersonId] [uniqueidentifier] NULL, 
	[VehicleId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[WebsiteRole] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[RoleName] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[WebsiteUserId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[WebsiteUser] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Username] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[Password] [nvarchar] (150) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[LassPasswordUpdate] [datetime] NOT NULL, 
	[Active] [bit] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[ZoneCoordinates] 
(
	[YCoordinate] [decimal] (20, 17) NOT NULL, 
	[XCoordinate] [decimal] (20, 17) NOT NULL, 
	[Order] [int] NOT NULL, 
	[Id] [uniqueidentifier] NOT NULL, 
	[FloorZonesId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
-- ----------------------------------------------------------------------------------------------------------------
-- Schema 'GOChangeTracking'
-- ----------------------------------------------------------------------------------------------------------------
CREATE SCHEMA [GOChangeTracking] /* AUTHORIZATION owner_name */
GO
-- -------[ Tables ]-----------------------------------------------------------------------------------------------
CREATE TABLE [GOChangeTracking].[CustomerAudit] 
(
	[fkRevisionDeleted] [int] NULL, 
	[fkRevisionLastModified] [int] NULL, 
	[fkRevisionCreated] [int] NULL, 
	[CreatedBy] [uniqueidentifier] NULL, 
	[LastModifiedBy] [uniqueidentifier] NULL, 
	[DeletedBy] [uniqueidentifier] NULL, 
	[fkCustomerId] [uniqueidentifier] NOT NULL, 
	[CreationDate] [datetime] NULL, 
	[LastModifiedDate] [datetime] NULL, 
	[DeletionDate] [datetime] NULL, 
	[IsDeleted] [bit] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [GOChangeTracking].[CustomerSnapshot] 
(
	[CustomerLogoFileSize] [int] NULL, 
	[CountryId] [uniqueidentifier] NOT NULL, 
	[fkCustomerId] [uniqueidentifier] NOT NULL, 
	[DealerId] [uniqueidentifier] NOT NULL, 
	[ContactPersonInformationId] [uniqueidentifier] NULL, 
	[ContractNumber] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Addess] [nvarchar] (250) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Description] [nvarchar] (500) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[ConnectionString] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[CustomerLogoInternalName] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Email] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Prefix] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[CompanyName] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[ContactNumber] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[ContractDate] [datetime] NULL, 
	[Addess_IsChanged] [bit] NULL, 
	[CustomerLogoInternalName_IsChanged] [bit] NULL, 
	[fkCustomerId_IsChanged] [bit] NULL, 
	[CompanyName_IsChanged] [bit] NULL, 
	[Description_IsChanged] [bit] NULL, 
	[Email_IsChanged] [bit] NULL, 
	[Active_IsChanged] [bit] NULL, 
	[CustomerLogo_IsChanged] [bit] NULL, 
	[ContractNumber_IsChanged] [bit] NULL, 
	[ContactNumber_IsChanged] [bit] NULL, 
	[Active] [bit] NOT NULL, 
	[Prefix_IsChanged] [bit] NULL, 
	[ContactPersonInformationId_IsChanged] [bit] NULL, 
	[CountryId_IsChanged] [bit] NULL, 
	[ConnectionString_IsChanged] [bit] NULL, 
	[CustomerLogoFileSize_IsChanged] [bit] NULL, 
	[DealerId_IsChanged] [bit] NULL, 
	[ContractDate_IsChanged] [bit] NULL, 
	[CustomerLogo] [nvarchar](1000) NULL, 
	[SnapshotId] [int] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [GOChangeTracking].[GOChangeDelta] 
(
	[GOChangeDeltaId] [uniqueidentifier] NOT NULL, 
	[When] [datetime] NOT NULL, 
	[Who] [nvarchar] (128) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[How] [nvarchar] (32) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[What] [nvarchar] (128) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[Key] [nvarchar] (256) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[Path] [nvarchar] (128) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[FromValue] [nvarchar] (256) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[ToValue] [nvarchar] (256) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [GOChangeTracking].[Revision] 
(
	[RevisionId] [int] IDENTITY (1,1) NOT NULL, 
	[fkGOUserId] [uniqueidentifier] NULL, 
	[When] [datetime] NOT NULL, 
	[TransactionInProgress] [bit] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [GOChangeTracking].[Snapshot] 
(
	[fkRevisionId] [int] NOT NULL, 
	[SnapshotId] [int] IDENTITY (1,1) NOT NULL, 
	[Change] [int] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [GOChangeTracking].[Tag] 
(
	[fkRevisionId] [int] NOT NULL, 
	[TagId] [uniqueidentifier] NOT NULL, 
	[Author] [uniqueidentifier] NULL, 
	[Label] [nvarchar] (256) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[Version] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Description] [nvarchar] (2048) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Summary] [nvarchar] (500) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[When] [datetime] NOT NULL, 
	[IsSystem] [bit] NOT NULL 
) ON [PRIMARY]
GO 
-- ----------------------------------------------------------------------------------------------------------------
-- Schema 'GOSecurity'
-- ----------------------------------------------------------------------------------------------------------------
CREATE SCHEMA [GOSecurity] /* AUTHORIZATION owner_name */
GO
-- -------[ Tables ]-----------------------------------------------------------------------------------------------
CREATE TABLE [GOSecurity].[GOGroup] 
(
	[Description] [nvarchar] (250) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[DisplayName] [nvarchar] (200) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[Name] [nvarchar] (200) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[IsSpecialGroup] [bit] NOT NULL, 
	[SpecialGroup] [int] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [GOSecurity].[GOGroupRole] 
(
	[GORoleName] [nvarchar] (200) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[GOGroupName] [nvarchar] (200) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [GOSecurity].[GOLoginHistory] 
(
	[Id] [int] IDENTITY (1,1) NOT NULL, 
	[User] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[Info] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[Timestamp] [datetime] NOT NULL, 
	[Result] [bit] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [GOSecurity].[GOUser] 
(
	[WebsiteAccessLevelValue] [smallint] NULL, 
	[Id] [uniqueidentifier] NOT NULL, 
	[ExternalUserId] [nvarchar] (500) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[NewEmailAddress] [nvarchar] (150) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[EmailAddress] [nvarchar] (150) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[UserName] [nvarchar] (150) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[FullName] [nvarchar] (250) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[AllowedDepartmentNames] [nvarchar] (1000) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[LastName] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[PreferredLocaleString] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[FirstName] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Password] [nvarchar] (150) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[PasswordExpiry] [datetime] NULL, 
	[EmailValidated] [bit] NOT NULL, 
	[UserValidated] [bit] NOT NULL, 
	[NewEmailValidated] [bit] NULL, 
	[Blocked] [bit] NOT NULL, 
	[Unregistered] [bit] NOT NULL, 
	[DealerAdmin] [bit] NULL, 
	[EmailChangeValidationInProgress] [bit] NOT NULL, 
	[WebsiteAccessLevel] [int] NOT NULL, 
	[PreferredLocale] [int] NULL, 
	[DealerId] [uniqueidentifier] NULL, 
	[GORoleName] [nvarchar] (200) COLLATE SQL_Latin1_General_CP1_CI_AS NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [GOSecurity].[GOUser2FA] 
(
	[_2FAConnectionFailedCounter] [smallint] NOT NULL, 
	[OTPSecret] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Last2FAConnexionTry] [datetime] NULL, 
	[OTPGenerationDateTime] [datetime] NULL, 
	[Is2FAEnabled] [bit] NULL, 
	[Id] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [GOSecurity].[GOUserGroup] 
(
	[GOUserId] [uniqueidentifier] NOT NULL, 
	[GOGroupName] [nvarchar] (200) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [GOSecurity].[GOUserRole] 
(
	[GOUserId] [uniqueidentifier] NOT NULL, 
	[GORoleName] [nvarchar] (200) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL 
) ON [PRIMARY]
GO 
-- ----------------------------------------------------------------------------------------------------------------
-- Schema 'ImportExport'
-- ----------------------------------------------------------------------------------------------------------------
CREATE SCHEMA [ImportExport] /* AUTHORIZATION owner_name */
GO
-- -------[ Tables ]-----------------------------------------------------------------------------------------------
CREATE TABLE [ImportExport].[ExportJobStatus] 
(
	[ExportLogFile] [nvarchar](1000) NULL, 
	[ExportedFile] [nvarchar](1000) NULL, 
	[Id] [uniqueidentifier] NOT NULL, 
	[ExportedFileFileSize] [int] NULL, 
	[ExportLogFileFileSize] [int] NULL, 
	[ExportLogFileInternalName] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[ExportedFileInternalName] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[GOUserId] [uniqueidentifier] NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [ImportExport].[ImportJobBatch] 
(
	[NumberOfImportsProcessed] [smallint] NOT NULL, 
	[Id] [int] IDENTITY (1,1) NOT NULL, 
	[Name] [nvarchar] (300) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[DateStart] [datetime] NOT NULL, 
	[DateEnd] [datetime] NULL, 
	[Status] [int] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [ImportExport].[ImportJobLog] 
(
	[ImportJobBatchId] [int] NULL, 
	[LineNumber] [int] NULL, 
	[Id] [int] IDENTITY (1,1) NOT NULL, 
	[ImportJobStatusId] [uniqueidentifier] NOT NULL, 
	[Details] [nvarchar] (4000) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Data] [nvarchar] (4000) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Log] [nvarchar] (2000) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[Date] [datetime] NOT NULL, 
	[LogType] [int] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [ImportExport].[ImportJobStatus] 
(
	[NumberOfLinesWithWarning] [int] NOT NULL, 
	[NumberOfLinesInError] [int] NOT NULL, 
	[TotalNumberOfLines] [int] NOT NULL, 
	[NumberOfLinesProcessed] [int] NOT NULL, 
	[ImportJobBatchId] [int] NULL, 
	[NumberOfLinesSkipped] [int] NOT NULL, 
	[ImportFileModificationDate] [datetime] NULL, 
	[IsLocalFile] [bit] NOT NULL, 
	[FileToUpload] [nvarchar](1000) NOT NULL, 
	[Id] [uniqueidentifier] NOT NULL, 
	[FileToUploadFileSize] [int] NOT NULL, 
	[FileToUploadInternalName] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL 
) ON [PRIMARY]
GO 
-- ----------------------------------------------------------------------------------------------------------------
-- LiveUpdate Model Sync
-- ----------------------------------------------------------------------------------------------------------------
CREATE SCHEMA [GO.LiveUpdate]
GO
CREATE TABLE [GO.LiveUpdate].[ModelSync](
	[Id] [uniqueidentifier] NOT NULL,
	[ModelRevisionId] [int] NOT NULL,
	[When] [datetime] NOT NULL,
CONSTRAINT [PK_ModelSyncId] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO 
-- ###############################################################################################################
-- Create statements for Primary key constraints, Foreign key constraints and Unique constraints
-- ###############################################################################################################
-- ----------------------------------------------------------------------------------------------------------------
-- Catalog 'FleetXQDB/FleetXQ.Application-8735218d-3aeb-4563-bccb-8cdfcdf1188f'
-- ----------------------------------------------------------------------------------------------------------------
-- ----------------------------------------------------------------------------------------------------------------
-- Primary Key constraints for schema 'dbo'
-- ----------------------------------------------------------------------------------------------------------------
ALTER TABLE [dbo].[AccessGroup] WITH NOCHECK 
	ADD CONSTRAINT [PK_AccessGroup] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[AccessGroupTemplate] WITH NOCHECK 
	ADD CONSTRAINT [PK_AccessGroupTemplate] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[AccessGroupToSite] WITH NOCHECK 
	ADD CONSTRAINT [PK_AccessGroupToSite] PRIMARY KEY CLUSTERED 
	( 
		[AccessGroupId], [SiteId] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[Alert] WITH NOCHECK 
	ADD CONSTRAINT [PK_Alert] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[AlertHistory] WITH NOCHECK 
	ADD CONSTRAINT [PK_AlertHistory] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[AlertSubscription] WITH NOCHECK 
	ADD CONSTRAINT [PK_AlertSubscription] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[BroadcastMessage] WITH NOCHECK 
	ADD CONSTRAINT [PK_BroadcastMessage] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[BroadcastMessageHistory] WITH NOCHECK 
	ADD CONSTRAINT [PK_BroadcastMessageHistory] PRIMARY KEY CLUSTERED 
	( 
		[MessageId] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[Canrule] WITH NOCHECK 
	ADD CONSTRAINT [PK_Canrule] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[CanruleDetails] WITH NOCHECK 
	ADD CONSTRAINT [PK_CanruleDetails] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[Card] WITH NOCHECK 
	ADD CONSTRAINT [PK_Card] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[CardToCardAccess] WITH NOCHECK 
	ADD CONSTRAINT [PK_CardToCardAccess] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[CategoryTemplate] WITH NOCHECK 
	ADD CONSTRAINT [PK_CategoryTemplate] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[ChecklistDetail] WITH NOCHECK 
	ADD CONSTRAINT [PK_ChecklistDetail] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[ChecklistResult] WITH NOCHECK 
	ADD CONSTRAINT [PK_ChecklistResult] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[ChecklistSettings] WITH NOCHECK 
	ADD CONSTRAINT [PK_ChecklistSettings] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[ContactPersonInformation] WITH NOCHECK 
	ADD CONSTRAINT [PK_ContactPersonInformation] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[Country] WITH NOCHECK 
	ADD CONSTRAINT [PK_Country] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[Customer] WITH NOCHECK 
	ADD CONSTRAINT [PK_Customer] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[CustomerFeatureSubscription] WITH NOCHECK 
	ADD CONSTRAINT [PK_CustomerFeatureSubscription] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[CustomerModel] WITH NOCHECK 
	ADD CONSTRAINT [PK_CustomerModel] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[CustomerPreOperationalChecklistTemplate] WITH NOCHECK 
	ADD CONSTRAINT [PK_CustomerPreOperationalChecklistTemplate] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[CustomerSSODetail] WITH NOCHECK 
	ADD CONSTRAINT [PK_CustomerSSODetail] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[Dealer] WITH NOCHECK 
	ADD CONSTRAINT [PK_Dealer] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[DealerConfiguration] WITH NOCHECK 
	ADD CONSTRAINT [PK_DealerConfiguration] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[DealerDriver] WITH NOCHECK 
	ADD CONSTRAINT [PK_DealerDriver] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[DealerFeatureSubscription] WITH NOCHECK 
	ADD CONSTRAINT [PK_DealerFeatureSubscription] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[Department] WITH NOCHECK 
	ADD CONSTRAINT [PK_Department] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[DepartmentChecklist] WITH NOCHECK 
	ADD CONSTRAINT [PK_DepartmentChecklist] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[DepartmentHourSettings] WITH NOCHECK 
	ADD CONSTRAINT [PK_DepartmentHourSettings] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[DepartmentVehicleMasterCardAccess] WITH NOCHECK 
	ADD CONSTRAINT [PK_DepartmentVehicleMasterCardAccess] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[DepartmentVehicleNormalCardAccess] WITH NOCHECK 
	ADD CONSTRAINT [PK_DepartmentVehicleNormalCardAccess] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[Driver] WITH NOCHECK 
	ADD CONSTRAINT [PK_Driver] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[EmailGroups] WITH NOCHECK 
	ADD CONSTRAINT [PK_EmailGroups] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[EmailGroupsToPerson] WITH NOCHECK 
	ADD CONSTRAINT [PK_EmailGroupsToPerson] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[FeatureSubscriptionTemplate] WITH NOCHECK 
	ADD CONSTRAINT [PK_FeatureSubscriptionTemplate] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[Firmware] WITH NOCHECK 
	ADD CONSTRAINT [PK_Firmware] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[FloorPlan] WITH NOCHECK 
	ADD CONSTRAINT [PK_FloorPlan] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[FloorZones] WITH NOCHECK 
	ADD CONSTRAINT [PK_FloorZones] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[GOTask] WITH NOCHECK 
	ADD CONSTRAINT [PK_GOTask] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[GOUserDepartment] WITH NOCHECK 
	ADD CONSTRAINT [PK_GOUserDepartment] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[GoUserToCustomer] WITH NOCHECK 
	ADD CONSTRAINT [PK_GoUserToCustomer] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[GPSHistory] WITH NOCHECK 
	ADD CONSTRAINT [PK_GPSHistory] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[Impact] WITH NOCHECK 
	ADD CONSTRAINT [PK_Impact] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[Inspection] WITH NOCHECK 
	ADD CONSTRAINT [PK_Inspection] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[IOFIELD] WITH NOCHECK 
	ADD CONSTRAINT [PK_IOFIELD] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[IoTDeviceMessageCache] WITH NOCHECK 
	ADD CONSTRAINT [PK_IoTDeviceMessageCache] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[LicenceDetail] WITH NOCHECK 
	ADD CONSTRAINT [PK_LicenceDetail] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[LicenseByModel] WITH NOCHECK 
	ADD CONSTRAINT [PK_LicenseByModel] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[MessageHistory] WITH NOCHECK 
	ADD CONSTRAINT [PK_MessageHistory] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[Model] WITH NOCHECK 
	ADD CONSTRAINT [PK_Model] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[ModelVehicleMasterCardAccess] WITH NOCHECK 
	ADD CONSTRAINT [PK_ModelVehicleMasterCardAccess] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[ModelVehicleNormalCardAccess] WITH NOCHECK 
	ADD CONSTRAINT [PK_ModelVehicleNormalCardAccess] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[Module] WITH NOCHECK 
	ADD CONSTRAINT [PK_Module] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[ModuleHistory] WITH NOCHECK 
	ADD CONSTRAINT [PK_ModuleHistory] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[NetworkSettings] WITH NOCHECK 
	ADD CONSTRAINT [PK_NetworkSettings] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[OnDemandSession] WITH NOCHECK 
	ADD CONSTRAINT [PK_OnDemandSession] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[OnDemandSettings] WITH NOCHECK 
	ADD CONSTRAINT [PK_OnDemandSettings] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[PedestrianDetectionHistory] WITH NOCHECK 
	ADD CONSTRAINT [PK_PedestrianDetectionHistory] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[Permission] WITH NOCHECK 
	ADD CONSTRAINT [PK_Permission] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[Person] WITH NOCHECK 
	ADD CONSTRAINT [PK_Person] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[PersonAllocation] WITH NOCHECK 
	ADD CONSTRAINT [PK_PersonAllocation] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[PersonChecklistLanguageSettings] WITH NOCHECK 
	ADD CONSTRAINT [PK_PersonChecklistLanguageSettings] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[PerVehicleMasterCardAccess] WITH NOCHECK 
	ADD CONSTRAINT [PK_PerVehicleMasterCardAccess] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[PerVehicleNormalCardAccess] WITH NOCHECK 
	ADD CONSTRAINT [PK_PerVehicleNormalCardAccess] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[PreOperationalChecklist] WITH NOCHECK 
	ADD CONSTRAINT [PK_PreOperationalChecklist] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[PSTATDetails] WITH NOCHECK 
	ADD CONSTRAINT [PK_PSTATDetails] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[Region] WITH NOCHECK 
	ADD CONSTRAINT [PK_Region] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[ReportSubscription] WITH NOCHECK 
	ADD CONSTRAINT [PK_ReportSubscription] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[ReportType] WITH NOCHECK 
	ADD CONSTRAINT [PK_ReportType] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[ServiceSettings] WITH NOCHECK 
	ADD CONSTRAINT [PK_ServiceSettings] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[Session] WITH NOCHECK 
	ADD CONSTRAINT [PK_Session] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[SessionDetails] WITH NOCHECK 
	ADD CONSTRAINT [PK_SessionDetails] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[Site] WITH NOCHECK 
	ADD CONSTRAINT [PK_Site] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[SiteVehicleMasterCardAccess] WITH NOCHECK 
	ADD CONSTRAINT [PK_SiteVehicleMasterCardAccess] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[SiteVehicleNormalCardAccess] WITH NOCHECK 
	ADD CONSTRAINT [PK_SiteVehicleNormalCardAccess] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[SlamcoreAPIKey] WITH NOCHECK 
	ADD CONSTRAINT [PK_SlamcoreAPIKey] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[SlamcoreAwareAuthenticationDetails] WITH NOCHECK 
	ADD CONSTRAINT [PK_SlamcoreAwareAuthenticationDetails] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[SlamcoreDevice] WITH NOCHECK 
	ADD CONSTRAINT [PK_SlamcoreDevice] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[SlamcoreDeviceHistory] WITH NOCHECK 
	ADD CONSTRAINT [PK_SlamcoreDeviceHistory] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[Timezone] WITH NOCHECK 
	ADD CONSTRAINT [PK_Timezone] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[UpdateFirmwareRequest] WITH NOCHECK 
	ADD CONSTRAINT [PK_UpdateFirmwareRequest] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[UploadLogoRequest] WITH NOCHECK 
	ADD CONSTRAINT [PK_UploadLogoRequest] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[Vehicle] WITH NOCHECK 
	ADD CONSTRAINT [PK_Vehicle] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[VehicleAlertSubscription] WITH NOCHECK 
	ADD CONSTRAINT [PK_VehicleAlertSubscription] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[VehicleBroadcastMessage] WITH NOCHECK 
	ADD CONSTRAINT [PK_VehicleBroadcastMessage] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[VehicleDiagnostic] WITH NOCHECK 
	ADD CONSTRAINT [PK_VehicleDiagnostic] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[VehicleGPS] WITH NOCHECK 
	ADD CONSTRAINT [PK_VehicleGPS] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[VehicleHireDehireHistory] WITH NOCHECK 
	ADD CONSTRAINT [PK_VehicleHireDehireHistory] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[VehicleHireDehireSynchronizationOptions] WITH NOCHECK 
	ADD CONSTRAINT [PK_VehicleHireDehireSynchronizationOptions] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[VehicleLockout] WITH NOCHECK 
	ADD CONSTRAINT [PK_VehicleLockout] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[VehicleOtherSettings] WITH NOCHECK 
	ADD CONSTRAINT [PK_VehicleOtherSettings] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[VehicleSessionlessImpact] WITH NOCHECK 
	ADD CONSTRAINT [PK_VehicleSessionlessImpact] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[VehicleSlamcoreLocationHistory] WITH NOCHECK 
	ADD CONSTRAINT [PK_VehicleSlamcoreLocationHistory] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[VORSettingHistory] WITH NOCHECK 
	ADD CONSTRAINT [PK_VORSettingHistory] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[WebsiteRole] WITH NOCHECK 
	ADD CONSTRAINT [PK_WebsiteRole] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[WebsiteUser] WITH NOCHECK 
	ADD CONSTRAINT [PK_WebsiteUser] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [dbo].[ZoneCoordinates] WITH NOCHECK 
	ADD CONSTRAINT [PK_ZoneCoordinates] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
-- ----------------------------------------------------------------------------------------------------------------
-- Unique constraints for schema 'dbo'
-- ----------------------------------------------------------------------------------------------------------------
CREATE UNIQUE NONCLUSTERED INDEX [U_CustomerContactPersonInformation]
ON [dbo].[Customer] 	
	([ContactPersonInformationId]) 
WHERE 
	[ContactPersonInformationId] IS NOT NULL  
GO 
CREATE UNIQUE NONCLUSTERED INDEX [U_CustomerCustomerFeatureSubscription]
ON [dbo].[Customer] 	
	([CustomerFeatureSubscriptionId]) 
WHERE 
	[CustomerFeatureSubscriptionId] IS NOT NULL  
GO 
CREATE UNIQUE NONCLUSTERED INDEX [U_DealerDealerConfiguration]
ON [dbo].[Dealer] 	
	([DealerConfigurationId]) 
WHERE 
	[DealerConfigurationId] IS NOT NULL  
GO 
CREATE UNIQUE NONCLUSTERED INDEX [U_DealerDealerFeatureSubscription]
ON [dbo].[Dealer] 	
	([DealerFeatureSubscriptionId]) 
WHERE 
	[DealerFeatureSubscriptionId] IS NOT NULL  
GO 
CREATE UNIQUE NONCLUSTERED INDEX [U_DealerDriverCard]
ON [dbo].[DealerDriver] 	
	([CardId]) 
WHERE 
	[CardId] IS NOT NULL  
GO 
IF (SELECT COUNT (*) FROM [dbo].[DealerDriver] WHERE [GOUserId] IS NULL) <= 1 
BEGIN
CREATE UNIQUE NONCLUSTERED INDEX [U_DealerDriverGOUser]
ON [dbo].[DealerDriver] 	
	([GOUserId]) 
END
ELSE
BEGIN
	PRINT 'WARNING: Failed to apply Unique Constraint on entity table DealerDriver - there is more than one existing NULL in mandatory column(s): [GOUserId]'
END
GO 
CREATE UNIQUE NONCLUSTERED INDEX [U_DepartmentDepartmentHourSettings]
ON [dbo].[Department] 	
	([DepartmentHourSettingsId]) 
WHERE 
	[DepartmentHourSettingsId] IS NOT NULL  
GO 
CREATE UNIQUE NONCLUSTERED INDEX [U_DriverCardDetails]
ON [dbo].[Driver] 	
	([CardDetailsId]) 
WHERE 
	[CardDetailsId] IS NOT NULL  
GO 
CREATE UNIQUE NONCLUSTERED INDEX [U_DriverLicenceDetail]
ON [dbo].[Driver] 	
	([LicenceDetailId]) 
WHERE 
	[LicenceDetailId] IS NOT NULL  
GO 
IF (SELECT COUNT (*) FROM [dbo].[Module] WHERE [IoTDevice] IS NULL) <= 1 
BEGIN
CREATE UNIQUE NONCLUSTERED INDEX [U_IoTDeviceIdConstraint]
ON [dbo].[Module] 	
	([IoTDevice]) 
END
ELSE
BEGIN
	PRINT 'WARNING: Failed to apply Unique Constraint on entity table Module - there is more than one existing NULL in mandatory column(s): [IoTDevice]'
END
GO 
IF (SELECT COUNT (*) FROM [dbo].[OnDemandSettings] WHERE [VehicleId] IS NULL) <= 1 
BEGIN
CREATE UNIQUE NONCLUSTERED INDEX [U_OnDemandSettingsVehicle]
ON [dbo].[OnDemandSettings] 	
	([VehicleId]) 
END
ELSE
BEGIN
	PRINT 'WARNING: Failed to apply Unique Constraint on entity table OnDemandSettings - there is more than one existing NULL in mandatory column(s): [VehicleId]'
END
GO 
CREATE UNIQUE NONCLUSTERED INDEX [U_PersonDriver]
ON [dbo].[Person] 	
	([DriverId]) 
WHERE 
	[DriverId] IS NOT NULL  
GO 
CREATE UNIQUE NONCLUSTERED INDEX [U_PersonWebsiteUser]
ON [dbo].[Person] 	
	([WebsiteUserId]) 
WHERE 
	[WebsiteUserId] IS NOT NULL  
GO 
CREATE UNIQUE NONCLUSTERED INDEX [U_PersonGOUser]
ON [dbo].[Person] 	
	([GOUserId]) 
WHERE 
	[GOUserId] IS NOT NULL  
GO 
CREATE UNIQUE NONCLUSTERED INDEX [U_PersonPersonChecklistLanguageSettings]
ON [dbo].[Person] 	
	([PersonChecklistLanguageSettingsId]) 
WHERE 
	[PersonChecklistLanguageSettingsId] IS NOT NULL  
GO 
CREATE UNIQUE NONCLUSTERED INDEX [U_SlamcoreDeviceSlamcoreAPIKey]
ON [dbo].[SlamcoreDevice] 	
	([SlamcoreAPIKeyId]) 
WHERE 
	[SlamcoreAPIKeyId] IS NOT NULL  
GO 
CREATE UNIQUE NONCLUSTERED INDEX [U_SlamcoreDeviceVehicle]
ON [dbo].[SlamcoreDevice] 	
	([VehicleId]) 
WHERE 
	[VehicleId] IS NOT NULL  
GO 
CREATE UNIQUE NONCLUSTERED INDEX [U_SlamcoreDeviceSlamcoreAwareAuthenticationDetails]
ON [dbo].[SlamcoreDevice] 	
	([SlamcoreAwareAuthenticationDetailsId]) 
WHERE 
	[SlamcoreAwareAuthenticationDetailsId] IS NOT NULL  
GO 
CREATE UNIQUE NONCLUSTERED INDEX [U_UVehicleChecklistSettings]
ON [dbo].[Vehicle] 	
	([ChecklistSettingsId]) 
WHERE 
	[ChecklistSettingsId] IS NOT NULL  
GO 
CREATE UNIQUE NONCLUSTERED INDEX [U_VehicleVehicleOtherSettings]
ON [dbo].[Vehicle] 	
	([VehicleOtherSettingsId]) 
WHERE 
	[VehicleOtherSettingsId] IS NOT NULL  
GO 
CREATE UNIQUE NONCLUSTERED INDEX [U_VehicleInspection]
ON [dbo].[Vehicle] 	
	([InspectionId]) 
WHERE 
	[InspectionId] IS NOT NULL  
GO 
CREATE UNIQUE NONCLUSTERED INDEX [U_UVehicleServiceSettings]
ON [dbo].[Vehicle] 	
	([ServiceSettingsId]) 
WHERE 
	[ServiceSettingsId] IS NOT NULL  
GO 
IF (SELECT COUNT (*) FROM [dbo].[VehicleDiagnostic] WHERE [VehicleId] IS NULL) <= 1 
BEGIN
CREATE UNIQUE NONCLUSTERED INDEX [U_VehicleDiagnosticVehicle]
ON [dbo].[VehicleDiagnostic] 	
	([VehicleId]) 
END
ELSE
BEGIN
	PRINT 'WARNING: Failed to apply Unique Constraint on entity table VehicleDiagnostic - there is more than one existing NULL in mandatory column(s): [VehicleId]'
END
GO 
-- ----------------------------------------------------------------------------------------------------------------
-- Primary Key constraints for schema 'GOChangeTracking'
-- ----------------------------------------------------------------------------------------------------------------
ALTER TABLE [GOChangeTracking].[CustomerAudit] WITH NOCHECK 
	ADD CONSTRAINT [PK_CustomerAudit] PRIMARY KEY CLUSTERED 
	( 
		[fkCustomerId] 
	) ON [PRIMARY]
GO 
ALTER TABLE [GOChangeTracking].[CustomerSnapshot] WITH NOCHECK 
	ADD CONSTRAINT [PK_CustomerSnapshot] PRIMARY KEY CLUSTERED 
	( 
		[SnapshotId] 
	) ON [PRIMARY]
GO 
ALTER TABLE [GOChangeTracking].[GOChangeDelta] WITH NOCHECK 
	ADD CONSTRAINT [PK_GOChangeDelta] PRIMARY KEY CLUSTERED 
	( 
		[GOChangeDeltaId] 
	) ON [PRIMARY]
GO 
ALTER TABLE [GOChangeTracking].[Revision] WITH NOCHECK 
	ADD CONSTRAINT [PK_Revision] PRIMARY KEY CLUSTERED 
	( 
		[RevisionId] 
	) ON [PRIMARY]
GO 
ALTER TABLE [GOChangeTracking].[Snapshot] WITH NOCHECK 
	ADD CONSTRAINT [PK_Snapshot] PRIMARY KEY CLUSTERED 
	( 
		[SnapshotId] 
	) ON [PRIMARY]
GO 
ALTER TABLE [GOChangeTracking].[Tag] WITH NOCHECK 
	ADD CONSTRAINT [PK_Tag] PRIMARY KEY CLUSTERED 
	( 
		[TagId] 
	) ON [PRIMARY]
GO 
-- ----------------------------------------------------------------------------------------------------------------
-- Unique constraints for schema 'GOChangeTracking'
-- ----------------------------------------------------------------------------------------------------------------
-- ----------------------------------------------------------------------------------------------------------------
-- Primary Key constraints for schema 'GOSecurity'
-- ----------------------------------------------------------------------------------------------------------------
ALTER TABLE [GOSecurity].[GOGroup] WITH NOCHECK 
	ADD CONSTRAINT [PK_GOGroup] PRIMARY KEY CLUSTERED 
	( 
		[Name] 
	) ON [PRIMARY]
GO 
ALTER TABLE [GOSecurity].[GOGroupRole] WITH NOCHECK 
	ADD CONSTRAINT [PK_GOGroupRole] PRIMARY KEY CLUSTERED 
	( 
		[GOGroupName], [GORoleName] 
	) ON [PRIMARY]
GO 
ALTER TABLE [GOSecurity].[GOLoginHistory] WITH NOCHECK 
	ADD CONSTRAINT [PK_GOLoginHistory] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [GOSecurity].[GOUser] WITH NOCHECK 
	ADD CONSTRAINT [PK_GOUser] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [GOSecurity].[GOUser2FA] WITH NOCHECK 
	ADD CONSTRAINT [PK_GOUser2FA] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [GOSecurity].[GOUserGroup] WITH NOCHECK 
	ADD CONSTRAINT [PK_GOUserGroup] PRIMARY KEY CLUSTERED 
	( 
		[GOGroupName], [GOUserId] 
	) ON [PRIMARY]
GO 
ALTER TABLE [GOSecurity].[GOUserRole] WITH NOCHECK 
	ADD CONSTRAINT [PK_GOUserRole] PRIMARY KEY CLUSTERED 
	( 
		[GORoleName], [GOUserId] 
	) ON [PRIMARY]
GO 
-- ----------------------------------------------------------------------------------------------------------------
-- Unique constraints for schema 'GOSecurity'
-- ----------------------------------------------------------------------------------------------------------------
IF (SELECT COUNT (*) FROM [GOSecurity].[GOUser] WHERE [UserName] IS NULL) <= 1 
BEGIN
CREATE UNIQUE NONCLUSTERED INDEX [U_UserNameUniqueConstraint]
ON [GOSecurity].[GOUser] 	
	([UserName]) 
END
ELSE
BEGIN
	PRINT 'WARNING: Failed to apply Unique Constraint on entity table GOUser - there is more than one existing NULL in mandatory column(s): [UserName]'
END
GO 
-- ----------------------------------------------------------------------------------------------------------------
-- Primary Key constraints for schema 'ImportExport'
-- ----------------------------------------------------------------------------------------------------------------
ALTER TABLE [ImportExport].[ExportJobStatus] WITH NOCHECK 
	ADD CONSTRAINT [PK_ExportJobStatus] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [ImportExport].[ImportJobBatch] WITH NOCHECK 
	ADD CONSTRAINT [PK_ImportJobBatch] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [ImportExport].[ImportJobLog] WITH NOCHECK 
	ADD CONSTRAINT [PK_ImportJobLog] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
ALTER TABLE [ImportExport].[ImportJobStatus] WITH NOCHECK 
	ADD CONSTRAINT [PK_ImportJobStatus] PRIMARY KEY CLUSTERED 
	( 
		[Id] 
	) ON [PRIMARY]
GO 
-- ----------------------------------------------------------------------------------------------------------------
-- Unique constraints for schema 'ImportExport'
-- ----------------------------------------------------------------------------------------------------------------

-- ----------------------------------------------------------------------------------------------------------------
-- All foreign Key constraints
-- ----------------------------------------------------------------------------------------------------------------
ALTER TABLE [dbo].[AccessGroup] 
	ADD CONSTRAINT [FK_AccessGroup_Customer_3dcf7432-f26c-4de3-a04d-bf48cce0ea88] FOREIGN KEY
	(
		[CustomerId] 
	)
	REFERENCES [dbo].[Customer]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[AccessGroupToSite] 
	ADD CONSTRAINT [FK_AccessGroupToSite_AccessGroup_fd6c2386-2d53-4f4b-8a3d-267bfa5feec9] FOREIGN KEY
	(
		[AccessGroupId] 
	)
	REFERENCES [dbo].[AccessGroup]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[AccessGroupToSite] 
	ADD CONSTRAINT [FK_AccessGroupToSite_Site_087b278d-2d76-4731-bd7b-84d845ddb3ee] FOREIGN KEY
	(
		[SiteId] 
	)
	REFERENCES [dbo].[Site]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[AlertHistory] 
	ADD CONSTRAINT [FK_AlertHistory_Vehicle_8c579211-9952-41c6-b1e8-821e6e699159] FOREIGN KEY
	(
		[VehicleId] 
	)
	REFERENCES [dbo].[Vehicle]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[AlertHistory] 
	ADD CONSTRAINT [FK_AlertHistory_Alert_148ee7b8-5d37-41a9-87ce-e0e76cee4757] FOREIGN KEY
	(
		[AlertId] 
	)
	REFERENCES [dbo].[Alert]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[AlertHistory] 
	ADD CONSTRAINT [FK_AlertHistory_Driver_dcf6dc2c-4cf3-49dc-bede-9c0cf9ba8ba2] FOREIGN KEY
	(
		[DriverId] 
	)
	REFERENCES [dbo].[Driver]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[AlertSubscription] 
	ADD CONSTRAINT [FK_AlertSubscription_Person_5650d143-f68b-4a7c-a561-162813c37de0] FOREIGN KEY
	(
		[PersonId] 
	)
	REFERENCES [dbo].[Person]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[AlertSubscription] 
	ADD CONSTRAINT [FK_AlertSubscription_GOUser_ed5a2dad-e169-4809-ae6c-279241d590aa] FOREIGN KEY
	(
		[GOUserId] 
	)
	REFERENCES [GOSecurity].[GOUser]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[AlertSubscription] 
	ADD CONSTRAINT [FK_AlertSubscription_Alert_b2379aa3-6eed-4fe4-9267-c57819974464] FOREIGN KEY
	(
		[AlertId] 
	)
	REFERENCES [dbo].[Alert]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[BroadcastMessageHistory] 
	ADD CONSTRAINT [FK_BroadcastMessageHistory_Vehicle_bd33cde8-c5ce-41a5-a911-ef2d590b5d13] FOREIGN KEY
	(
		[VehicleId] 
	)
	REFERENCES [dbo].[Vehicle]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[BroadcastMessageHistory] 
	ADD CONSTRAINT [FK_BroadcastMessageHistory_Driver_f3a7c35a-b39a-4261-a285-03308be3776d] FOREIGN KEY
	(
		[DriverId] 
	)
	REFERENCES [dbo].[Driver]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[CanruleDetails] 
	ADD CONSTRAINT [FK_CanruleDetails_Canrule_3cfcf9eb-91f5-466e-959b-3762dc01edd4] FOREIGN KEY
	(
		[CanruleId] 
	)
	REFERENCES [dbo].[Canrule]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[CardToCardAccess] 
	ADD CONSTRAINT [FK_CardToCardAccess_Card_21f41fe7-31d6-4778-9ae9-3e1abc651c7d] FOREIGN KEY
	(
		[CardDetailsId] 
	)
	REFERENCES [dbo].[Card]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[CardToCardAccess] 
	ADD CONSTRAINT [FK_CardToCardAccess_Permission_409a502a-2d11-4782-a8aa-9d7a155dbe5e] FOREIGN KEY
	(
		[PermissionId] 
	)
	REFERENCES [dbo].[Permission]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[ChecklistDetail] 
	ADD CONSTRAINT [FK_ChecklistDetail_ChecklistResult_da775c12-04e7-4564-b1c6-cc8e22c047f9] FOREIGN KEY
	(
		[ChecklistResultId] 
	)
	REFERENCES [dbo].[ChecklistResult]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[ChecklistDetail] 
	ADD CONSTRAINT [FK_ChecklistDetail_PreOperationalChecklist_0e042e02-8b9f-4e46-ac36-dc6a1595adec] FOREIGN KEY
	(
		[PreOperationalChecklistId] 
	)
	REFERENCES [dbo].[PreOperationalChecklist]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[ChecklistResult] 
	ADD CONSTRAINT [FK_ChecklistResult_Session_e15496e6-cf49-4b48-87d9-ab93d49b1e90] FOREIGN KEY
	(
		[SessionId1] 
	)
	REFERENCES [dbo].[Session]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[Customer] 
	ADD CONSTRAINT [FK_Customer_CustomerFeatureSubscription_945c6194-41db-46b7-80c1-d6bad91dbfb2] FOREIGN KEY
	(
		[CustomerFeatureSubscriptionId] 
	)
	REFERENCES [dbo].[CustomerFeatureSubscription]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[Customer] 
	ADD CONSTRAINT [FK_Customer_Dealer_c083c856-084c-4012-8e5d-23fd69e310a3] FOREIGN KEY
	(
		[DealerId] 
	)
	REFERENCES [dbo].[Dealer]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[Customer] 
	ADD CONSTRAINT [FK_Customer_ContactPersonInformation_0026cacb-d167-4502-8b19-5f41c24c24a2] FOREIGN KEY
	(
		[ContactPersonInformationId] 
	)
	REFERENCES [dbo].[ContactPersonInformation]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[Customer] 
	ADD CONSTRAINT [FK_Customer_Country_1d85d2e8-296d-4323-a1ea-ca5d7f1ccc2c] FOREIGN KEY
	(
		[CountryId] 
	)
	REFERENCES [dbo].[Country]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[CustomerModel] 
	ADD CONSTRAINT [FK_CustomerModel_Customer_90952c06-c7f0-4ec3-8b05-4cbf06e42045] FOREIGN KEY
	(
		[CustomerId] 
	)
	REFERENCES [dbo].[Customer]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[CustomerModel] 
	ADD CONSTRAINT [FK_CustomerModel_Model_6882ed0b-3622-4f07-ab93-ee61b7e54a49] FOREIGN KEY
	(
		[ModelId] 
	)
	REFERENCES [dbo].[Model]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[CustomerPreOperationalChecklistTemplate] 
	ADD CONSTRAINT [FK_CustomerPreOperationalChecklistTemplate_Customer_1e748209-6977-4409-9545-31d7b1883df3] FOREIGN KEY
	(
		[CustomerId] 
	)
	REFERENCES [dbo].[Customer]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[CustomerSSODetail] 
	ADD CONSTRAINT [FK_CustomerSSODetail_Customer_44e029f2-3574-402a-b4e7-21893477a0f5] FOREIGN KEY
	(
		[CustomerId] 
	)
	REFERENCES [dbo].[Customer]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[Dealer] 
	ADD CONSTRAINT [FK_Dealer_DealerConfiguration_2b931b10-e2fb-4449-a389-57f669b9f90e] FOREIGN KEY
	(
		[DealerConfigurationId] 
	)
	REFERENCES [dbo].[DealerConfiguration]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[Dealer] 
	ADD CONSTRAINT [FK_Dealer_Region_b3e71548-b170-4cbd-82aa-e16a72d87fef] FOREIGN KEY
	(
		[RegionId] 
	)
	REFERENCES [dbo].[Region]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[Dealer] 
	ADD CONSTRAINT [FK_Dealer_DealerFeatureSubscription_7b89c9c6-114a-4b21-8ba1-610460d0c9c0] FOREIGN KEY
	(
		[DealerFeatureSubscriptionId] 
	)
	REFERENCES [dbo].[DealerFeatureSubscription]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[DealerDriver] 
	ADD CONSTRAINT [FK_DealerDriver_Card_bb5ea20c-9705-4761-bf40-dd6d747d1e58] FOREIGN KEY
	(
		[CardId] 
	)
	REFERENCES [dbo].[Card]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[DealerDriver] 
	ADD CONSTRAINT [FK_DealerDriver_GOUser_83c5005f-f0da-41b1-8059-200ecf749af3] FOREIGN KEY
	(
		[GOUserId] 
	)
	REFERENCES [GOSecurity].[GOUser]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[Department] 
	ADD CONSTRAINT [FK_Department_Customer_b5a4e285-595a-443c-a1d9-8e974dc009af] FOREIGN KEY
	(
		[CustomerId] 
	)
	REFERENCES [dbo].[Customer]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[Department] 
	ADD CONSTRAINT [FK_Department_Site_30827fba-269f-47df-90d7-fc289588dfed] FOREIGN KEY
	(
		[SiteId] 
	)
	REFERENCES [dbo].[Site]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[Department] 
	ADD CONSTRAINT [FK_Department_DepartmentHourSettings_7f12cba3-7ebc-452f-a596-1d85dd11a406] FOREIGN KEY
	(
		[DepartmentHourSettingsId] 
	)
	REFERENCES [dbo].[DepartmentHourSettings]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[DepartmentChecklist] 
	ADD CONSTRAINT [FK_DepartmentChecklist_Model_8a6cb737-c954-4aed-a5e7-059040f4ac2b] FOREIGN KEY
	(
		[ModelId] 
	)
	REFERENCES [dbo].[Model]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[DepartmentChecklist] 
	ADD CONSTRAINT [FK_DepartmentChecklist_Department_91ede9ad-af01-4580-888f-827f6939a72e] FOREIGN KEY
	(
		[DepartmentId] 
	)
	REFERENCES [dbo].[Department]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[DepartmentVehicleMasterCardAccess] 
	ADD CONSTRAINT [FK_DepartmentVehicleMasterCardAccess_Card_96bb684a-30d5-447f-9a1b-a71788696cc8] FOREIGN KEY
	(
		[CardId] 
	)
	REFERENCES [dbo].[Card]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[DepartmentVehicleMasterCardAccess] 
	ADD CONSTRAINT [FK_DepartmentVehicleMasterCardAccess_Department_5e194f32-eecf-406a-8134-6c9391d92a0a] FOREIGN KEY
	(
		[DepartmentId] 
	)
	REFERENCES [dbo].[Department]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[DepartmentVehicleNormalCardAccess] 
	ADD CONSTRAINT [FK_DepartmentVehicleNormalCardAccess_Card_f4bcec39-54ef-433b-a9dc-d93f0eb1e2ef] FOREIGN KEY
	(
		[CardId] 
	)
	REFERENCES [dbo].[Card]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[DepartmentVehicleNormalCardAccess] 
	ADD CONSTRAINT [FK_DepartmentVehicleNormalCardAccess_Department_a9cc9629-123e-4fea-a18f-c888f1e64157] FOREIGN KEY
	(
		[DepartmentId] 
	)
	REFERENCES [dbo].[Department]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[DepartmentVehicleNormalCardAccess] 
	ADD CONSTRAINT [FK_DepartmentVehicleNormalCardAccess_Permission_767fc3e3-5223-45d2-80b7-42781cb88e90] FOREIGN KEY
	(
		[PermissionId] 
	)
	REFERENCES [dbo].[Permission]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[Driver] 
	ADD CONSTRAINT [FK_Driver_Card_add32482-cb44-4a20-8c56-64105c65023e] FOREIGN KEY
	(
		[CardDetailsId] 
	)
	REFERENCES [dbo].[Card]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[Driver] 
	ADD CONSTRAINT [FK_Driver_LicenceDetail_ce6e01c7-868d-49d2-b83b-18aad66da1da] FOREIGN KEY
	(
		[LicenceDetailId] 
	)
	REFERENCES [dbo].[LicenceDetail]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[Driver] 
	ADD CONSTRAINT [FK_Driver_Customer_0bae8c0f-6efa-4508-a371-306f7f0aae3a] FOREIGN KEY
	(
		[CustomerId] 
	)
	REFERENCES [dbo].[Customer]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[Driver] 
	ADD CONSTRAINT [FK_Driver_Department_9abba562-525f-4d38-9068-c5de5d4b7544] FOREIGN KEY
	(
		[DepartmentId] 
	)
	REFERENCES [dbo].[Department]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[Driver] 
	ADD CONSTRAINT [FK_Driver_Site_6afd86f4-f67f-42ce-8c3a-4ef3538d882c] FOREIGN KEY
	(
		[SiteId] 
	)
	REFERENCES [dbo].[Site]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[EmailGroups] 
	ADD CONSTRAINT [FK_EmailGroups_Customer_8e3cf5bd-2df4-48cd-8659-0f440e30d42c] FOREIGN KEY
	(
		[CustomerId] 
	)
	REFERENCES [dbo].[Customer]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[EmailGroupsToPerson] 
	ADD CONSTRAINT [FK_EmailGroupsToPerson_EmailGroups_4f16e0b8-0f6d-4ee0-9c07-40b84f31466e] FOREIGN KEY
	(
		[EmailGroupsId] 
	)
	REFERENCES [dbo].[EmailGroups]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[EmailGroupsToPerson] 
	ADD CONSTRAINT [FK_EmailGroupsToPerson_Person_d4b2c672-0ea9-4a5b-8199-ab43f5686c59] FOREIGN KEY
	(
		[PersonId] 
	)
	REFERENCES [dbo].[Person]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[FloorPlan] 
	ADD CONSTRAINT [FK_FloorPlan_Site_f25468dc-ac20-4adb-b804-efbf00a40a06] FOREIGN KEY
	(
		[SiteId] 
	)
	REFERENCES [dbo].[Site]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[FloorZones] 
	ADD CONSTRAINT [FK_FloorZones_FloorPlan_5205a52c-84c2-44f0-b46b-e9280d31fec5] FOREIGN KEY
	(
		[FloorPlanId] 
	)
	REFERENCES [dbo].[FloorPlan]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[GOUserDepartment] 
	ADD CONSTRAINT [FK_GOUserDepartment_GOUser_557ad852-8e06-419f-9476-5a6accfa8761] FOREIGN KEY
	(
		[GOUserId] 
	)
	REFERENCES [GOSecurity].[GOUser]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[GOUserDepartment] 
	ADD CONSTRAINT [FK_GOUserDepartment_Department_035ab3eb-d1b8-479d-a7de-50013b1b9891] FOREIGN KEY
	(
		[DepartmentId] 
	)
	REFERENCES [dbo].[Department]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[GoUserToCustomer] 
	ADD CONSTRAINT [FK_GoUserToCustomer_GOUser_a529d975-6f34-4443-bf79-833161143380] FOREIGN KEY
	(
		[GOUserId] 
	)
	REFERENCES [GOSecurity].[GOUser]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[GoUserToCustomer] 
	ADD CONSTRAINT [FK_GoUserToCustomer_Customer_bbea0512-c9e8-46b1-a346-658c76a7bf8a] FOREIGN KEY
	(
		[CustomerId] 
	)
	REFERENCES [dbo].[Customer]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[GPSHistory] 
	ADD CONSTRAINT [FK_GPSHistory_Session_31d8ceaf-c7ae-43d0-8fbb-2412e53e1fc3] FOREIGN KEY
	(
		[SessionId] 
	)
	REFERENCES [dbo].[Session]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[Impact] 
	ADD CONSTRAINT [FK_Impact_Session_3c09f089-7633-42f6-8cd8-a2631a1a73d0] FOREIGN KEY
	(
		[SessionId] 
	)
	REFERENCES [dbo].[Session]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[LicenseByModel] 
	ADD CONSTRAINT [FK_LicenseByModel_Driver_27585d0e-86d1-4f90-a63b-0e41318a0604] FOREIGN KEY
	(
		[DriverId] 
	)
	REFERENCES [dbo].[Driver]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[LicenseByModel] 
	ADD CONSTRAINT [FK_LicenseByModel_Model_5c036e05-6006-4c25-bd90-536ef3d121c0] FOREIGN KEY
	(
		[ModelId] 
	)
	REFERENCES [dbo].[Model]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[MessageHistory] 
	ADD CONSTRAINT [FK_MessageHistory_GOUser_beac2511-01e3-4577-989d-980cf8cb7424] FOREIGN KEY
	(
		[GOUserId] 
	)
	REFERENCES [GOSecurity].[GOUser]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[MessageHistory] 
	ADD CONSTRAINT [FK_MessageHistory_Vehicle_15f1cd39-737c-4082-94b9-a3555d05f477] FOREIGN KEY
	(
		[VehicleId] 
	)
	REFERENCES [dbo].[Vehicle]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[Model] 
	ADD CONSTRAINT [FK_Model_Dealer_26b3b630-a609-4876-92b4-48e593a0c632] FOREIGN KEY
	(
		[DealerId] 
	)
	REFERENCES [dbo].[Dealer]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[ModelVehicleMasterCardAccess] 
	ADD CONSTRAINT [FK_ModelVehicleMasterCardAccess_Model_f8ad7ba2-174d-4af0-9ba1-73fe3f8f90af] FOREIGN KEY
	(
		[ModelId] 
	)
	REFERENCES [dbo].[Model]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[ModelVehicleMasterCardAccess] 
	ADD CONSTRAINT [FK_ModelVehicleMasterCardAccess_Card_72dadd97-2049-4357-9f16-7f4115564ffc] FOREIGN KEY
	(
		[CardId] 
	)
	REFERENCES [dbo].[Card]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[ModelVehicleNormalCardAccess] 
	ADD CONSTRAINT [FK_ModelVehicleNormalCardAccess_Permission_e77f605a-2fe9-44d7-90c7-ae2c2f5476c2] FOREIGN KEY
	(
		[PermissionId] 
	)
	REFERENCES [dbo].[Permission]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[ModelVehicleNormalCardAccess] 
	ADD CONSTRAINT [FK_ModelVehicleNormalCardAccess_Card_113bdb29-b514-48fc-9ba6-d812210db702] FOREIGN KEY
	(
		[CardId] 
	)
	REFERENCES [dbo].[Card]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[ModelVehicleNormalCardAccess] 
	ADD CONSTRAINT [FK_ModelVehicleNormalCardAccess_Department_1cc878fb-01a2-404b-b7f9-bb781408da35] FOREIGN KEY
	(
		[DepartmentId] 
	)
	REFERENCES [dbo].[Department]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[ModelVehicleNormalCardAccess] 
	ADD CONSTRAINT [FK_ModelVehicleNormalCardAccess_Model_2e12c856-21d5-4673-ab63-cf1730313a32] FOREIGN KEY
	(
		[ModelId] 
	)
	REFERENCES [dbo].[Model]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[Module] 
	ADD CONSTRAINT [FK_Module_Dealer_12077abc-926a-4a3e-a4f1-72d99d2e7da5] FOREIGN KEY
	(
		[DealerId] 
	)
	REFERENCES [dbo].[Dealer]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[ModuleHistory] 
	ADD CONSTRAINT [FK_ModuleHistory_Module_a6043676-1d09-44f6-ba2b-b0a7db5f4d72] FOREIGN KEY
	(
		[ModuleId] 
	)
	REFERENCES [dbo].[Module]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[ModuleHistory] 
	ADD CONSTRAINT [FK_ModuleHistory_Vehicle_ae36977d-dee8-4010-bfa5-4b401ee09d69] FOREIGN KEY
	(
		[VehicleId] 
	)
	REFERENCES [dbo].[Vehicle]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[NetworkSettings] 
	ADD CONSTRAINT [FK_NetworkSettings_Vehicle_e7c02129-32fb-4ec8-86e7-810425713227] FOREIGN KEY
	(
		[VehicleId] 
	)
	REFERENCES [dbo].[Vehicle]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[OnDemandSession] 
	ADD CONSTRAINT [FK_OnDemandSession_Driver_1f97e66e-7e51-453f-b45e-64e3190c7eeb] FOREIGN KEY
	(
		[DriverId] 
	)
	REFERENCES [dbo].[Driver]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[OnDemandSession] 
	ADD CONSTRAINT [FK_OnDemandSession_Vehicle_050ccc36-3ef7-459c-8c1b-e37a0fad7d66] FOREIGN KEY
	(
		[VehicleId] 
	)
	REFERENCES [dbo].[Vehicle]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[OnDemandSettings] 
	ADD CONSTRAINT [FK_OnDemandSettings_Vehicle_85d81552-2e23-4319-9bcb-0da2cb720753] FOREIGN KEY
	(
		[VehicleId] 
	)
	REFERENCES [dbo].[Vehicle]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[PedestrianDetectionHistory] 
	ADD CONSTRAINT [FK_PedestrianDetectionHistory_Vehicle_2bf6b6e5-e101-4c79-bd65-dd9855a94ae6] FOREIGN KEY
	(
		[VehicleId] 
	)
	REFERENCES [dbo].[Vehicle]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[PedestrianDetectionHistory] 
	ADD CONSTRAINT [FK_PedestrianDetectionHistory_Driver_e73e21d7-2254-4402-8803-91a7fc28f61e] FOREIGN KEY
	(
		[DriverId] 
	)
	REFERENCES [dbo].[Driver]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[Person] 
	ADD CONSTRAINT [FK_Person_WebsiteUser_4fd2de49-f48b-4d13-9068-6077abf98105] FOREIGN KEY
	(
		[WebsiteUserId] 
	)
	REFERENCES [dbo].[WebsiteUser]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[Person] 
	ADD CONSTRAINT [FK_Person_Customer_d6604ce8-6110-46ab-af1a-8d9f73806d8e] FOREIGN KEY
	(
		[CustomerId] 
	)
	REFERENCES [dbo].[Customer]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[Person] 
	ADD CONSTRAINT [FK_Person_PersonChecklistLanguageSettings_32094fa8-8a68-4012-96e7-31eb835d5174] FOREIGN KEY
	(
		[PersonChecklistLanguageSettingsId] 
	)
	REFERENCES [dbo].[PersonChecklistLanguageSettings]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[Person] 
	ADD CONSTRAINT [FK_Person_Driver_684bfe93-1ca3-4664-9758-40587890d430] FOREIGN KEY
	(
		[DriverId] 
	)
	REFERENCES [dbo].[Driver]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[Person] 
	ADD CONSTRAINT [FK_Person_GOUser_1962f85d-894f-45b5-ad66-6f9a198f7964] FOREIGN KEY
	(
		[GOUserId] 
	)
	REFERENCES [GOSecurity].[GOUser]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[Person] 
	ADD CONSTRAINT [FK_Person_AccessGroup_8461be98-6271-4191-be44-94d212cc3409] FOREIGN KEY
	(
		[AccessGroupId] 
	)
	REFERENCES [dbo].[AccessGroup]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[Person] 
	ADD CONSTRAINT [FK_Person_Site_10206d84-788a-441c-951d-5d0d2a483e54] FOREIGN KEY
	(
		[SiteId] 
	)
	REFERENCES [dbo].[Site]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[Person] 
	ADD CONSTRAINT [FK_Person_Department_59df9dec-53b5-476e-a098-db5463a32445] FOREIGN KEY
	(
		[DepartmentId] 
	)
	REFERENCES [dbo].[Department]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[PersonAllocation] 
	ADD CONSTRAINT [FK_PersonAllocation_Person_d4d5769f-ec92-4ded-b0e6-4fdd388e645b] FOREIGN KEY
	(
		[PersonId] 
	)
	REFERENCES [dbo].[Person]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[PersonAllocation] 
	ADD CONSTRAINT [FK_PersonAllocation_Site_e76ccf16-2506-4625-957d-47da678adcf3] FOREIGN KEY
	(
		[SiteId] 
	)
	REFERENCES [dbo].[Site]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[PersonAllocation] 
	ADD CONSTRAINT [FK_PersonAllocation_Department_47dd94dc-3be4-423b-9d64-143f11360b93] FOREIGN KEY
	(
		[DepartmentId] 
	)
	REFERENCES [dbo].[Department]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[PerVehicleMasterCardAccess] 
	ADD CONSTRAINT [FK_PerVehicleMasterCardAccess_SiteVehicleNormalCardAccess_c1c8530d-00b6-4815-8567-9dcc5c48ba8c] FOREIGN KEY
	(
		[SiteVehicleNormalCardAccessId] 
	)
	REFERENCES [dbo].[SiteVehicleNormalCardAccess]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[PerVehicleMasterCardAccess] 
	ADD CONSTRAINT [FK_PerVehicleMasterCardAccess_DepartmentVehicleMasterCardAccess_e8fbc10c-95cc-4ec0-92b5-59815bc34045] FOREIGN KEY
	(
		[DepartmentVehicleMasterCardAccessId] 
	)
	REFERENCES [dbo].[DepartmentVehicleMasterCardAccess]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[PerVehicleMasterCardAccess] 
	ADD CONSTRAINT [FK_PerVehicleMasterCardAccess_ModelVehicleMasterCardAccess_9443143e-a6b5-487d-adda-545d2df1239c] FOREIGN KEY
	(
		[ModelVehicleMasterCardAccessId] 
	)
	REFERENCES [dbo].[ModelVehicleMasterCardAccess]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[PerVehicleMasterCardAccess] 
	ADD CONSTRAINT [FK_PerVehicleMasterCardAccess_Card_84bd9059-e42a-4d49-925b-9d4efa0c9aa3] FOREIGN KEY
	(
		[CardId] 
	)
	REFERENCES [dbo].[Card]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[PerVehicleMasterCardAccess] 
	ADD CONSTRAINT [FK_PerVehicleMasterCardAccess_Vehicle_a1d56619-796c-405c-8238-1379d05e59c7] FOREIGN KEY
	(
		[VehicleId] 
	)
	REFERENCES [dbo].[Vehicle]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[PerVehicleNormalCardAccess] 
	ADD CONSTRAINT [FK_PerVehicleNormalCardAccess_Permission_cbdbffbb-caff-433b-b584-9e3000ae5ec0] FOREIGN KEY
	(
		[PermissionId] 
	)
	REFERENCES [dbo].[Permission]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[PerVehicleNormalCardAccess] 
	ADD CONSTRAINT [FK_PerVehicleNormalCardAccess_Vehicle_4fefca5f-d82a-4485-b455-f83e2a1e2595] FOREIGN KEY
	(
		[VehicleId] 
	)
	REFERENCES [dbo].[Vehicle]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[PerVehicleNormalCardAccess] 
	ADD CONSTRAINT [FK_PerVehicleNormalCardAccess_Card_06a193c8-0acc-4cfb-b83f-96620f710541] FOREIGN KEY
	(
		[CardId] 
	)
	REFERENCES [dbo].[Card]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[PreOperationalChecklist] 
	ADD CONSTRAINT [FK_PreOperationalChecklist_DepartmentChecklist_f1133524-b99d-41aa-9687-1ab2742487b6] FOREIGN KEY
	(
		[SiteChecklistId] 
	)
	REFERENCES [dbo].[DepartmentChecklist]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[PSTATDetails] 
	ADD CONSTRAINT [FK_PSTATDetails_Session_47719aab-8282-477f-b2ac-cc51c41806d6] FOREIGN KEY
	(
		[SessionId] 
	)
	REFERENCES [dbo].[Session]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[PSTATDetails] 
	ADD CONSTRAINT [FK_PSTATDetails_IOFIELD_e2c27695-5273-4c05-b2d2-bda81860ad37] FOREIGN KEY
	(
		[IOFIELDId] 
	)
	REFERENCES [dbo].[IOFIELD]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[ReportSubscription] 
	ADD CONSTRAINT [FK_ReportSubscription_ReportType_2984b96f-5165-40f2-bfb9-77f39f2f89b7] FOREIGN KEY
	(
		[ReportTypeId] 
	)
	REFERENCES [dbo].[ReportType]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[ReportSubscription] 
	ADD CONSTRAINT [FK_ReportSubscription_Site_a080ac82-8430-444f-a3c6-fcca4a0eeecf] FOREIGN KEY
	(
		[SiteId] 
	)
	REFERENCES [dbo].[Site]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[ReportSubscription] 
	ADD CONSTRAINT [FK_ReportSubscription_Person_d3017f7d-9c7b-4374-8405-293dd8d99a8e] FOREIGN KEY
	(
		[PersonId] 
	)
	REFERENCES [dbo].[Person]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[ReportSubscription] 
	ADD CONSTRAINT [FK_ReportSubscription_Customer_7b44d771-9a9e-4f30-9d50-6e9e675fe59c] FOREIGN KEY
	(
		[CustomerId] 
	)
	REFERENCES [dbo].[Customer]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[ReportSubscription] 
	ADD CONSTRAINT [FK_ReportSubscription_Department_434af733-95ce-4afc-a4c7-8f6354a56a06] FOREIGN KEY
	(
		[DepartmentId] 
	)
	REFERENCES [dbo].[Department]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[ReportSubscription] 
	ADD CONSTRAINT [FK_ReportSubscription_GOUser_2204a73f-165e-421c-beb5-623aa425ab90] FOREIGN KEY
	(
		[GOUserId] 
	)
	REFERENCES [GOSecurity].[GOUser]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[ReportSubscription] 
	ADD CONSTRAINT [FK_ReportSubscription_EmailGroups_cb9a412b-b34b-459d-ab48-895f2a687e75] FOREIGN KEY
	(
		[EmailGroupsId] 
	)
	REFERENCES [dbo].[EmailGroups]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[Session] 
	ADD CONSTRAINT [FK_Session_Driver_37a08ff5-9693-4fe1-9971-75ba27fc1d17] FOREIGN KEY
	(
		[DriverId] 
	)
	REFERENCES [dbo].[Driver]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[Session] 
	ADD CONSTRAINT [FK_Session_Vehicle_0c952c6c-6c41-4e73-b0ec-7a2d990b9d30] FOREIGN KEY
	(
		[VehicleId] 
	)
	REFERENCES [dbo].[Vehicle]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[SessionDetails] 
	ADD CONSTRAINT [FK_SessionDetails_Session_698a015b-5efd-46fc-8427-0218f14a0c89] FOREIGN KEY
	(
		[SessionId] 
	)
	REFERENCES [dbo].[Session]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[SessionDetails] 
	ADD CONSTRAINT [FK_SessionDetails_IOFIELD_bd1aa838-0cde-4a0f-bf4f-0dd222c7d644] FOREIGN KEY
	(
		[IOFIELDId] 
	)
	REFERENCES [dbo].[IOFIELD]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[Site] 
	ADD CONSTRAINT [FK_Site_Timezone_fd57de19-b305-458c-bc15-0d225d924910] FOREIGN KEY
	(
		[TimezoneId] 
	)
	REFERENCES [dbo].[Timezone]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[Site] 
	ADD CONSTRAINT [FK_Site_Customer_8bdb6864-594d-4e00-8104-0e2531cb1031] FOREIGN KEY
	(
		[CustomerId] 
	)
	REFERENCES [dbo].[Customer]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[SiteVehicleMasterCardAccess] 
	ADD CONSTRAINT [FK_SiteVehicleMasterCardAccess_Site_fc03d69a-f2b7-415e-99c4-eafb4f465e2c] FOREIGN KEY
	(
		[SiteId] 
	)
	REFERENCES [dbo].[Site]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[SiteVehicleMasterCardAccess] 
	ADD CONSTRAINT [FK_SiteVehicleMasterCardAccess_Card_a89a4d47-6219-4c70-9ec2-7b508a758751] FOREIGN KEY
	(
		[CardId] 
	)
	REFERENCES [dbo].[Card]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[SiteVehicleNormalCardAccess] 
	ADD CONSTRAINT [FK_SiteVehicleNormalCardAccess_Permission_99b73375-b183-4b6f-bbf7-0c86070ba01d] FOREIGN KEY
	(
		[PermissionId] 
	)
	REFERENCES [dbo].[Permission]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[SiteVehicleNormalCardAccess] 
	ADD CONSTRAINT [FK_SiteVehicleNormalCardAccess_Site_caca5f6a-cde1-4005-9c9a-00b82a96fa1a] FOREIGN KEY
	(
		[SiteId] 
	)
	REFERENCES [dbo].[Site]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[SiteVehicleNormalCardAccess] 
	ADD CONSTRAINT [FK_SiteVehicleNormalCardAccess_Card_803e6c00-83df-46b9-9dff-5edfc0d2d950] FOREIGN KEY
	(
		[CardId] 
	)
	REFERENCES [dbo].[Card]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[SlamcoreDevice] 
	ADD CONSTRAINT [FK_SlamcoreDevice_SlamcoreAwareAuthenticationDetails_1e264a0a-e607-4475-a821-b7c75a8c9930] FOREIGN KEY
	(
		[SlamcoreAwareAuthenticationDetailsId] 
	)
	REFERENCES [dbo].[SlamcoreAwareAuthenticationDetails]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[SlamcoreDevice] 
	ADD CONSTRAINT [FK_SlamcoreDevice_SlamcoreAPIKey_3b46f4a6-8511-48a6-9a7a-a2627bf61c88] FOREIGN KEY
	(
		[SlamcoreAPIKeyId] 
	)
	REFERENCES [dbo].[SlamcoreAPIKey]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[SlamcoreDevice] 
	ADD CONSTRAINT [FK_SlamcoreDevice_Customer_dcb546ad-fb93-4cf4-86eb-6d8788186479] FOREIGN KEY
	(
		[CustomerId] 
	)
	REFERENCES [dbo].[Customer]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[SlamcoreDevice] 
	ADD CONSTRAINT [FK_SlamcoreDevice_Vehicle_37d2025a-1fd1-4e54-b3f9-c727ebec4113] FOREIGN KEY
	(
		[VehicleId] 
	)
	REFERENCES [dbo].[Vehicle]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[SlamcoreDeviceHistory] 
	ADD CONSTRAINT [FK_SlamcoreDeviceHistory_Vehicle_d55cd36f-bea3-4298-a010-04de6f64a8fd] FOREIGN KEY
	(
		[VehicleId] 
	)
	REFERENCES [dbo].[Vehicle]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[SlamcoreDeviceHistory] 
	ADD CONSTRAINT [FK_SlamcoreDeviceHistory_SlamcoreDevice_1199b949-2b1a-4936-bb1a-54a5c7844774] FOREIGN KEY
	(
		[SlamcoreDeviceId] 
	)
	REFERENCES [dbo].[SlamcoreDevice]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[UpdateFirmwareRequest] 
	ADD CONSTRAINT [FK_UpdateFirmwareRequest_Firmware_5d770b28-d39c-4ec7-a7ac-58bb60fd51b4] FOREIGN KEY
	(
		[FirmwareId] 
	)
	REFERENCES [dbo].[Firmware]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[Vehicle] 
	ADD CONSTRAINT [FK_Vehicle_Firmware_382a1244-8e2e-45ef-80b0-f24b6904e1ab] FOREIGN KEY
	(
		[FirmwareId] 
	)
	REFERENCES [dbo].[Firmware]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[Vehicle] 
	ADD CONSTRAINT [FK_Vehicle_Model_b513419b-33cb-44e7-a3c9-5970374d4f6d] FOREIGN KEY
	(
		[ModelId] 
	)
	REFERENCES [dbo].[Model]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[Vehicle] 
	ADD CONSTRAINT [FK_Vehicle_Driver_4012b1cf-c7c2-464e-be8d-4e93e1b61601] FOREIGN KEY
	(
		[DriverId] 
	)
	REFERENCES [dbo].[Driver]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[Vehicle] 
	ADD CONSTRAINT [FK_Vehicle_Inspection_c735d01b-3071-432a-8196-6d13d22aeff6] FOREIGN KEY
	(
		[InspectionId] 
	)
	REFERENCES [dbo].[Inspection]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[Vehicle] 
	ADD CONSTRAINT [FK_Vehicle_DepartmentChecklist_e3ec59b7-a5f3-49e5-bd69-0a086088cd6d] FOREIGN KEY
	(
		[DepartmentChecklistId] 
	)
	REFERENCES [dbo].[DepartmentChecklist]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[Vehicle] 
	ADD CONSTRAINT [FK_Vehicle_Department_3c4946d0-538b-451c-a73c-28e9ef14b30c] FOREIGN KEY
	(
		[DepartmentId] 
	)
	REFERENCES [dbo].[Department]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[Vehicle] 
	ADD CONSTRAINT [FK_Vehicle_VehicleOtherSettings_7f813c68-3701-4fea-80b2-3757fd508841] FOREIGN KEY
	(
		[VehicleOtherSettingsId] 
	)
	REFERENCES [dbo].[VehicleOtherSettings]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[Vehicle] 
	ADD CONSTRAINT [FK_Vehicle_Person_bbb9efac-1133-4e46-83e3-57c010d2299b] FOREIGN KEY
	(
		[PersonId] 
	)
	REFERENCES [dbo].[Person]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[Vehicle] 
	ADD CONSTRAINT [FK_Vehicle_ServiceSettings_65009433-a71a-4834-a53c-bda5c46d4470] FOREIGN KEY
	(
		[ServiceSettingsId] 
	)
	REFERENCES [dbo].[ServiceSettings]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[Vehicle] 
	ADD CONSTRAINT [FK_Vehicle_ChecklistSettings_a7a23cb4-5831-4cff-8491-6b0bd3dfbbc7] FOREIGN KEY
	(
		[ChecklistSettingsId] 
	)
	REFERENCES [dbo].[ChecklistSettings]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[Vehicle] 
	ADD CONSTRAINT [FK_Vehicle_Canrule_23a4e1c6-d0e3-4d51-b5b8-2f1459d27261] FOREIGN KEY
	(
		[CanruleId] 
	)
	REFERENCES [dbo].[Canrule]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[Vehicle] 
	ADD CONSTRAINT [FK_Vehicle_Site_d0990232-424f-4e20-b881-78f753d2415e] FOREIGN KEY
	(
		[SiteId] 
	)
	REFERENCES [dbo].[Site]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[Vehicle] 
	ADD CONSTRAINT [FK_Vehicle_Module_7b54e02c-125e-44ab-9d06-4520b696a257] FOREIGN KEY
	(
		[ModuleId1] 
	)
	REFERENCES [dbo].[Module]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[Vehicle] 
	ADD CONSTRAINT [FK_Vehicle_Customer_5ff9dda7-9cfa-4c6a-ab75-17f9076b3257] FOREIGN KEY
	(
		[CustomerId] 
	)
	REFERENCES [dbo].[Customer]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[VehicleAlertSubscription] 
	ADD CONSTRAINT [FK_VehicleAlertSubscription_Vehicle_24adaa75-8ce9-403c-9db2-d05a535f1436] FOREIGN KEY
	(
		[VehicleId] 
	)
	REFERENCES [dbo].[Vehicle]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[VehicleAlertSubscription] 
	ADD CONSTRAINT [FK_VehicleAlertSubscription_AlertSubscription_f4dcda79-1d97-4062-9121-be42b007cff0] FOREIGN KEY
	(
		[AlertSubscriptionId] 
	)
	REFERENCES [dbo].[AlertSubscription]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[VehicleBroadcastMessage] 
	ADD CONSTRAINT [FK_VehicleBroadcastMessage_Vehicle_52b054a2-7951-40e4-a1bd-c4123e3d7f2d] FOREIGN KEY
	(
		[VehicleId] 
	)
	REFERENCES [dbo].[Vehicle]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[VehicleBroadcastMessage] 
	ADD CONSTRAINT [FK_VehicleBroadcastMessage_BroadcastMessage_f979682b-8d7b-4702-b0de-5e6356217b53] FOREIGN KEY
	(
		[BroadcastMessageId] 
	)
	REFERENCES [dbo].[BroadcastMessage]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[VehicleDiagnostic] 
	ADD CONSTRAINT [FK_VehicleDiagnostic_Vehicle_a8bf44c6-76ed-4271-b9f6-390a3d40d111] FOREIGN KEY
	(
		[VehicleId] 
	)
	REFERENCES [dbo].[Vehicle]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[VehicleGPS] 
	ADD CONSTRAINT [FK_VehicleGPS_Session_0eae986f-e18d-41af-bc5b-9b772038cb95] FOREIGN KEY
	(
		[SessionId] 
	)
	REFERENCES [dbo].[Session]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[VehicleGPS] 
	ADD CONSTRAINT [FK_VehicleGPS_Vehicle_b21bcb91-f405-4211-ba06-09b42428841a] FOREIGN KEY
	(
		[VehicleId] 
	)
	REFERENCES [dbo].[Vehicle]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[VehicleHireDehireHistory] 
	ADD CONSTRAINT [FK_VehicleHireDehireHistory_Department_cca8149d-484b-4c96-beee-65624bcd80a2] FOREIGN KEY
	(
		[DepartmentId] 
	)
	REFERENCES [dbo].[Department]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[VehicleHireDehireHistory] 
	ADD CONSTRAINT [FK_VehicleHireDehireHistory_Vehicle_c5721cdc-6c53-4f5c-91bf-c2b21b004b02] FOREIGN KEY
	(
		[VehicleId] 
	)
	REFERENCES [dbo].[Vehicle]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[VehicleLockout] 
	ADD CONSTRAINT [FK_VehicleLockout_Session_b9f7dae6-ea2c-48a3-91e5-943b3f4574a6] FOREIGN KEY
	(
		[SessionId] 
	)
	REFERENCES [dbo].[Session]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[VehicleLockout] 
	ADD CONSTRAINT [FK_VehicleLockout_Driver_f961ab06-b7cd-4856-be03-9e07b9899810] FOREIGN KEY
	(
		[DriverId] 
	)
	REFERENCES [dbo].[Driver]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[VehicleSessionlessImpact] 
	ADD CONSTRAINT [FK_VehicleSessionlessImpact_Vehicle_e0be9c05-1e2d-4311-a9de-4e4b58b4da49] FOREIGN KEY
	(
		[VehicleId] 
	)
	REFERENCES [dbo].[Vehicle]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[VehicleSlamcoreLocationHistory] 
	ADD CONSTRAINT [FK_VehicleSlamcoreLocationHistory_SlamcoreDevice_f86d947d-6332-4878-bcc8-27418568c1d2] FOREIGN KEY
	(
		[SlamcoreDeviceId] 
	)
	REFERENCES [dbo].[SlamcoreDevice]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[VORSettingHistory] 
	ADD CONSTRAINT [FK_VORSettingHistory_Person_b10bdfa8-ac8a-4181-b681-28ab40c1d43a] FOREIGN KEY
	(
		[PersonId] 
	)
	REFERENCES [dbo].[Person]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[VORSettingHistory] 
	ADD CONSTRAINT [FK_VORSettingHistory_Vehicle_8b40f9df-b6ec-48ad-983c-7d334ef15194] FOREIGN KEY
	(
		[VehicleId] 
	)
	REFERENCES [dbo].[Vehicle]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[WebsiteRole] 
	ADD CONSTRAINT [FK_WebsiteRole_WebsiteUser_9df62c9c-e694-43f7-bd29-b6e453c8f0c0] FOREIGN KEY
	(
		[WebsiteUserId] 
	)
	REFERENCES [dbo].[WebsiteUser]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[ZoneCoordinates] 
	ADD CONSTRAINT [FK_ZoneCoordinates_FloorZones_cbfbf708-a6cd-44ef-8a65-225a3cacff0a] FOREIGN KEY
	(
		[FloorZonesId] 
	)
	REFERENCES [dbo].[FloorZones]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [GOChangeTracking].[CustomerAudit] 
	ADD CONSTRAINT [FK_CustomerAudit_Revision_5b690619-de0b-498d-8778-3244bfb14064] FOREIGN KEY
	(
		[fkRevisionDeleted] 
	)
	REFERENCES [GOChangeTracking].[Revision]
	(
		[RevisionId] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [GOChangeTracking].[CustomerAudit] 
	ADD CONSTRAINT [FK_CustomerAudit_Revision_27c625b5-bac4-4c3a-b7f6-9f29d7585ddc] FOREIGN KEY
	(
		[fkRevisionLastModified] 
	)
	REFERENCES [GOChangeTracking].[Revision]
	(
		[RevisionId] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [GOChangeTracking].[CustomerAudit] 
	ADD CONSTRAINT [FK_CustomerAudit_Revision_ed9b298d-8b74-4b94-8efe-0fef553b5975] FOREIGN KEY
	(
		[fkRevisionCreated] 
	)
	REFERENCES [GOChangeTracking].[Revision]
	(
		[RevisionId] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [GOChangeTracking].[CustomerAudit] 
	ADD CONSTRAINT [FK_CustomerAudit_GOUser_f987a35c-cb3a-4d02-8971-b13873220bc7] FOREIGN KEY
	(
		[CreatedBy] 
	)
	REFERENCES [GOSecurity].[GOUser]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [GOChangeTracking].[CustomerAudit] 
	ADD CONSTRAINT [FK_CustomerAudit_GOUser_9b07c494-2d6e-4889-a725-91ab76884616] FOREIGN KEY
	(
		[LastModifiedBy] 
	)
	REFERENCES [GOSecurity].[GOUser]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [GOChangeTracking].[CustomerAudit] 
	ADD CONSTRAINT [FK_CustomerAudit_GOUser_7499b596-6e64-4de2-a677-87a3421849f2] FOREIGN KEY
	(
		[DeletedBy] 
	)
	REFERENCES [GOSecurity].[GOUser]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [GOChangeTracking].[Revision] 
	ADD CONSTRAINT [FK_Revision_GOUser_4610bade-d9be-472e-abf2-96e4ef941adc] FOREIGN KEY
	(
		[fkGOUserId] 
	)
	REFERENCES [GOSecurity].[GOUser]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [GOChangeTracking].[Snapshot] 
	ADD CONSTRAINT [FK_Snapshot_Revision_7750d401-9d2a-404c-a602-bc347e061c98] FOREIGN KEY
	(
		[fkRevisionId] 
	)
	REFERENCES [GOChangeTracking].[Revision]
	(
		[RevisionId] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [GOChangeTracking].[Tag] 
	ADD CONSTRAINT [FK_Tag_Revision_8a69cbe1-53dd-4479-86a6-6d87408e9dee] FOREIGN KEY
	(
		[fkRevisionId] 
	)
	REFERENCES [GOChangeTracking].[Revision]
	(
		[RevisionId] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [GOChangeTracking].[Tag] 
	ADD CONSTRAINT [FK_Tag_GOUser_d2f564c6-9ba6-4ea9-8940-d71637a69010] FOREIGN KEY
	(
		[Author] 
	)
	REFERENCES [GOSecurity].[GOUser]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [GOSecurity].[GOGroupRole] 
	ADD CONSTRAINT [FK_GOGroupRole_GOGroup_c05003f3-be63-4063-982e-1a1917b265f2] FOREIGN KEY
	(
		[GOGroupName] 
	)
	REFERENCES [GOSecurity].[GOGroup]
	(
		[Name] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [GOSecurity].[GOUser] 
	ADD CONSTRAINT [FK_GOUser_Dealer_1cd6c7f7-769f-428f-aeaf-c3a64f36c57b] FOREIGN KEY
	(
		[DealerId] 
	)
	REFERENCES [dbo].[Dealer]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [GOSecurity].[GOUserGroup] 
	ADD CONSTRAINT [FK_GOUserGroup_GOUser_4455c39c-b864-472b-adc0-03805c524298] FOREIGN KEY
	(
		[GOUserId] 
	)
	REFERENCES [GOSecurity].[GOUser]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [GOSecurity].[GOUserGroup] 
	ADD CONSTRAINT [FK_GOUserGroup_GOGroup_1c47a1b1-1026-42d4-9b0d-bb9bc4725963] FOREIGN KEY
	(
		[GOGroupName] 
	)
	REFERENCES [GOSecurity].[GOGroup]
	(
		[Name] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [GOSecurity].[GOUserRole] 
	ADD CONSTRAINT [FK_GOUserRole_GOUser_d2f142dd-e0a3-4e52-8033-32a4e87d2052] FOREIGN KEY
	(
		[GOUserId] 
	)
	REFERENCES [GOSecurity].[GOUser]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [ImportExport].[ExportJobStatus] 
	ADD CONSTRAINT [FK_ExportJobStatus_GOUser_d94f3a3f-78f9-4e8f-af88-8cdca6a30a13] FOREIGN KEY
	(
		[GOUserId] 
	)
	REFERENCES [GOSecurity].[GOUser]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [ImportExport].[ImportJobLog] 
	ADD CONSTRAINT [FK_ImportJobLog_ImportJobBatch_f51ab67f-9489-46d0-b2ac-86bc12e33d4b] FOREIGN KEY
	(
		[ImportJobBatchId] 
	)
	REFERENCES [ImportExport].[ImportJobBatch]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [ImportExport].[ImportJobLog] 
	ADD CONSTRAINT [FK_ImportJobLog_ImportJobStatus_41cf4f15-a9e6-47c3-a0a2-c1a6e8b7cfda] FOREIGN KEY
	(
		[ImportJobStatusId] 
	)
	REFERENCES [ImportExport].[ImportJobStatus]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [ImportExport].[ImportJobStatus] 
	ADD CONSTRAINT [FK_ImportJobStatus_ImportJobBatch_3513c1db-1387-463e-b960-7af8036fe707] FOREIGN KEY
	(
		[ImportJobBatchId] 
	)
	REFERENCES [ImportExport].[ImportJobBatch]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
-- ----------------------------------------------------------------------------------------------------------------
-- All foreign Key constraints for entity hierarchies
-- ----------------------------------------------------------------------------------------------------------------
ALTER TABLE [GOSecurity].[GOUser2FA] 
	ADD CONSTRAINT [FK_GOUser2FA_GOUser_TargetPerEntityLink] FOREIGN KEY
	(
		[Id] 
	)
	REFERENCES [GOSecurity].[GOUser]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [GOChangeTracking].[CustomerSnapshot] 
	ADD CONSTRAINT [FK_CustomerSnapshot_Snapshot_TargetPerEntityLink] FOREIGN KEY
	(
		[SnapshotId] 
	)
	REFERENCES [GOChangeTracking].[Snapshot]
	(
		[SnapshotId] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[AllVehicleCalibrationFilter] 
	ADD CONSTRAINT [FK_AllVehicleCalibrationFilter_DashboardFilter_TargetPerEntityLink] FOREIGN KEY
	(
		[Id] 
	)
	REFERENCES [dbo].[DashboardFilter]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[BroadcastMessageHistoryFilter] 
	ADD CONSTRAINT [FK_BroadcastMessageHistoryFilter_DashboardFilter_TargetPerEntityLink] FOREIGN KEY
	(
		[Id] 
	)
	REFERENCES [dbo].[DashboardFilter]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[DashboardFilterMoreFields] 
	ADD CONSTRAINT [FK_DashboardFilterMoreFields_DashboardFilter_TargetPerEntityLink] FOREIGN KEY
	(
		[Id] 
	)
	REFERENCES [dbo].[DashboardFilter]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[DriverAccessAbuseFilter] 
	ADD CONSTRAINT [FK_DriverAccessAbuseFilter_DashboardFilter_TargetPerEntityLink] FOREIGN KEY
	(
		[Id] 
	)
	REFERENCES [dbo].[DashboardFilter]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[EmailSubscriptionReportFilter] 
	ADD CONSTRAINT [FK_EmailSubscriptionReportFilter_DashboardFilter_TargetPerEntityLink] FOREIGN KEY
	(
		[Id] 
	)
	REFERENCES [dbo].[DashboardFilter]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[FeatureSubscriptionsFilter] 
	ADD CONSTRAINT [FK_FeatureSubscriptionsFilter_DashboardFilter_TargetPerEntityLink] FOREIGN KEY
	(
		[Id] 
	)
	REFERENCES [dbo].[DashboardFilter]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[GeneralProductivityReportFilter] 
	ADD CONSTRAINT [FK_GeneralProductivityReportFilter_DashboardFilter_TargetPerEntityLink] FOREIGN KEY
	(
		[Id] 
	)
	REFERENCES [dbo].[DashboardFilter]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[HireDeHireReportFilter] 
	ADD CONSTRAINT [FK_HireDeHireReportFilter_DashboardFilter_TargetPerEntityLink] FOREIGN KEY
	(
		[Id] 
	)
	REFERENCES [dbo].[DashboardFilter]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[ImpactReportFilter] 
	ADD CONSTRAINT [FK_ImpactReportFilter_DashboardFilter_TargetPerEntityLink] FOREIGN KEY
	(
		[Id] 
	)
	REFERENCES [dbo].[DashboardFilter]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[LicenseExpiryReportFilter] 
	ADD CONSTRAINT [FK_LicenseExpiryReportFilter_DashboardFilter_TargetPerEntityLink] FOREIGN KEY
	(
		[Id] 
	)
	REFERENCES [dbo].[DashboardFilter]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[MachineUnlockReportFilter] 
	ADD CONSTRAINT [FK_MachineUnlockReportFilter_DashboardFilter_TargetPerEntityLink] FOREIGN KEY
	(
		[Id] 
	)
	REFERENCES [dbo].[DashboardFilter]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[MainDashboardFilter] 
	ADD CONSTRAINT [FK_MainDashboardFilter_DashboardFilter_TargetPerEntityLink] FOREIGN KEY
	(
		[Id] 
	)
	REFERENCES [dbo].[DashboardFilter]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[OnDemandAuthorisationFilter] 
	ADD CONSTRAINT [FK_OnDemandAuthorisationFilter_DashboardFilter_TargetPerEntityLink] FOREIGN KEY
	(
		[Id] 
	)
	REFERENCES [dbo].[DashboardFilter]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[PedestrianDetectionHistoryFilter] 
	ADD CONSTRAINT [FK_PedestrianDetectionHistoryFilter_DashboardFilter_TargetPerEntityLink] FOREIGN KEY
	(
		[Id] 
	)
	REFERENCES [dbo].[DashboardFilter]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[PreOpReportFilter] 
	ADD CONSTRAINT [FK_PreOpReportFilter_DashboardFilter_TargetPerEntityLink] FOREIGN KEY
	(
		[Id] 
	)
	REFERENCES [dbo].[DashboardFilter]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[ProficiencyReportFilter] 
	ADD CONSTRAINT [FK_ProficiencyReportFilter_DashboardFilter_TargetPerEntityLink] FOREIGN KEY
	(
		[Id] 
	)
	REFERENCES [dbo].[DashboardFilter]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[SlamcoreDeviceFilter] 
	ADD CONSTRAINT [FK_SlamcoreDeviceFilter_DashboardFilter_TargetPerEntityLink] FOREIGN KEY
	(
		[Id] 
	)
	REFERENCES [dbo].[DashboardFilter]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[SynchronizationStatusReportFilter] 
	ADD CONSTRAINT [FK_SynchronizationStatusReportFilter_DashboardFilter_TargetPerEntityLink] FOREIGN KEY
	(
		[Id] 
	)
	REFERENCES [dbo].[DashboardFilter]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [dbo].[VORReportFilter] 
	ADD CONSTRAINT [FK_VORReportFilter_DashboardFilter_TargetPerEntityLink] FOREIGN KEY
	(
		[Id] 
	)
	REFERENCES [dbo].[DashboardFilter]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [ImportExport].[ExportJobStatus] 
	ADD CONSTRAINT [FK_ExportJobStatus_GOTask_TargetPerEntityLink] FOREIGN KEY
	(
		[Id] 
	)
	REFERENCES [dbo].[GOTask]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
ALTER TABLE [ImportExport].[ImportJobStatus] 
	ADD CONSTRAINT [FK_ImportJobStatus_GOTask_TargetPerEntityLink] FOREIGN KEY
	(
		[Id] 
	)
	REFERENCES [dbo].[GOTask]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO 
		
 
-- ----------------------------------------------------------------------------------------------------------------
-- Default Admin user
-- ----------------------------------------------------------------------------------------------------------------
DECLARE @GOUserId uniqueidentifier;
SET @GOUserId = NEWID();

INSERT INTO GOSecurity.GOUser (Id, UserName, FullName, Password, EmailAddress, EmailValidated, EmailChangeValidationInProgress, UserValidated, Blocked, Unregistered) VALUES (@GOUserId, 'Admin', 'Admin', 'e3afed0047b08059d0fada10f400c1e5', '<EMAIL>', 'True', 'False', 'True', 'False', 'False')
INSERT INTO GOSecurity.GOUserRole (GOUserId, GORoleName) VALUES (@GOUserId, 'Administrator')
GO
-- ----------------------------------------------------------------------------------------------------------------
-- Add special group for guest (anonymous) access
-- ----------------------------------------------------------------------------------------------------------------
INSERT INTO GOSecurity.GOGroup(IsSpecialGroup, SpecialGroup, Name, DisplayName, Description) VALUES(1, 1, 'AnonymousUsers', 'Anonymous Users', NULL)
GO
-- ----------------------------------------------------------------------------------------------------------------
-- Assign Anonymous Users the Guest role
-- ----------------------------------------------------------------------------------------------------------------
INSERT INTO GOSecurity.GOGroupRole(GORoleName,GOGroupName) VALUES('Guest', 'AnonymousUsers')
GO
-- ----------------------------------------------------------------------------------------------------------------
-- LiveUpdate Model Sync
-- ----------------------------------------------------------------------------------------------------------------
INSERT INTO [GO.LiveUpdate].[ModelSync] ([Id], [ModelRevisionId], [When]) VALUES ('AF3DF4FF-A05A-4969-9796-FAC22A6ED2AF', 3954, GETUTCDATE())
GO
-- ----------------------------------------------------------------------------------------------------------------
