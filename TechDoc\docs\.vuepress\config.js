module.exports = {
    title: 'XQ360 Documentation',
    description: 'Technical documentation for XQ360 Fleet Management System',

    head: [
        ['script', { src: 'https://cdn.jsdelivr.net/npm/mermaid@11.9.0/dist/mermaid.min.js' }],
        ['script', { src: 'https://cdn.jsdelivr.net/npm/@panzoom/panzoom@4.5.1/dist/panzoom.min.js', async: true }],
        ['script', {}, `
            // Load Panzoom dynamically with multiple fallback options
            async function loadPanzoom() {
                // First, check if Panzoom is already loaded
                if (typeof window.Panzoom !== 'undefined') {
                    console.log('Panzoom already available');
                    return true;
                }

                const cdnUrls = [
                    'https://cdn.jsdelivr.net/npm/@panzoom/panzoom@4.5.1/dist/panzoom.min.js',
                    'https://unpkg.com/@panzoom/panzoom@4.5.1/dist/panzoom.min.js',
                    'https://cdn.skypack.dev/@panzoom/panzoom@4.5.1'
                ];

                for (const url of cdnUrls) {
                    try {
                        console.log('Attempting to load Panzoom from:', url);
                        await new Promise((resolve, reject) => {
                            const script = document.createElement('script');
                            script.src = url;
                            script.onload = () => {
                                console.log('Successfully loaded Panzoom from:', url);
                                resolve();
                            };
                            script.onerror = () => {
                                console.warn('Failed to load Panzoom from:', url);
                                reject(new Error('Script load failed'));
                            };
                            // Set a timeout for the script load
                            setTimeout(() => {
                                reject(new Error('Script load timeout'));
                            }, 5000);
                            document.head.appendChild(script);
                        });

                        // Verify Panzoom is actually available
                        if (typeof window.Panzoom !== 'undefined') {
                            return true;
                        }
                    } catch (error) {
                        console.warn('Error loading from', url, ':', error.message);
                        continue;
                    }
                }

                console.error('Failed to load Panzoom from all CDN sources');
                return false;
            }

            // Wait for all libraries with timeout and error handling
            function waitForLibraries(timeoutMs = 10000) {
                return new Promise((resolve, reject) => {
                    const startTime = Date.now();
                    let panzoomLoaded = false;

                    const checkLibraries = async () => {
                        const elapsed = Date.now() - startTime;

                        // Check for timeout
                        if (elapsed > timeoutMs) {
                            console.error('Timeout waiting for libraries after', elapsed, 'ms');
                            reject(new Error('Library loading timeout'));
                            return;
                        }

                        // Check if Mermaid is loaded
                        const mermaidReady = typeof window.mermaid !== 'undefined';

                        // Try to load Panzoom if not already attempted
                        if (!panzoomLoaded) {
                            panzoomLoaded = true;
                            await loadPanzoom();
                        }

                        // Check if Panzoom is loaded
                        const panzoomReady = typeof window.Panzoom !== 'undefined';

                        console.log('Library status - Mermaid:', mermaidReady ? 'loaded' : 'waiting', 'Panzoom:', panzoomReady ? 'loaded' : 'waiting');

                        if (mermaidReady && panzoomReady) {
                            console.log('Both Mermaid and Panzoom libraries loaded successfully');
                            resolve();
                        } else {
                            setTimeout(checkLibraries, 200);
                        }
                    };

                    checkLibraries();
                });
            }

            // Initialize Mermaid and interactive functionality
            async function initializeMermaidAndInteractivity() {
                try {
                    console.log('Starting library loading...');
                    await waitForLibraries();
                    console.log('Libraries loaded successfully, starting Mermaid and interactive initialization...');

                    // Initialize Mermaid first
                    window.mermaid.initialize({
                        startOnLoad: false,
                        theme: 'default',
                        flowchart: {
                            useMaxWidth: true,
                            htmlLabels: true
                        },
                        themeVariables: {
                            primaryColor: '#1976d2',
                            primaryTextColor: '#1976d2',
                            primaryBorderColor: '#1976d2',
                            lineColor: '#2e7d32',
                            secondaryColor: '#f50057',
                            tertiaryColor: '#ff6f00'
                        }
                    });

                    // Render all Mermaid diagrams first
                    await renderAllMermaidDiagrams();

                    // Then add interactivity if Panzoom is available
                    if (typeof window.Panzoom !== 'undefined') {
                        setTimeout(async () => {
                            console.log('Adding interactivity after delay...');
                            await addInteractivityToExistingDiagrams();
                            setupMutationObserver();
                        }, 1500);
                    } else {
                        console.warn('Panzoom not available, skipping interactive features');
                        // Still set up mutation observer for basic diagram rendering
                        setupMutationObserver();
                    }
                } catch (error) {
                    console.error('Failed to initialize Mermaid and interactivity:', error);

                    // Fallback: try to render diagrams without interactivity
                    try {
                        if (typeof window.mermaid !== 'undefined') {
                            console.log('Attempting fallback Mermaid initialization...');
                            window.mermaid.initialize({
                                startOnLoad: false,
                                theme: 'default'
                            });
                            await renderAllMermaidDiagrams();
                            setupMutationObserver();
                        }
                    } catch (fallbackError) {
                        console.error('Fallback initialization also failed:', fallbackError);
                    }
                }
            }

            async function renderAllMermaidDiagrams() {
                console.log('Starting Mermaid diagram rendering...');
                const mermaidElements = document.querySelectorAll('.mermaid:not([data-processed])');
                console.log('Found mermaid elements to render:', mermaidElements.length);

                for (let i = 0; i < mermaidElements.length; i++) {
                    const element = mermaidElements[i];
                    const mermaidContent = element.textContent.trim();

                    if (mermaidContent) {
                        try {
                            console.log('Rendering mermaid diagram', i + 1, 'of', mermaidElements.length);
                            const id = 'mermaid-' + Math.random().toString(36).substr(2, 9);
                            const { svg } = await window.mermaid.render(id, mermaidContent);
                            element.innerHTML = svg;
                            element.setAttribute('data-processed', 'true');
                            console.log('Successfully rendered diagram', i + 1);
                        } catch (error) {
                            console.error('Error rendering mermaid diagram', i + 1, ':', error);
                            element.innerHTML = '<div style="color: red; padding: 10px; border: 1px solid red;">Error rendering diagram: ' + error.message + '</div>';
                        }
                    }
                }
                console.log('Finished rendering all Mermaid diagrams');
            }

            function setupMutationObserver() {
                // Watch for new Mermaid diagrams being added
                const observer = new MutationObserver((mutations) => {
                    let shouldProcess = false;
                    mutations.forEach(mutation => {
                        if (mutation.type === 'childList') {
                            mutation.addedNodes.forEach(node => {
                                if (node.nodeType === 1) {
                                    if (node.classList?.contains('mermaid') || node.querySelector?.('.mermaid')) {
                                        shouldProcess = true;
                                    }
                                }
                            });
                        }
                    });
                    if (shouldProcess) {
                        setTimeout(async () => {
                            await renderAllMermaidDiagrams();
                            await addInteractivityToExistingDiagrams();
                        }, 500);
                    }
                });
                observer.observe(document.body, { childList: true, subtree: true });
            }

            // Expose functions globally for debugging
            window.debugMermaid = {
                renderAllMermaidDiagrams,
                addInteractivityToExistingDiagrams,
                initializeMermaidAndInteractivity
            };

            // Start initialization when DOM is ready
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', initializeMermaidAndInteractivity);
            } else {
                initializeMermaidAndInteractivity();
            }

            function cleanupExistingDiagrams() {
                const existingContainers = document.querySelectorAll('.mermaid-interactive-container');
                existingContainers.forEach(container => {
                    if (container._keydownHandler) {
                        document.removeEventListener('keydown', container._keydownHandler);
                    }
                    if (container._panzoom) {
                        container._panzoom.destroy();
                    }
                });
            }

            async function addInteractivityToExistingDiagrams() {
                // Clean up any existing interactive containers first
                cleanupExistingDiagrams();

                // Find all rendered Mermaid diagrams that don't already have interactivity
                const mermaidElements = document.querySelectorAll('.mermaid:not([data-interactive])');
                console.log('Found mermaid elements to make interactive:', mermaidElements.length);

                if (mermaidElements.length === 0) {
                    console.log('No mermaid elements found to make interactive');
                    return;
                }

                // Process each diagram
                mermaidElements.forEach((element, index) => {
                    const svgElement = element.querySelector('svg');
                    if (!svgElement) {
                        console.log('No SVG found in mermaid element', index);
                        return;
                    }

                    console.log('Adding interactivity to existing diagram', index);
                    element.setAttribute('data-interactive', 'true');

                    // Create interactive wrapper
                    const interactiveContainer = document.createElement('div');
                    interactiveContainer.className = 'mermaid-interactive-container';

                    // Create controls
                    const controls = document.createElement('div');
                    controls.className = 'mermaid-controls';
                    controls.innerHTML =
                        '<button class="mermaid-btn reset-view">⌂</button>' +
                        '<button class="mermaid-btn fullscreen">⛶</button>' +
                        '<span class="mermaid-instructions">Drag to pan • Scroll to zoom • Keyboard shortcuts available</span>';

                    // Create viewport
                    const viewport = document.createElement('div');
                    viewport.className = 'mermaid-viewport';

                    // Move the SVG to the viewport
                    viewport.appendChild(svgElement.cloneNode(true));

                    // Assemble the interactive container
                    interactiveContainer.appendChild(controls);
                    interactiveContainer.appendChild(viewport);

                    // Replace the original element content
                    element.innerHTML = '';
                    element.appendChild(interactiveContainer);

                    // Add interactive functionality
                    setTimeout(() => {
                        addInteractivity(interactiveContainer, index);
                    }, 100);

                    console.log('Successfully added interactivity to diagram', index);
                });
            }

            function addInteractivity(container, index) {
                console.log('Adding interactivity to diagram', index);

                const viewport = container.querySelector('.mermaid-viewport');
                const svgElement = viewport?.querySelector('svg');
                const controls = container.querySelector('.mermaid-controls');

                console.log('Elements found:', {
                    viewport: !!viewport,
                    svgElement: !!svgElement,
                    controls: !!controls,
                    panzoom: typeof window.Panzoom
                });

                if (!viewport || !svgElement || !controls) {
                    console.error('Required elements not found for diagram', index);
                    return;
                }

                if (!window.Panzoom) {
                    console.warn('Panzoom library not available for diagram', index, '- interactive features will be limited');
                    // Still add basic controls but disable pan/zoom functionality
                    addBasicControls(controls, container);
                    return;
                }

                try {
                    // Initialize panzoom with enhanced options
                    const panzoom = window.Panzoom(svgElement, {
                        maxScale: 8,
                        minScale: 0.2,
                        increment: 0.15,
                        cursor: 'grab',
                        animate: true,
                        duration: 200,
                        easing: 'ease-in-out',
                        startTransform: { scale: 1, x: 0, y: 0 },
                        contain: 'outside'
                    });

                    console.log('Panzoom initialized successfully for diagram', index);

                    // Add mouse wheel support with smooth scrolling
                    viewport.addEventListener('wheel', (e) => {
                        e.preventDefault();
                        panzoom.zoomWithWheel(e);
                    });

                    // Add control button functionality
                    const resetBtn = controls.querySelector('.reset-view');
                    const fullscreenBtn = controls.querySelector('.fullscreen');

                    if (resetBtn) {
                        resetBtn.addEventListener('click', (e) => {
                            e.preventDefault();
                            console.log('Reset view clicked');
                            panzoom.reset();
                        });
                    }

                    if (fullscreenBtn) {
                        fullscreenBtn.addEventListener('click', (e) => {
                            e.preventDefault();
                            console.log('Fullscreen clicked');
                            toggleFullscreen(container);
                        });
                    }

                    // Add keyboard shortcuts
                    const keydownHandler = (e) => {
                        if (container.matches(':hover') || container.matches('.fullscreen')) {
                            switch(e.key) {
                                case '0':
                                    e.preventDefault();
                                    console.log('Keyboard reset');
                                    panzoom.reset();
                                    break;
                                case 'f':
                                case 'F':
                                    if (e.ctrlKey || e.metaKey) {
                                        e.preventDefault();
                                        console.log('Keyboard fullscreen');
                                        toggleFullscreen(container);
                                    }
                                    break;
                            }
                        }
                    };

                    document.addEventListener('keydown', keydownHandler);

                    // Store reference for cleanup
                    container._keydownHandler = keydownHandler;
                    container._panzoom = panzoom;

                    console.log('Interactive controls added successfully for diagram', index);

                } catch (error) {
                    console.error('Failed to add interactivity to diagram', index, ':', error);
                }
            }

            function addBasicControls(controls, container) {
                console.log('Adding basic controls without pan/zoom functionality');

                // Only enable fullscreen functionality
                const fullscreenBtn = controls.querySelector('.fullscreen');
                if (fullscreenBtn) {
                    fullscreenBtn.addEventListener('click', (e) => {
                        e.preventDefault();
                        toggleFullscreen(container);
                    });
                }

                // Disable reset button and show warning
                const resetBtn = controls.querySelector('.reset-view');
                if (resetBtn) {
                    resetBtn.disabled = true;
                    resetBtn.style.opacity = '0.5';
                    resetBtn.title = 'Pan/zoom functionality unavailable - Panzoom library not loaded';
                }

                // Update instructions
                const instructions = controls.querySelector('.mermaid-instructions');
                if (instructions) {
                    instructions.textContent = 'Fullscreen available • Pan/zoom unavailable';
                }
            }

            function toggleFullscreen(container) {
                const isFullscreen = container.classList.contains('fullscreen');

                if (isFullscreen) {
                    container.classList.remove('fullscreen');
                    document.body.style.overflow = '';
                } else {
                    container.classList.add('fullscreen');
                    document.body.style.overflow = 'hidden';
                }

                // Update fullscreen button text
                const btn = container.querySelector('.fullscreen');
                btn.textContent = isFullscreen ? '⛶' : '✕';
                btn.title = isFullscreen ? 'Toggle Fullscreen' : 'Exit Fullscreen';
            }
        `],
        ['style', {}, `
            .mermaid-diagram-container {
                margin: 10px 0;
                padding: 10px;
                background: #f8f9fa;
                border-radius: 8px;
                border: 1px solid #e9ecef;
                overflow-x: auto;
            }
            
            .mermaid-file-container {
                margin: 10px 0;
                padding: 10px;
                background: #f8f9fa;
                border-radius: 8px;
                border: 1px solid #e9ecef;
                overflow-x: auto;
            }
            
            .mermaid {
                text-align: center;
                overflow-x: auto;
                background: transparent;
            }
            
            .mermaid svg {
                max-width: 100%;
                height: auto;
            }
            
            /* Interactive Mermaid Styles */
            .mermaid-interactive-container {
                position: relative;
                border: 1px solid #e9ecef;
                border-radius: 8px;
                background: #f8f9fa;
                margin: 10px 0;
                overflow: hidden;
            }
            
            .mermaid-interactive-container.fullscreen {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                z-index: 9999;
                margin: 0;
                border-radius: 0;
                background: #fff;
            }
            
            .mermaid-controls {
                display: flex;
                align-items: center;
                gap: 8px;
                padding: 12px;
                background: #ffffff;
                border-bottom: 1px solid #e9ecef;
                box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            }
            
            .mermaid-btn {
                background: #ffffff;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 6px 12px;
                cursor: pointer;
                font-size: 14px;
                transition: all 0.2s;
                display: inline-flex;
                align-items: center;
                gap: 4px;
            }
            
            .mermaid-btn:hover {
                background: #e9ecef;
                border-color: #adb5bd;
                transform: translateY(-1px);
            }
            
            .mermaid-btn:active {
                transform: translateY(0);
                background: #dee2e6;
            }
            
            .mermaid-instructions {
                margin-left: auto;
                font-size: 12px;
                color: #6c757d;
                font-style: italic;
            }
            
            .mermaid-viewport {
                position: relative;
                height: 700px;
                overflow: hidden;
                cursor: grab;
                background: #ffffff;
            }
            
            .mermaid-viewport:active {
                cursor: grabbing;
            }
            
            .mermaid-interactive-container.fullscreen .mermaid-viewport {
                height: calc(100vh - 60px);
            }
            
            .mermaid-viewport svg {
                display: block;
                max-width: 100%;
                max-height: 100%;
                margin: auto;
                pointer-events: all;
            }
            
            /* Panzoom specific styles */
            .panzoom-element {
                cursor: grab;
            }
            
            .panzoom-element.panzoom-active {
                cursor: grabbing;
            }
            
            /* Hover effects */
            .mermaid-interactive-container:hover .mermaid-controls {
                opacity: 1;
            }
            
            /* Mobile responsive */
            @media (max-width: 768px) {
                .mermaid-controls {
                    padding: 8px;
                    flex-wrap: wrap;
                }
                
                .mermaid-btn {
                    padding: 4px 8px;
                    font-size: 12px;
                }
                
                .mermaid-instructions {
                    width: 100%;
                    margin-left: 0;
                    margin-top: 4px;
                    text-align: center;
                }
                
                .mermaid-viewport {
                    height: 600px;
                }
            }
            
            .loading {
                color: #6c757d;
                text-align: center;
            }
            
            .mermaid-loading {
                text-align: center;
                padding: 40px;
                color: #6c757d;
                font-style: italic;
                background: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 8px;
                animation: pulse 2s infinite;
            }
            
            @keyframes pulse {
                0%, 100% { opacity: 1; }
                50% { opacity: 0.7; }
            }
            
            /* Smooth transitions */
            .mermaid-interactive-container {
                transition: all 0.3s ease;
            }
            
            .mermaid-viewport svg {
                transition: opacity 0.2s ease;
            }
            
            /* Visual feedback for interactions */
            .mermaid-viewport.panning {
                cursor: grabbing !important;
            }
            



        `]
    ],

    plugins: [
        [require('./plugins/mermaid-file-plugin')]
    ],

    themeConfig: {
        logo: '/images/logo.png',
        nav: [
            {
                text: 'Home',
                link: '/',
            },
            {
                text: 'Getting Started',
                link: '/guides/',
            },
            {
                text: 'Features',
                items: [
                    {
                        text: 'Core Features',
                        link: '/features/',
                    },
                    {
                        text: 'Security',
                        link: '/features/security/',
                    },
                    {
                        text: 'Internationalization',
                        link: '/features/internationalization/',
                    },
                ],
            },
            {
                text: 'Architecture',
                link: '/architecture/',
            },
            {
                text: 'API Reference',
                link: '/reference/',
            },
            {
                text: 'Development',
                items: [
                    {
                        text: 'Setup & Installation',
                        link: '/development/setup/',
                    },
                    {
                        text: 'Contributing',
                        link: '/development/contributing/',
                    },
                    {
                        text: 'Testing',
                        link: '/development/testing/',
                    },
                ],
            },
        ],

        sidebar: {
            '/guides/': [
                {
                    title: 'Getting Started',
                    children: [
                        '',
                        'installation',
                        'quick-start',
                        'configuration'
                    ]
                }
            ],
            '/features/': [
                {
                    title: 'Core Features',
                    children: [
                        '',
                        'vehicle-management',
                        'driver-management',
                        'reporting'
                    ]
                },
                {
                    title: 'Security',
                    children: [
                        'security/',
                        'security/authentication',
                        'security/authorization'
                    ]
                }
            ],
            '/architecture/': [
                {
                    title: 'System Architecture',
                    children: [
                        '',
                        'overview',
                        'database',
                        'api-design',
                        'mermaid-test',
                        'mermaid-test-simple'
                    ]
                }
            ],
            '/reference/': [
                {
                    title: 'API Reference',
                    children: [
                        '',
                        'rest-api',
                        'webhooks',
                        'error-codes'
                    ]
                }
            ],
            '/development/': [
                {
                    title: 'Development Guide',
                    children: [
                        '',
                        'setup',
                        'contributing',
                        'testing'
                    ]
                }
            ]
        },

        // Enable edit links (optional - configure based on your repository)
        editLinks: false,
        lastUpdated: 'Last Updated',

        // Footer
        footer: 'XQ360 Fleet Management System Documentation'
    }
} 