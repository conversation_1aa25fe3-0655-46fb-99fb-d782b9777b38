using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using GenerativeObjects.Practices.ORMSupportClasses;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FleetXQ.BusinessLayer.Components.Server
{
    /// <summary>
    /// Custom interface extension for IModuleUtilities to add performance optimization methods
    /// </summary>
    public partial interface IModuleUtilities
    {
        /// <summary>
        /// GetAvailableModulesWithPagination - New method for large datasets
        /// Implements pagination support for better performance with large module datasets
        /// </summary>
        /// <param name="dealerId">The dealer ID to filter modules by</param>
        /// <param name="pageNumber">Page number for pagination (1-based)</param>
        /// <param name="pageSize">Number of items per page</param>
        /// <param name="searchTerm">Optional search term to filter by IoTDevice or CCID</param>
        /// <param name="parameters">Additional parameters</param>
        /// <returns>Paginated collection of available modules</returns>
        Task<ComponentResponse<DataObjectCollection<ModuleDataObject>>> GetAvailableModulesWithPaginationAsync(
            Guid dealerId, 
            int pageNumber = 1, 
            int pageSize = 50, 
            string searchTerm = null,
            Dictionary<string, object> parameters = null);

        /// <summary>
        /// Bulk load vehicle-module relationships for efficient processing
        /// This method optimizes data transfer between layers
        /// </summary>
        /// <param name="dealerId">Optional dealer ID to filter relationships</param>
        /// <returns>Dictionary mapping vehicle IDs to module IDs</returns>
        Task<Dictionary<Guid, Guid>> GetVehicleModuleRelationshipsAsync(Guid? dealerId = null);

        /// <summary>
        /// Invalidate cache when module status changes
        /// This ensures cache consistency when modules are assigned/unassigned
        /// </summary>
        /// <param name="dealerId">Optional dealer ID to invalidate specific cache</param>
        void InvalidateAvailableModulesCache(Guid? dealerId = null);
    }
}
