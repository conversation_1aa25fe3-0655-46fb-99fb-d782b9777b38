# VehicleAccessProcessorFunction Tests

This directory contains comprehensive unit tests for the `VehicleAccessProcessorFunction` Azure Function.

## Test Coverage

The test suite covers:

### Health Check Tests
- **HealthCheck_WhenServiceIsHealthy_ShouldCallTestConnectionAsync**: Verifies the health check calls the service and returns success when healthy
- **HealthCheck_WhenServiceIsUnhealthy_ShouldCallTestConnectionAsync**: Verifies the health check calls the service and returns failure when unhealthy

### Message Processing Tests
- **ProcessVehicleAccessCreationMessage_WithValidMessage_ShouldProcessSuccessfully**: Tests successful message processing
- **ProcessVehicleAccessCreationMessage_WithInvalidMessageFormat_ShouldDeadLetterMessage**: Tests handling of invalid JSON messages
- **ProcessVehicleAccessCreationMessage_WithNullMessage_ShouldDeadLetterMessage**: Tests handling of null messages
- **ProcessVehicleAccessCreationMessage_WithFailedProcessingAndRetriesRemaining_ShouldAbandonMessage**: Tests retry logic when processing fails
- **ProcessVehicleAccessCreationMessage_WithFailedProcessingAndMaxRetriesExceeded_ShouldDeadLetterMessage**: Tests dead lettering after max retries
- **ProcessVehicleAccessCreationMessage_WithExceptionAndRetriesRemaining_ShouldAbandonMessage**: Tests exception handling with retries
- **ProcessVehicleAccessCreationMessage_WithExceptionAndMaxRetriesExceeded_ShouldDeadLetterMessage**: Tests exception handling after max retries
- **ProcessVehicleAccessCreationMessage_WithExceptionAndNullMessage_ShouldDeadLetterMessage**: Tests exception handling with invalid messages
- **ProcessVehicleAccessCreationMessage_WithExceptionInMessageHandling_ShouldLogError**: Tests error handling in message processing

## Test Framework

- **Testing Framework**: NUnit 4.1.0
- **Mocking Framework**: NSubstitute 5.1.0
- **Assertions**: NFluent 3.0.3

## Architecture

The tests use:
- **Interface-based mocking**: `IVehicleAccessCreationService` interface for clean separation
- **Dependency injection**: Tests inject mocked dependencies
- **Azure Functions testing**: Mock Azure Service Bus components
- **Comprehensive scenarios**: Cover success, failure, retry, and error cases

## Running Tests

```bash
# From the tests directory
dotnet test

# With verbose output
dotnet test --verbosity normal

# Generate coverage report
dotnet test --collect:"XPlat Code Coverage"
```

## Test Data

The tests use:
- Mock Service Bus messages with various payloads
- Test `VehicleAccessCreationMessage` objects with realistic data
- Configurable retry counts and error scenarios

## Key Testing Patterns

1. **Arrange-Act-Assert**: Clear test structure
2. **Mock isolation**: Each test has isolated mocks
3. **Behavior verification**: Tests verify method calls and interactions
4. **Error simulation**: Tests simulate various failure scenarios
5. **Retry logic testing**: Comprehensive retry mechanism validation

## Notes

- Health check tests focus on service interaction rather than HTTP infrastructure due to mocking complexities
- Message processing tests verify the core business logic and error handling
- All tests pass with comprehensive coverage of the Azure Function behavior 