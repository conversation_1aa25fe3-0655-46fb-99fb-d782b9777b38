using System;
using System.Collections.Generic;

namespace FleetXQFunctionService.Messages
{
    /// <summary>
    /// Message class for vehicle synchronization operations sent to Azure Service Bus
    /// This must match the structure used in CustomCode/DataLayer/VehicleSyncMessage.cs
    /// </summary>
    public class VehicleSyncMessage
    {
        /// <summary>
        /// The vehicle ID to be synchronized
        /// </summary>
        public Guid VehicleId { get; set; }

        /// <summary>
        /// User ID that initiated this sync operation
        /// </summary>
        public Guid? InitiatedByUserId { get; set; }

        /// <summary>
        /// Person ID for session-based processing
        /// </summary>
        public Guid PersonId { get; set; }

        /// <summary>
        /// Customer ID for multi-tenant isolation
        /// </summary>
        public Guid CustomerId { get; set; }

        /// <summary>
        /// When the sync message was created
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Correlation ID for tracking related operations
        /// </summary>
        public string CorrelationId { get; set; }

        /// <summary>
        /// Message priority (Normal, High, etc.)
        /// </summary>
        public string Priority { get; set; } = "Normal";

        /// <summary>
        /// The sync operation reason/trigger
        /// </summary>
        public string SyncReason { get; set; } = string.Empty;

        /// <summary>
        /// Vehicle sequence number for batch processing
        /// </summary>
        public int VehicleSequence { get; set; }

        /// <summary>
        /// Total vehicles in the sync batch
        /// </summary>
        public int TotalVehicles { get; set; }

        /// <summary>
        /// Permission levels that were updated and should be processed (Master=1, NormalDriver=3)
        /// If null or empty, defaults to both levels for backward compatibility
        /// </summary>
        public List<int> PermissionLevels { get; set; }
    }
}