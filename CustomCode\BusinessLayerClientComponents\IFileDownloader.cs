﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using GenerativeObjects.Practices.Commands;

namespace FleetXQ.BusinessLayer.Components.Client
{
    /// <summary>
	/// FileDownloader Component
	///  
	/// </summary>
	public interface IFileDownloader : IModelBase 
    {
		/// <summary>
		/// DownloadFile Method
		///  
      /// </summary>
		/// <param name="fileName"></param>
        /// <returns></returns>		
		/// <param name="fileUrl"></param>
        /// <returns></returns>		
		System.Threading.Tasks.Task<ComponentResponse<System.Boolean>> DownloadFileAsync(System.String fileName, System.String fileUrl, Dictionary<string, object> parameters = null);
		
	}
}
