﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net7.0</TargetFramework>
    <GeneratePackageOnBuild>True</GeneratePackageOnBuild>
  </PropertyGroup>
  <PropertyGroup>
    <VersionPrefix>*********</VersionPrefix>
    <VersionSuffix></VersionSuffix>
    <Authors>Generative Objects</Authors>
    <Description>FleetXQ Custom Data Providers</Description>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="GenerativeObjects.Practices.LayerSupportClasses" Version="2.0.3" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Features" Version="5.0.17" />
    <PackageReference Include="Microsoft.CSharp" Version="4.7.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="7.0.0" />
    <PackageReference Include="System.Data.DataSetExtensions" Version="4.5.0" />
    <PackageReference Include="System.IO.Pipelines" Version="7.0.0" />
    <PackageReference Include="System.Text.Encodings.Web" Version="7.0.0" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\GeneratedCode\DataLayer\FleetXQ.Data.DataObjects.csproj" />
  </ItemGroup>
</Project>

